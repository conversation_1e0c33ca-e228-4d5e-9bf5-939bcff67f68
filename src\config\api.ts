// API Configuration for Backend
const getApiBaseUrl = (): string => {
  const backendUrl = import.meta.env.VITE_BACKEND_URL || import.meta.env.VITE_API_URL;

  if (backendUrl) {
    // Ensure the URL ends with /api/v1 for consistent versioning
    const cleanUrl = backendUrl.replace(/\/+$/, ''); // Remove trailing slashes
    return cleanUrl.endsWith('/api') ? `${cleanUrl}/v1` :
           cleanUrl.endsWith('/api/v1') ? cleanUrl : `${cleanUrl}/api/v1`;
  }

  // Fallback to localhost for development
  return 'http://localhost:3000/api/v1';
};

export const API_CONFIG = {
  baseURL: getApiBaseUrl(),
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT || '30000'),
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  retryAttempts: parseInt(import.meta.env.VITE_API_RETRY_ATTEMPTS || '3'),
  retryDelay: parseInt(import.meta.env.VITE_API_RETRY_DELAY || '1000'),
};

export const PAYSTACK_CONFIG = {
  publicKey: import.meta.env.VITE_PAYSTACK_PUBLIC_KEY || 'pk_test_44f6dbb6159b73b84fff2fc441dcdd997e15c10d',
  baseURL: import.meta.env.VITE_PAYSTACK_BASE_URL || 'https://api.paystack.co',
  secretKey: import.meta.env.VITE_PAYSTACK_SECRET_KEY, // For backend use only
};

export const APP_CONFIG = {
  name: import.meta.env.VITE_APP_NAME || 'Better Interest',
  version: import.meta.env.VITE_APP_VERSION || '1.0.0',
  environment: import.meta.env.VITE_APP_ENVIRONMENT || 'development',
  debug: import.meta.env.VITE_DEBUG === 'true',
  logLevel: import.meta.env.VITE_LOG_LEVEL || 'info',
  demoMode: import.meta.env.VITE_DEMO_MODE === 'true', // Disabled by default
  features: {
    fixedDeposits: import.meta.env.VITE_FEATURE_FIXED_DEPOSITS !== 'false',
    investments: import.meta.env.VITE_FEATURE_INVESTMENTS !== 'false',
    autoSave: import.meta.env.VITE_FEATURE_AUTOSAVE !== 'false',
    referrals: import.meta.env.VITE_FEATURE_REFERRALS !== 'false',
    loans: import.meta.env.VITE_FEATURE_LOANS !== 'false',
  }
};

export const EXTERNAL_APIS = {
  nibss: {
    baseURL: import.meta.env.VITE_NIBSS_BASE_URL || 'https://api.nibss-plc.com.ng',
    institutionCode: import.meta.env.VITE_NIBSS_INSTITUTION_CODE,
    apiKey: import.meta.env.VITE_NIBSS_API_KEY,
  },
  flutterwave: {
    publicKey: import.meta.env.VITE_FLUTTERWAVE_PUBLIC_KEY,
    baseURL: import.meta.env.VITE_FLUTTERWAVE_BASE_URL || 'https://api.flutterwave.com/v3',
  },
  monnify: {
    apiKey: import.meta.env.VITE_MONNIFY_API_KEY,
    contractCode: import.meta.env.VITE_MONNIFY_CONTRACT_CODE,
    baseURL: import.meta.env.VITE_MONNIFY_BASE_URL || 'https://api.monnify.com/api/v1',
  }
};
