// API Configuration for Backend
export const API_CONFIG = {
  baseURL: 'http://localhost:3000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
};

export const PAYSTACK_CONFIG = {
  publicKey: import.meta.env.VITE_PAYSTACK_PUBLIC_KEY || 'pk_test_44f6dbb6159b73b84fff2fc441dcdd997e15c10d',
  baseURL: import.meta.env.VITE_PAYSTACK_BASE_URL || 'https://api.paystack.co',
};

export const APP_CONFIG = {
  name: import.meta.env.VITE_APP_NAME || 'Better Interest',
  version: import.meta.env.VITE_APP_VERSION || '1.0.0',
  environment: import.meta.env.VITE_APP_ENVIRONMENT || 'development',
  debug: import.meta.env.VITE_DEBUG === 'true' || false,
  logLevel: import.meta.env.VITE_LOG_LEVEL || 'info',
  demoMode: import.meta.env.VITE_DEMO_MODE === 'true' || true, // Enable demo mode by default
};