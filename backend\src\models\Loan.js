const mongoose = require('mongoose');

const LoanSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  // Loan details
  amount: {
    type: Number,
    required: true,
    min: [5000, 'Minimum loan amount is ₦5,000'],
    max: [500000, 'Maximum loan amount is ₦500,000']
  },
  interestRate: {
    type: Number,
    required: true,
    min: [5, 'Minimum interest rate is 5%'],
    max: [30, 'Maximum interest rate is 30%']
  },
  duration: {
    type: Number,
    required: true,
    min: [30, 'Minimum loan duration is 30 days'],
    max: [365, 'Maximum loan duration is 365 days']
  },
  purpose: {
    type: String,
    enum: ['emergency', 'business', 'education', 'medical', 'personal', 'other'],
    required: true
  },
  // Status tracking
  status: {
    type: String,
    enum: ['pending', 'approved', 'disbursed', 'active', 'completed', 'defaulted', 'rejected'],
    default: 'pending',
    index: true
  },
  // Financial calculations
  totalAmount: {
    type: Number // Principal + Interest
  },
  monthlyPayment: {
    type: Number
  },
  remainingBalance: {
    type: Number
  },
  totalPaid: {
    type: Number,
    default: 0
  },
  // Important dates
  applicationDate: {
    type: Date,
    default: Date.now
  },
  approvalDate: {
    type: Date
  },
  disbursementDate: {
    type: Date
  },
  dueDate: {
    type: Date
  },
  completionDate: {
    type: Date
  },
  // Eligibility and scoring
  creditScore: {
    type: Number,
    min: 300,
    max: 850
  },
  eligibilityScore: {
    type: Number,
    min: 0,
    max: 100
  },
  riskLevel: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  // Collateral (savings-backed loans)
  collateral: {
    type: {
      type: String,
      enum: ['savings', 'fixed_deposit', 'investment'],
    },
    referenceId: {
      type: mongoose.Schema.Types.ObjectId
    },
    amount: Number,
    percentage: {
      type: Number,
      min: 50,
      max: 90,
      default: 80 // 80% of collateral value
    }
  },
  // Repayment tracking
  repayments: [{
    amount: {
      type: Number,
      required: true
    },
    date: {
      type: Date,
      default: Date.now
    },
    method: {
      type: String,
      enum: ['auto_debit', 'manual', 'wallet'],
      default: 'wallet'
    },
    reference: String,
    status: {
      type: String,
      enum: ['successful', 'failed', 'pending'],
      default: 'successful'
    }
  }],
  // Auto-repayment settings
  autoRepayment: {
    enabled: {
      type: Boolean,
      default: false
    },
    cardId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Card'
    },
    frequency: {
      type: String,
      enum: ['weekly', 'bi_weekly', 'monthly'],
      default: 'monthly'
    },
    nextPaymentDate: Date
  },
  // Application details
  application: {
    employmentStatus: {
      type: String,
      enum: ['employed', 'self_employed', 'unemployed', 'student'],
      required: true
    },
    monthlyIncome: {
      type: Number,
      required: true,
      min: 0
    },
    monthlyExpenses: {
      type: Number,
      required: true,
      min: 0
    },
    bankStatement: {
      uploaded: Boolean,
      fileUrl: String,
      verified: Boolean
    },
    guarantor: {
      name: String,
      phone: String,
      email: String,
      relationship: String,
      verified: Boolean
    }
  },
  // Approval/Rejection details
  review: {
    reviewedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    reviewDate: Date,
    comments: String,
    rejectionReason: String
  },
  // Metadata
  metadata: {
    ipAddress: String,
    userAgent: String,
    applicationSource: {
      type: String,
      enum: ['web', 'mobile', 'api'],
      default: 'web'
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for days overdue
LoanSchema.virtual('daysOverdue').get(function() {
  if (this.status !== 'active' || !this.dueDate) return 0;
  
  const now = new Date();
  const due = new Date(this.dueDate);
  
  if (now <= due) return 0;
  
  const diffTime = now - due;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Virtual for next payment amount
LoanSchema.virtual('nextPaymentAmount').get(function() {
  if (this.status !== 'active') return 0;
  return Math.min(this.monthlyPayment, this.remainingBalance);
});

// Virtual for loan progress
LoanSchema.virtual('repaymentProgress').get(function() {
  if (!this.totalAmount || this.totalAmount === 0) return 0;
  return (this.totalPaid / this.totalAmount) * 100;
});

// Pre-save middleware
LoanSchema.pre('save', function(next) {
  // Calculate total amount and monthly payment
  if (this.isNew || this.isModified('amount') || this.isModified('interestRate') || this.isModified('duration')) {
    this.calculateLoanTerms();
  }
  
  // Set due date when loan is disbursed
  if (this.isModified('status') && this.status === 'disbursed' && !this.dueDate) {
    this.dueDate = new Date(Date.now() + this.duration * 24 * 60 * 60 * 1000);
    this.disbursementDate = new Date();
    this.remainingBalance = this.totalAmount;
  }
  
  next();
});

// Indexes
LoanSchema.index({ userId: 1, status: 1 });
LoanSchema.index({ status: 1, dueDate: 1 });
LoanSchema.index({ applicationDate: -1 });
LoanSchema.index({ 'collateral.referenceId': 1 });

// Static methods
LoanSchema.statics.getUserLoans = function(userId, status = null) {
  const query = { userId };
  if (status) query.status = status;
  return this.find(query).sort({ applicationDate: -1 });
};

LoanSchema.statics.getOverdueLoans = function() {
  return this.find({
    status: 'active',
    dueDate: { $lt: new Date() }
  }).populate('userId', 'firstName lastName email phone');
};

LoanSchema.statics.calculateEligibility = async function(userId) {
  const User = mongoose.model('User');
  const SavingsPlan = mongoose.model('SavingsPlan');
  const FixedDeposit = mongoose.model('FixedDeposit');
  
  const user = await User.findById(userId);
  if (!user) return { eligible: false, reason: 'User not found' };
  
  // Check basic requirements
  if (!user.isVerified || user.kycStatus !== 'verified') {
    return { eligible: false, reason: 'KYC verification required' };
  }
  
  // Check account age (minimum 30 days)
  const accountAge = (Date.now() - user.createdAt) / (1000 * 60 * 60 * 24);
  if (accountAge < 30) {
    return { eligible: false, reason: 'Account must be at least 30 days old' };
  }
  
  // Check savings history
  const totalSavings = await SavingsPlan.aggregate([
    { $match: { userId: mongoose.Types.ObjectId(userId) } },
    { $group: { _id: null, total: { $sum: '$currentAmount' } } }
  ]);
  
  const savingsAmount = totalSavings.length > 0 ? totalSavings[0].total : 0;
  
  // Check fixed deposits
  const fixedDeposits = await FixedDeposit.find({ userId, status: 'active' });
  const totalFixedDeposits = fixedDeposits.reduce((sum, fd) => sum + fd.amount, 0);
  
  const totalCollateral = savingsAmount + totalFixedDeposits;
  
  if (totalCollateral < 10000) {
    return { 
      eligible: false, 
      reason: 'Minimum ₦10,000 in savings or fixed deposits required' 
    };
  }
  
  // Check existing loans
  const activeLoans = await this.countDocuments({ userId, status: { $in: ['active', 'disbursed'] } });
  if (activeLoans > 0) {
    return { eligible: false, reason: 'You have an active loan' };
  }
  
  // Calculate maximum loan amount (80% of collateral)
  const maxLoanAmount = Math.floor(totalCollateral * 0.8);
  
  return {
    eligible: true,
    maxAmount: Math.min(maxLoanAmount, 500000), // Cap at ₦500,000
    collateralAmount: totalCollateral,
    creditScore: user.creditScore || 650,
    recommendedAmount: Math.min(maxLoanAmount * 0.6, 200000) // Conservative recommendation
  };
};

// Instance methods
LoanSchema.methods.calculateLoanTerms = function() {
  const principal = this.amount;
  const rate = this.interestRate / 100;
  const duration = this.duration;
  
  // Simple interest calculation for short-term loans
  const interest = principal * rate * (duration / 365);
  this.totalAmount = Math.round(principal + interest);
  
  // Calculate monthly payment (assuming monthly payments)
  const months = Math.ceil(duration / 30);
  this.monthlyPayment = Math.round(this.totalAmount / months);
};

LoanSchema.methods.approve = function(reviewerId, comments = '') {
  this.status = 'approved';
  this.approvalDate = new Date();
  this.review.reviewedBy = reviewerId;
  this.review.reviewDate = new Date();
  this.review.comments = comments;
  
  return this.save();
};

LoanSchema.methods.reject = function(reviewerId, reason) {
  this.status = 'rejected';
  this.review.reviewedBy = reviewerId;
  this.review.reviewDate = new Date();
  this.review.rejectionReason = reason;
  
  return this.save();
};

LoanSchema.methods.disburse = async function() {
  if (this.status !== 'approved') {
    throw new Error('Loan must be approved before disbursement');
  }
  
  const User = mongoose.model('User');
  const user = await User.findById(this.userId);
  
  if (!user) {
    throw new Error('User not found');
  }
  
  // Credit user account
  user.balance += this.amount;
  await user.save();
  
  // Update loan status
  this.status = 'disbursed';
  this.disbursementDate = new Date();
  this.remainingBalance = this.totalAmount;
  
  // Set due date
  this.dueDate = new Date(Date.now() + this.duration * 24 * 60 * 60 * 1000);
  
  await this.save();
  
  return this;
};

LoanSchema.methods.makeRepayment = function(amount, method = 'wallet', reference = '') {
  if (this.status !== 'active' && this.status !== 'disbursed') {
    throw new Error('Loan is not active');
  }
  
  const repayment = {
    amount,
    method,
    reference,
    status: 'successful'
  };
  
  this.repayments.push(repayment);
  this.totalPaid += amount;
  this.remainingBalance = Math.max(0, this.remainingBalance - amount);
  
  // Check if loan is fully paid
  if (this.remainingBalance <= 0) {
    this.status = 'completed';
    this.completionDate = new Date();
  } else {
    this.status = 'active';
  }
  
  return this.save();
};

LoanSchema.methods.markDefault = function() {
  this.status = 'defaulted';
  return this.save();
};

module.exports = mongoose.model('Loan', LoanSchema);
