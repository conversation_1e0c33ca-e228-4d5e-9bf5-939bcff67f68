# ===========================================
# BETTER INTEREST - NETLIFY DEPLOYMENT SCRIPT (PowerShell)
# ===========================================

param(
    [Parameter(Position=0)]
    [ValidateSet("production", "prod", "preview", "staging", "help")]
    [string]$DeployType = "preview",
    
    [Parameter(Position=1)]
    [switch]$SkipTests
)

# Configuration
$SITE_NAME = "better-interest-demo"
$PRODUCTION_DOMAIN = "demo.kojaonline.store"
$API_DOMAIN = "api.kojaonline.store"

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    White = "White"
}

# Functions
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Colors.Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Colors.Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Colors.Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Colors.Red
}

# Check if required tools are installed
function Test-Dependencies {
    Write-Info "Checking dependencies..."
    
    # Check Node.js
    try {
        $nodeVersion = node --version
        Write-Info "Node.js version: $nodeVersion"
    }
    catch {
        Write-Error "Node.js is not installed. Please install Node.js 16 or higher."
        exit 1
    }
    
    # Check npm
    try {
        $npmVersion = npm --version
        Write-Info "npm version: $npmVersion"
    }
    catch {
        Write-Error "npm is not installed. Please install npm."
        exit 1
    }
    
    # Check Netlify CLI
    try {
        $netlifyVersion = netlify --version
        Write-Info "Netlify CLI version: $netlifyVersion"
    }
    catch {
        Write-Warning "Netlify CLI is not installed. Installing..."
        npm install -g netlify-cli
    }
    
    Write-Success "All dependencies are available."
}

# Validate environment variables
function Test-Environment {
    Write-Info "Validating environment variables..."
    
    if (-not (Test-Path ".env")) {
        Write-Error ".env file not found. Please create it from .env.example"
        exit 1
    }
    
    # Read .env file
    $envContent = Get-Content ".env" | Where-Object { $_ -match "^[^#].*=" }
    $envVars = @{}
    
    foreach ($line in $envContent) {
        $parts = $line -split "=", 2
        if ($parts.Length -eq 2) {
            $envVars[$parts[0].Trim()] = $parts[1].Trim()
        }
    }
    
    if (-not $envVars.ContainsKey("VITE_API_URL")) {
        Write-Error "VITE_API_URL is not set in .env file"
        exit 1
    }
    
    if (-not $envVars.ContainsKey("VITE_PAYSTACK_PUBLIC_KEY")) {
        Write-Warning "VITE_PAYSTACK_PUBLIC_KEY is not set in .env file"
    }
    
    Write-Success "Environment validation completed."
}

# Install dependencies
function Install-Dependencies {
    Write-Info "Installing dependencies..."
    
    try {
        npm ci --silent
        Write-Success "Dependencies installed successfully."
    }
    catch {
        Write-Error "Failed to install dependencies: $_"
        exit 1
    }
}

# Run tests
function Invoke-Tests {
    Write-Info "Running tests..."
    
    # Type checking
    try {
        $tscPath = Get-Command tsc -ErrorAction SilentlyContinue
        if ($tscPath) {
            Write-Info "Running TypeScript type checking..."
            npx tsc --noEmit
            Write-Success "TypeScript type checking passed."
        }
    }
    catch {
        Write-Warning "TypeScript type checking failed: $_"
    }
    
    # Linting
    try {
        $packageJson = Get-Content "package.json" | ConvertFrom-Json
        if ($packageJson.scripts.lint) {
            Write-Info "Running ESLint..."
            npm run lint
            Write-Success "Linting passed."
        }
    }
    catch {
        Write-Warning "Linting failed: $_"
    }
    
    Write-Success "All tests completed successfully."
}

# Build the application
function Build-Application {
    Write-Info "Building application for production..."
    
    # Clean previous build
    if (Test-Path "dist") {
        Remove-Item -Recurse -Force "dist"
        Write-Info "Cleaned previous build directory."
    }
    
    # Build
    try {
        npm run build
    }
    catch {
        Write-Error "Build failed: $_"
        exit 1
    }
    
    # Verify build output
    if (-not (Test-Path "dist")) {
        Write-Error "Build failed - dist directory not found."
        exit 1
    }
    
    if (-not (Test-Path "dist/index.html")) {
        Write-Error "Build failed - index.html not found in dist directory."
        exit 1
    }
    
    # Check build size
    $buildSize = (Get-ChildItem -Recurse "dist" | Measure-Object -Property Length -Sum).Sum / 1MB
    Write-Info "Build size: $([math]::Round($buildSize, 2)) MB"
    
    Write-Success "Application built successfully."
}

# Deploy to Netlify
function Deploy-ToNetlify {
    param([string]$DeployType)
    
    Write-Info "Deploying to Netlify ($DeployType)..."
    
    # Login check
    try {
        netlify status | Out-Null
    }
    catch {
        Write-Info "Please login to Netlify..."
        netlify login
    }
    
    # Deploy
    if ($DeployType -eq "production") {
        Write-Info "Deploying to production..."
        
        try {
            netlify deploy --prod --dir=dist --site=$SITE_NAME
            
            # Verify deployment
            Write-Info "Verifying production deployment..."
            Start-Sleep -Seconds 10
            
            $response = Invoke-WebRequest -Uri "https://$PRODUCTION_DOMAIN" -Method Head -TimeoutSec 30
            if ($response.StatusCode -eq 200) {
                Write-Success "Production deployment verified successfully!"
                Write-Info "Site URL: https://$PRODUCTION_DOMAIN"
            }
            else {
                Write-Error "Production deployment verification failed."
                exit 1
            }
        }
        catch {
            Write-Error "Production deployment failed: $_"
            exit 1
        }
    }
    else {
        Write-Info "Deploying preview..."
        
        try {
            $deployOutput = netlify deploy --dir=dist --site=$SITE_NAME --json | ConvertFrom-Json
            $deployUrl = $deployOutput.deploy_url
            
            if ($deployUrl) {
                Write-Success "Preview deployment completed!"
                Write-Info "Preview URL: $deployUrl"
            }
            else {
                Write-Error "Preview deployment failed."
                exit 1
            }
        }
        catch {
            Write-Error "Preview deployment failed: $_"
            exit 1
        }
    }
}

# Post-deployment tasks
function Invoke-PostDeployment {
    Write-Info "Running post-deployment tasks..."
    
    # Warm up cache
    Write-Info "Warming up cache..."
    try {
        Invoke-WebRequest -Uri "https://$PRODUCTION_DOMAIN" -Method Head -TimeoutSec 10 | Out-Null
        Invoke-WebRequest -Uri "https://$PRODUCTION_DOMAIN/enhanced" -Method Head -TimeoutSec 10 | Out-Null
        Invoke-WebRequest -Uri "https://$PRODUCTION_DOMAIN/faq" -Method Head -TimeoutSec 10 | Out-Null
    }
    catch {
        Write-Warning "Cache warm-up failed: $_"
    }
    
    # Test API connectivity
    Write-Info "Testing API connectivity..."
    try {
        $apiResponse = Invoke-WebRequest -Uri "https://$API_DOMAIN/health" -Method Head -TimeoutSec 10
        if ($apiResponse.StatusCode -eq 200) {
            Write-Success "API is accessible."
        }
        else {
            Write-Warning "API health check returned status: $($apiResponse.StatusCode)"
        }
    }
    catch {
        Write-Warning "API health check failed. Please verify backend deployment."
    }
    
    Write-Success "Post-deployment tasks completed."
}

# Main deployment function
function Start-Deployment {
    param(
        [string]$DeployType,
        [bool]$SkipTests
    )
    
    Write-Info "Starting Better Interest deployment..."
    Write-Info "Deploy type: $DeployType"
    Write-Info "Skip tests: $SkipTests"
    
    # Pre-deployment checks
    Test-Dependencies
    Test-Environment
    Install-Dependencies
    
    # Testing (unless skipped)
    if (-not $SkipTests) {
        Invoke-Tests
    }
    else {
        Write-Warning "Skipping tests as requested."
    }
    
    # Build and deploy
    Build-Application
    Deploy-ToNetlify $DeployType
    
    # Post-deployment (only for production)
    if ($DeployType -eq "production") {
        Invoke-PostDeployment
    }
    
    Write-Success "🎉 Deployment completed successfully!"
    
    if ($DeployType -eq "production") {
        Write-Host ""
        Write-Host "🌐 Production URL: https://$PRODUCTION_DOMAIN" -ForegroundColor $Colors.Green
        Write-Host "📊 Netlify Dashboard: https://app.netlify.com/sites/$SITE_NAME" -ForegroundColor $Colors.Blue
        Write-Host "🔧 API URL: https://$API_DOMAIN" -ForegroundColor $Colors.Blue
        Write-Host ""
    }
}

# Handle script arguments
switch ($DeployType) {
    { $_ -in @("production", "prod") } {
        Start-Deployment "production" $SkipTests.IsPresent
    }
    { $_ -in @("preview", "staging") } {
        Start-Deployment "preview" $SkipTests.IsPresent
    }
    "help" {
        Write-Host "Usage: .\deploy.ps1 [production|preview] [-SkipTests]"
        Write-Host ""
        Write-Host "Commands:"
        Write-Host "  production  Deploy to production (demo.kojaonline.store)"
        Write-Host "  preview     Deploy preview (default)"
        Write-Host "  help        Show this help message"
        Write-Host ""
        Write-Host "Options:"
        Write-Host "  -SkipTests  Skip running tests before deployment"
        Write-Host ""
        Write-Host "Examples:"
        Write-Host "  .\deploy.ps1 production           # Deploy to production"
        Write-Host "  .\deploy.ps1 preview              # Deploy preview"
        Write-Host "  .\deploy.ps1 production -SkipTests # Deploy to production without tests"
    }
    default {
        Start-Deployment "preview" $SkipTests.IsPresent
    }
}
