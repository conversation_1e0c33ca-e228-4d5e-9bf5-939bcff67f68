{"name": "better-interest-backend", "version": "1.0.0", "description": "Better Interest Backend API", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest"}, "dependencies": {"aws-sdk": "^2.1435.0", "axios": "^1.5.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.8.1", "express-validator": "^7.2.1", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "nodemailer": "^6.9.4"}, "devDependencies": {"jest": "^29.6.4", "nodemon": "^3.0.1"}, "keywords": ["fintech", "savings", "api"], "author": "Better Interest Team", "license": "MIT"}