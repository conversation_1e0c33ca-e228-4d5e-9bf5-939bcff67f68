{"name": "better-interest-backend", "version": "1.0.0", "description": "Better Interest Backend API", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "dotenv": "^16.3.1", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.9.4", "joi": "^17.9.2", "express-rate-limit": "^6.8.1", "aws-sdk": "^2.1435.0", "axios": "^1.5.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4"}, "keywords": ["fintech", "savings", "api"], "author": "Better Interest Team", "license": "MIT"}