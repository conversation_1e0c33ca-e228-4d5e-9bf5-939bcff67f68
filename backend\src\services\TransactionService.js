const mongoose = require('mongoose');
const Transaction = require('../models/Transaction');
const User = require('../models/User');
const SavingsPlan = require('../models/SavingsPlan');
const GroupSavingsPlan = require('../models/GroupSavingsPlan');
const FixedDeposit = require('../models/FixedDeposit');
const { sendEmail } = require('./emailService');

class TransactionService {
  constructor() {
    this.retryAttempts = 3;
    this.retryDelay = 1000; // 1 second
  }

  /**
   * Create a transaction with atomic operations
   * @param {Object} transactionData - Transaction data
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Transaction result
   */
  async createTransaction(transactionData, options = {}) {
    const session = await mongoose.startSession();
    
    try {
      await session.withTransaction(async () => {
        const result = await this._processTransaction(transactionData, session, options);
        return result;
      }, {
        readPreference: 'primary',
        readConcern: { level: 'local' },
        writeConcern: { w: 'majority' }
      });

      return { success: true, transaction: result };
    } catch (error) {
      console.error('Transaction failed:', error);
      throw new Error(`Transaction failed: ${error.message}`);
    } finally {
      await session.endSession();
    }
  }

  /**
   * Process transaction with business logic
   * @private
   */
  async _processTransaction(transactionData, session, options) {
    // Validate transaction data
    this._validateTransactionData(transactionData);

    // Get user with session
    const user = await User.findById(transactionData.userId).session(session);
    if (!user) {
      throw new Error('User not found');
    }

    // Check balance for debit transactions
    if (this._isDebitTransaction(transactionData.type)) {
      await this._validateSufficientBalance(user, transactionData.amount);
    }

    // Set balance before
    transactionData.balanceBefore = user.balance;

    // Calculate new balance
    const balanceChange = this._calculateBalanceChange(transactionData);
    transactionData.balanceAfter = user.balance + balanceChange;

    // Create transaction record
    const transaction = new Transaction({
      ...transactionData,
      reference: transactionData.reference || this._generateReference(transactionData.type),
      status: 'pending'
    });

    await transaction.save({ session });

    // Update user balance
    user.balance = transactionData.balanceAfter;
    user.lastTransactionDate = new Date();
    await user.save({ session });

    // Process related entities
    await this._processRelatedEntities(transactionData, session);

    // Update transaction status to completed
    transaction.status = 'completed';
    await transaction.save({ session });

    // Send notifications if enabled
    if (options.sendNotification !== false) {
      await this._sendTransactionNotification(user, transaction);
    }

    return transaction;
  }

  /**
   * Batch process multiple transactions
   * @param {Array} transactions - Array of transaction data
   * @returns {Promise<Object>} Batch result
   */
  async batchProcessTransactions(transactions) {
    const session = await mongoose.startSession();
    const results = [];
    const errors = [];

    try {
      await session.withTransaction(async () => {
        for (const [index, txnData] of transactions.entries()) {
          try {
            const result = await this._processTransaction(txnData, session);
            results.push({ index, success: true, transaction: result });
          } catch (error) {
            errors.push({ index, error: error.message, data: txnData });
          }
        }

        // If any critical errors, abort the entire batch
        if (errors.length > 0 && errors.some(e => e.data.critical)) {
          throw new Error('Critical transaction failed in batch');
        }
      });

      return { success: true, results, errors };
    } catch (error) {
      console.error('Batch transaction failed:', error);
      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Reverse a transaction
   * @param {String} transactionId - Transaction ID to reverse
   * @param {String} reason - Reason for reversal
   * @returns {Promise<Object>} Reversal result
   */
  async reverseTransaction(transactionId, reason) {
    const session = await mongoose.startSession();

    try {
      const result = await session.withTransaction(async () => {
        const originalTransaction = await Transaction.findById(transactionId).session(session);
        
        if (!originalTransaction) {
          throw new Error('Original transaction not found');
        }

        if (originalTransaction.status !== 'completed') {
          throw new Error('Can only reverse completed transactions');
        }

        // Create reversal transaction
        const reversalData = {
          userId: originalTransaction.userId,
          type: this._getReversalType(originalTransaction.type),
          amount: originalTransaction.amount,
          originalAmount: originalTransaction.amount,
          description: `Reversal: ${originalTransaction.description}`,
          category: originalTransaction.category,
          relatedId: originalTransaction._id,
          relatedModel: 'Transaction',
          metadata: {
            ...originalTransaction.metadata,
            isReversal: true,
            originalTransactionId: originalTransaction._id,
            reversalReason: reason
          }
        };

        const reversalTransaction = await this._processTransaction(reversalData, session);

        // Mark original transaction as reversed
        originalTransaction.status = 'reversed';
        originalTransaction.metadata.reversedBy = reversalTransaction._id;
        originalTransaction.metadata.reversalReason = reason;
        await originalTransaction.save({ session });

        return { original: originalTransaction, reversal: reversalTransaction };
      });

      return { success: true, ...result };
    } catch (error) {
      console.error('Transaction reversal failed:', error);
      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Get transaction history with filters
   * @param {String} userId - User ID
   * @param {Object} filters - Filter options
   * @returns {Promise<Object>} Transaction history
   */
  async getTransactionHistory(userId, filters = {}) {
    try {
      const {
        type,
        status,
        category,
        startDate,
        endDate,
        limit = 50,
        skip = 0,
        sortBy = 'createdAt',
        sortOrder = -1
      } = filters;

      const query = { userId };

      // Apply filters
      if (type) query.type = Array.isArray(type) ? { $in: type } : type;
      if (status) query.status = Array.isArray(status) ? { $in: status } : status;
      if (category) query.category = Array.isArray(category) ? { $in: category } : category;

      if (startDate || endDate) {
        query.createdAt = {};
        if (startDate) query.createdAt.$gte = new Date(startDate);
        if (endDate) query.createdAt.$lte = new Date(endDate);
      }

      const transactions = await Transaction.find(query)
        .sort({ [sortBy]: sortOrder })
        .limit(parseInt(limit))
        .skip(parseInt(skip))
        .populate('userId', 'firstName lastName email')
        .populate('relatedId')
        .lean();

      const total = await Transaction.countDocuments(query);

      // Calculate summary statistics
      const summary = await this._calculateTransactionSummary(userId, query);

      return {
        success: true,
        data: {
          transactions,
          pagination: {
            total,
            limit: parseInt(limit),
            skip: parseInt(skip),
            pages: Math.ceil(total / limit)
          },
          summary
        }
      };
    } catch (error) {
      console.error('Error fetching transaction history:', error);
      throw error;
    }
  }

  /**
   * Validate transaction data
   * @private
   */
  _validateTransactionData(data) {
    const required = ['userId', 'type', 'amount', 'description'];
    const missing = required.filter(field => !data[field]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required fields: ${missing.join(', ')}`);
    }

    if (data.amount <= 0) {
      throw new Error('Transaction amount must be positive');
    }

    const validTypes = [
      'deposit', 'withdrawal', 'transfer', 'interest', 'penalty', 
      'bonus', 'refund', 'bill_payment', 'loan_disbursement', 
      'loan_repayment', 'purchase', 'investment'
    ];

    if (!validTypes.includes(data.type)) {
      throw new Error(`Invalid transaction type: ${data.type}`);
    }
  }

  /**
   * Check if transaction is a debit
   * @private
   */
  _isDebitTransaction(type) {
    const debitTypes = [
      'withdrawal', 'transfer', 'penalty', 'bill_payment', 
      'loan_repayment', 'purchase', 'investment'
    ];
    return debitTypes.includes(type);
  }

  /**
   * Validate sufficient balance
   * @private
   */
  async _validateSufficientBalance(user, amount) {
    if (user.balance < amount) {
      throw new Error(`Insufficient balance. Available: ₦${user.balance.toLocaleString()}, Required: ₦${amount.toLocaleString()}`);
    }
  }

  /**
   * Calculate balance change
   * @private
   */
  _calculateBalanceChange(transactionData) {
    const isDebit = this._isDebitTransaction(transactionData.type);
    return isDebit ? -Math.abs(transactionData.amount) : Math.abs(transactionData.amount);
  }

  /**
   * Generate unique transaction reference
   * @private
   */
  _generateReference(type) {
    const prefix = type.toUpperCase().substring(0, 3);
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `${prefix}_${timestamp}_${random}`;
  }

  /**
   * Process related entities (savings plans, etc.)
   * @private
   */
  async _processRelatedEntities(transactionData, session) {
    if (!transactionData.relatedId || !transactionData.relatedModel) {
      return;
    }

    switch (transactionData.relatedModel) {
      case 'SavingsPlan':
        await this._updateSavingsPlan(transactionData, session);
        break;
      case 'GroupSavingsPlan':
        await this._updateGroupSavingsPlan(transactionData, session);
        break;
      case 'FixedDeposit':
        await this._updateFixedDeposit(transactionData, session);
        break;
    }
  }

  /**
   * Update savings plan
   * @private
   */
  async _updateSavingsPlan(transactionData, session) {
    const plan = await SavingsPlan.findById(transactionData.relatedId).session(session);
    if (!plan) return;

    const isCredit = !this._isDebitTransaction(transactionData.type);
    const change = isCredit ? transactionData.amount : -transactionData.amount;
    
    plan.currentAmount += change;
    plan.lastTransactionDate = new Date();
    
    // Check if target reached
    if (plan.currentAmount >= plan.targetAmount && plan.status === 'active') {
      plan.status = 'completed';
      plan.completedAt = new Date();
    }

    await plan.save({ session });
  }

  /**
   * Send transaction notification
   * @private
   */
  async _sendTransactionNotification(user, transaction) {
    try {
      const emailData = {
        to: user.email,
        subject: `Transaction ${transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)} - ₦${transaction.amount.toLocaleString()}`,
        template: 'transaction-notification',
        data: {
          userName: `${user.firstName} ${user.lastName}`,
          transactionType: transaction.type,
          amount: transaction.amount,
          description: transaction.description,
          reference: transaction.reference,
          balanceAfter: transaction.balanceAfter,
          date: transaction.createdAt
        }
      };

      await sendEmail(emailData);
    } catch (error) {
      console.error('Failed to send transaction notification:', error);
      // Don't throw error as this is not critical
    }
  }

  /**
   * Calculate transaction summary
   * @private
   */
  async _calculateTransactionSummary(userId, baseQuery) {
    const pipeline = [
      { $match: { ...baseQuery, userId: new mongoose.Types.ObjectId(userId) } },
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          avgAmount: { $avg: '$amount' }
        }
      }
    ];

    const summary = await Transaction.aggregate(pipeline);
    
    return {
      totalTransactions: summary.reduce((sum, item) => sum + item.count, 0),
      totalAmount: summary.reduce((sum, item) => sum + item.totalAmount, 0),
      byType: summary.reduce((acc, item) => {
        acc[item._id] = {
          count: item.count,
          totalAmount: item.totalAmount,
          avgAmount: item.avgAmount
        };
        return acc;
      }, {})
    };
  }

  /**
   * Get reversal transaction type
   * @private
   */
  _getReversalType(originalType) {
    const reversalMap = {
      'deposit': 'withdrawal',
      'withdrawal': 'deposit',
      'penalty': 'bonus',
      'bonus': 'penalty',
      'interest': 'penalty'
    };
    
    return reversalMap[originalType] || 'refund';
  }
}

module.exports = new TransactionService();
