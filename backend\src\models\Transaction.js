const mongoose = require('mongoose');

const transactionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: ['deposit', 'withdrawal', 'transfer', 'interest', 'penalty', 'bonus', 'refund', 'bill_payment', 'loan_disbursement', 'loan_repayment', 'purchase', 'investment'],
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  roundUpAmount: {
    type: Number,
    default: 0
  },
  originalAmount: {
    type: Number,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  reference: {
    type: String,
    unique: true,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'cancelled'],
    default: 'pending'
  },
  category: {
    type: String,
    enum: ['savings', 'investment', 'bill', 'transfer', 'loan', 'interest', 'penalty', 'bonus', 'food', 'transport', 'utilities', 'entertainment', 'shopping', 'healthcare', 'education', 'other'],
    default: 'other'
  },
  relatedId: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: 'relatedModel'
  },
  relatedModel: {
    type: String,
    enum: ['SavingsPlan', 'GroupSavingsPlan', 'FixedDeposit', 'Investment', 'Loan', 'Payment']
  },
  balanceBefore: {
    type: Number,
    default: 0
  },
  balanceAfter: {
    type: Number,
    default: 0
  },
  fees: {
    type: Number,
    default: 0
  },
  exchangeRate: {
    type: Number,
    default: 1
  },
  merchant: {
    name: String,
    category: String,
    location: String
  },
  paymentMethod: {
    type: String,
    enum: ['card', 'bank_transfer', 'mobile_money', 'cash'],
    default: 'card'
  },
  metadata: {
    roundUpEnabled: { type: Boolean, default: true },
    roundUpMultiplier: { type: Number, default: 1 },
    source: String,
    destinationAccount: String,
    processorResponse: mongoose.Schema.Types.Mixed
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for displaying formatted amount
transactionSchema.virtual('formattedAmount').get(function() {
  return new Intl.NumberFormat('en-NG', {
    style: 'currency',
    currency: 'NGN'
  }).format(this.amount);
});

// Indexes
transactionSchema.index({ userId: 1, createdAt: -1 });
transactionSchema.index({ reference: 1 });
transactionSchema.index({ status: 1, type: 1 });
transactionSchema.index({ 'metadata.roundUpEnabled': 1, roundUpAmount: 1 });

// Calculate round-up amount
transactionSchema.methods.calculateRoundUp = function(multiplier = 1) {
  const roundedUp = Math.ceil(this.originalAmount / 100) * 100;
  this.roundUpAmount = (roundedUp - this.originalAmount) * multiplier;
  return this.roundUpAmount;
};

// Generate unique reference
transactionSchema.pre('save', function(next) {
  if (!this.reference) {
    this.reference = `TXN_${Date.now()}_${Math.random().toString(36).substring(2, 11).toUpperCase()}`;
  }
  next();
});

// Virtual for transaction direction
transactionSchema.virtual('direction').get(function() {
  const debitTypes = ['withdrawal', 'transfer', 'penalty', 'bill_payment', 'loan_repayment'];
  return debitTypes.includes(this.type) ? 'debit' : 'credit';
});

// Method to generate unique reference
transactionSchema.methods.generateReference = function() {
  const prefix = this.type.toUpperCase().substring(0, 3);
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8).toUpperCase();
  this.reference = `${prefix}_${timestamp}_${random}`;
  return this.reference;
};

// Static method to find user transactions
transactionSchema.statics.findByUser = function(userId, options = {}) {
  const {
    type,
    status,
    category,
    startDate,
    endDate,
    limit = 20,
    skip = 0
  } = options;

  const query = { userId };

  if (type) query.type = type;
  if (status) query.status = status;
  if (category) query.category = category;

  if (startDate || endDate) {
    query.createdAt = {};
    if (startDate) query.createdAt.$gte = new Date(startDate);
    if (endDate) query.createdAt.$lte = new Date(endDate);
  }

  return this.find(query)
    .sort({ createdAt: -1 })
    .limit(limit)
    .skip(skip)
    .populate('userId', 'firstName lastName email')
    .populate('relatedId');
};

// Static method to create transaction with balance update
transactionSchema.statics.createWithBalanceUpdate = async function(transactionData) {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const User = mongoose.model('User');
    const user = await User.findById(transactionData.userId).session(session);

    if (!user) {
      throw new Error('User not found');
    }

    // Set balance before
    transactionData.balanceBefore = user.balance;

    // Calculate new balance
    const isDebit = ['withdrawal', 'transfer', 'penalty', 'bill_payment', 'loan_repayment'].includes(transactionData.type);
    const balanceChange = isDebit ? -Math.abs(transactionData.amount) : Math.abs(transactionData.amount);

    transactionData.balanceAfter = user.balance + balanceChange;

    // Create transaction
    const transaction = new this(transactionData);
    if (!transaction.reference) {
      transaction.generateReference();
    }

    await transaction.save({ session });

    // Update user balance
    user.balance = transactionData.balanceAfter;
    await user.save({ session });

    await session.commitTransaction();
    return transaction;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    session.endSession();
  }
};

module.exports = mongoose.model('Transaction', transactionSchema);
