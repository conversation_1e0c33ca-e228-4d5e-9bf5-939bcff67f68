const mongoose = require('mongoose');

const transactionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: ['purchase', 'transfer', 'withdrawal', 'deposit', 'bill_payment', 'investment'],
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  roundUpAmount: {
    type: Number,
    default: 0
  },
  originalAmount: {
    type: Number,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  reference: {
    type: String,
    unique: true,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'cancelled'],
    default: 'pending'
  },
  category: {
    type: String,
    enum: ['food', 'transport', 'utilities', 'entertainment', 'shopping', 'healthcare', 'education', 'other'],
    default: 'other'
  },
  merchant: {
    name: String,
    category: String,
    location: String
  },
  paymentMethod: {
    type: String,
    enum: ['card', 'bank_transfer', 'mobile_money', 'cash'],
    default: 'card'
  },
  metadata: {
    roundUpEnabled: { type: Boolean, default: true },
    roundUpMultiplier: { type: Number, default: 1 },
    source: String,
    destinationAccount: String,
    processorResponse: mongoose.Schema.Types.Mixed
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for displaying formatted amount
transactionSchema.virtual('formattedAmount').get(function() {
  return new Intl.NumberFormat('en-NG', {
    style: 'currency',
    currency: 'NGN'
  }).format(this.amount);
});

// Indexes
transactionSchema.index({ userId: 1, createdAt: -1 });
transactionSchema.index({ reference: 1 });
transactionSchema.index({ status: 1, type: 1 });
transactionSchema.index({ 'metadata.roundUpEnabled': 1, roundUpAmount: 1 });

// Calculate round-up amount
transactionSchema.methods.calculateRoundUp = function(multiplier = 1) {
  const roundedUp = Math.ceil(this.originalAmount / 100) * 100;
  this.roundUpAmount = (roundedUp - this.originalAmount) * multiplier;
  return this.roundUpAmount;
};

// Generate unique reference
transactionSchema.pre('save', function(next) {
  if (!this.reference) {
    this.reference = `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
  }
  next();
});

module.exports = mongoose.model('Transaction', transactionSchema);