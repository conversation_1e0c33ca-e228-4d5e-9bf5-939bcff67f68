import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Shield, User, CreditCard, ExternalLink } from "lucide-react";
import { toast } from "sonner";

interface DojahKycModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: (data: any) => void;
}

export const DojahKycModal = ({ open, onOpenChange, onSuccess }: DojahKycModalProps) => {
  const [formData, setFormData] = useState({
    fullName: "",
    idType: "",
    idNumber: "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.fullName || !formData.idType || !formData.idNumber) {
      toast.error("Please fill in all required fields");
      return;
    }

    setIsLoading(true);

    try {
      // TODO: Integrate with Dojah API via Supabase edge function
      // This will redirect to Dojah's verification flow
      
      // Mock API call
      const dojahVerificationUrl = `https://api.dojah.io/verify?name=${encodeURIComponent(formData.fullName)}&id_type=${formData.idType}&id_number=${formData.idNumber}`;
      
      // Open Dojah verification in new window
      window.open(dojahVerificationUrl, '_blank', 'width=600,height=700');
      
      toast.success("Redirecting to Dojah for verification...");
      onSuccess(formData);
      onOpenChange(false);
      
    } catch (error: any) {
      toast.error("Failed to initiate verification");
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      fullName: "",
      idType: "",
      idNumber: "",
    });
  };

  return (
    <Dialog open={open} onOpenChange={(newOpen) => {
      onOpenChange(newOpen);
      if (!newOpen) resetForm();
    }}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-primary" />
            KYC Verification with Dojah
          </DialogTitle>
          <DialogDescription>
            Complete your identity verification to unlock all features
          </DialogDescription>
        </DialogHeader>

        <Card className="border-blue-200 bg-blue-50/50 dark:bg-blue-900/20">
          <CardContent className="pt-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-800">
                <Shield className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  Secure Verification Process
                </p>
                <p className="text-xs text-blue-700 dark:text-blue-300">
                  Your data is encrypted and processed securely by Dojah
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                First Name
              </Label>
              <Input
                id="firstName"
                value={formData.fullName.split(' ')[0] || ''}
                onChange={(e) => {
                  const lastName = formData.fullName.split(' ').slice(1).join(' ');
                  setFormData(prev => ({ ...prev, fullName: `${e.target.value} ${lastName}`.trim() }));
                }}
                placeholder="First name"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="lastName">
                Last Name
              </Label>
              <Input
                id="lastName"
                value={formData.fullName.split(' ').slice(1).join(' ') || ''}
                onChange={(e) => {
                  const firstName = formData.fullName.split(' ')[0] || '';
                  setFormData(prev => ({ ...prev, fullName: `${firstName} ${e.target.value}`.trim() }));
                }}
                placeholder="Last name"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="idType" className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              ID Type
            </Label>
            <Select value={formData.idType} onValueChange={(value) => setFormData(prev => ({ ...prev, idType: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Select ID type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="nin">NIN (National Identification Number)</SelectItem>
                <SelectItem value="bvn">BVN (Bank Verification Number)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="idNumber">
              {formData.idType === 'nin' ? 'NIN Number' : formData.idType === 'bvn' ? 'BVN Number' : 'ID Number'}
            </Label>
            <Input
              id="idNumber"
              value={formData.idNumber}
              onChange={(e) => setFormData(prev => ({ ...prev, idNumber: e.target.value }))}
              placeholder={`Enter your ${formData.idType?.toUpperCase() || 'ID number'}`}
              maxLength={formData.idType === 'nin' ? 11 : formData.idType === 'bvn' ? 11 : undefined}
              required
            />
          </div>

          <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg">
            <p className="text-sm text-amber-800 dark:text-amber-200">
              <strong>Note:</strong> You will be redirected to Dojah's secure verification platform to complete the process.
            </p>
          </div>

          <div className="flex gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1 gap-2"
            >
              {isLoading ? (
                <>
                  <span className="animate-spin">⟳</span>
                  Verifying...
                </>
              ) : (
                <>
                  <ExternalLink className="h-4 w-4" />
                  Start Verification
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};