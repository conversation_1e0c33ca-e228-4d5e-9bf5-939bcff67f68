const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const axios = require('axios');

// NIBSS Configuration - Replace with actual credentials
const NIBSS_CONFIG = {
  baseUrl: process.env.NIBSS_BASE_URL || 'https://sandbox.nibss.com/api/v1',
  institutionCode: process.env.NIBSS_INSTITUTION_CODE || 'DEMO001',
  username: process.env.NIBSS_USERNAME || 'demo_user',
  password: process.env.NIBSS_PASSWORD || 'demo_pass',
  apiKey: process.env.NIBSS_API_KEY || 'demo_api_key'
};

// Helper function to get NIBSS auth token
const getNIBSSToken = async () => {
  try {
    const response = await axios.post(`${NIBSS_CONFIG.baseUrl}/auth/login`, {
      username: NIBSS_CONFIG.username,
      password: NIBSS_CONFIG.password,
      institutionCode: NIBSS_CONFIG.institutionCode
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': NIBSS_CONFIG.apiKey
      }
    });
    
    return response.data.token;
  } catch (error) {
    console.error('NIBSS Auth Error:', error.response?.data || error.message);
    throw new Error('Failed to authenticate with NIBSS');
  }
};

// Name Enquiry Service
router.post('/name-enquiry', auth, async (req, res) => {
  try {
    const { accountNumber, bankCode } = req.body;
    
    if (!accountNumber || !bankCode) {
      return res.status(400).json({
        success: false,
        message: 'Account number and bank code are required'
      });
    }

    const token = await getNIBSSToken();
    
    const response = await axios.post(`${NIBSS_CONFIG.baseUrl}/name-enquiry`, {
      destinationInstitutionCode: bankCode,
      channelCode: '7',
      accountNumber: accountNumber
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    res.json({
      success: true,
      data: {
        accountNumber: response.data.accountNumber,
        accountName: response.data.accountName,
        bankCode: response.data.destinationInstitutionCode,
        responseCode: response.data.responseCode
      }
    });
  } catch (error) {
    console.error('NIBSS Name Enquiry Error:', error.response?.data || error.message);
    res.status(500).json({
      success: false,
      message: 'Failed to verify account details',
      error: error.response?.data || error.message
    });
  }
});

// Fund Transfer Service (NIP)
router.post('/transfer', auth, async (req, res) => {
  try {
    const { 
      amount, 
      destinationAccountNumber, 
      destinationBankCode, 
      narration, 
      beneficiaryName 
    } = req.body;

    if (!amount || !destinationAccountNumber || !destinationBankCode) {
      return res.status(400).json({
        success: false,
        message: 'Amount, destination account number, and bank code are required'
      });
    }

    const token = await getNIBSSToken();
    
    const transferData = {
      sessionId: `TXN_${Date.now()}`,
      nameEnquiryId: `NE_${Date.now()}`,
      destinationInstitutionCode: destinationBankCode,
      channelCode: '7',
      amount: parseFloat(amount).toFixed(2),
      destinationAccountNumber: destinationAccountNumber,
      originatorAccountNumber: req.user.accountNumber || '**********',
      originatorName: `${req.user.firstName} ${req.user.lastName}`,
      originatorKYCLevel: '3',
      beneficiaryName: beneficiaryName,
      narration: narration || 'Better Interest Transfer',
      paymentReference: `BI_${Date.now()}`
    };

    const response = await axios.post(`${NIBSS_CONFIG.baseUrl}/single-payment`, transferData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    res.json({
      success: true,
      data: {
        sessionId: response.data.sessionId,
        responseCode: response.data.responseCode,
        responseDescription: response.data.responseDescription,
        amount: amount,
        beneficiaryName: beneficiaryName,
        reference: transferData.paymentReference
      }
    });
  } catch (error) {
    console.error('NIBSS Transfer Error:', error.response?.data || error.message);
    res.status(500).json({
      success: false,
      message: 'Transfer failed',
      error: error.response?.data || error.message
    });
  }
});

// Transaction Status Query
router.get('/transaction-status/:sessionId', auth, async (req, res) => {
  try {
    const { sessionId } = req.params;
    
    const token = await getNIBSSToken();
    
    const response = await axios.get(`${NIBSS_CONFIG.baseUrl}/transaction-status/${sessionId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    res.json({
      success: true,
      data: response.data
    });
  } catch (error) {
    console.error('NIBSS Status Query Error:', error.response?.data || error.message);
    res.status(500).json({
      success: false,
      message: 'Failed to query transaction status',
      error: error.response?.data || error.message
    });
  }
});

// Get Bank List
router.get('/banks', async (req, res) => {
  try {
    // For demo purposes, return a static list of Nigerian banks
    // In production, this would fetch from NIBSS or CBN API
    const banks = [
      { code: '044', name: 'Access Bank' },
      { code: '063', name: 'Access Bank (Diamond)' },
      { code: '035', name: 'ALAT by WEMA' },
      { code: '401', name: 'ASO Savings and Loans' },
      { code: '050', name: 'Ecobank Nigeria' },
      { code: '070', name: 'Fidelity Bank' },
      { code: '011', name: 'First Bank of Nigeria' },
      { code: '214', name: 'First City Monument Bank' },
      { code: '058', name: 'Guaranty Trust Bank' },
      { code: '030', name: 'Heritage Bank' },
      { code: '301', name: 'Jaiz Bank' },
      { code: '082', name: 'Keystone Bank' },
      { code: '221', name: 'Stanbic IBTC Bank' },
      { code: '068', name: 'Standard Chartered Bank' },
      { code: '232', name: 'Sterling Bank' },
      { code: '100', name: 'Suntrust Bank' },
      { code: '032', name: 'Union Bank of Nigeria' },
      { code: '033', name: 'United Bank For Africa' },
      { code: '215', name: 'Unity Bank' },
      { code: '035', name: 'Wema Bank' },
      { code: '057', name: 'Zenith Bank' }
    ];

    res.json({
      success: true,
      data: banks
    });
  } catch (error) {
    console.error('Banks List Error:', error.message);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch bank list'
    });
  }
});

// Balance Enquiry
router.get('/balance', auth, async (req, res) => {
  try {
    const token = await getNIBSSToken();
    
    const response = await axios.post(`${NIBSS_CONFIG.baseUrl}/balance-enquiry`, {
      accountNumber: req.user.accountNumber || '**********',
      institutionCode: NIBSS_CONFIG.institutionCode
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    res.json({
      success: true,
      data: {
        accountNumber: response.data.accountNumber,
        availableBalance: response.data.availableBalance,
        ledgerBalance: response.data.ledgerBalance,
        currency: response.data.currency || 'NGN'
      }
    });
  } catch (error) {
    console.error('NIBSS Balance Enquiry Error:', error.response?.data || error.message);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch balance',
      error: error.response?.data || error.message
    });
  }
});

module.exports = router;