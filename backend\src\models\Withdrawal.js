const mongoose = require('mongoose');

const withdrawalSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  savingsPlanId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SavingsPlan'
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  withdrawalType: {
    type: String,
    enum: ['partial', 'full', 'emergency', 'maturity'],
    required: true
  },
  reason: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'processing', 'completed', 'rejected', 'cancelled'],
    default: 'pending'
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  destinationAccount: {
    accountNumber: {
      type: String,
      required: true
    },
    bankName: {
      type: String,
      required: true
    },
    bankCode: {
      type: String,
      required: true
    },
    accountName: {
      type: String,
      required: true
    }
  },
  charges: {
    processingFee: { type: Number, default: 0 },
    penaltyFee: { type: Number, default: 0 },
    totalCharges: { type: Number, default: 0 }
  },
  netAmount: {
    type: Number,
    required: true
  },
  reference: {
    type: String,
    unique: true,
    required: true
  },
  bankReference: {
    type: String
  },
  approvalChain: [{
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    level: String,
    action: { type: String, enum: ['approved', 'rejected'] },
    comments: String,
    approvedAt: {
      type: Date,
      default: Date.now
    }
  }],
  timeline: [{
    status: String,
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    notes: String
  }],
  verification: {
    otpVerified: { type: Boolean, default: false },
    pinVerified: { type: Boolean, default: false },
    biometricVerified: { type: Boolean, default: false },
    verifiedAt: Date
  },
  processing: {
    provider: String,
    providerReference: String,
    providerResponse: Object,
    attempts: { type: Number, default: 0 },
    lastAttempt: Date,
    failureReason: String
  },
  documents: [{
    type: String,
    url: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  scheduledDate: {
    type: Date
  },
  completedAt: Date,
  rejectedAt: Date,
  rejectionReason: String,
  metadata: {
    ipAddress: String,
    userAgent: String,
    deviceInfo: Object,
    location: Object
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for formatted amount
withdrawalSchema.virtual('formattedAmount').get(function() {
  return new Intl.NumberFormat('en-NG', {
    style: 'currency',
    currency: 'NGN'
  }).format(this.amount);
});

// Virtual for days pending
withdrawalSchema.virtual('daysPending').get(function() {
  const now = new Date();
  const created = new Date(this.createdAt);
  return Math.floor((now - created) / (1000 * 60 * 60 * 24));
});

// Indexes
withdrawalSchema.index({ userId: 1, status: 1 });
withdrawalSchema.index({ reference: 1 });
withdrawalSchema.index({ status: 1, priority: -1, createdAt: 1 });
withdrawalSchema.index({ scheduledDate: 1, status: 1 });
withdrawalSchema.index({ createdAt: -1 });

// Generate unique reference
withdrawalSchema.pre('save', function(next) {
  if (!this.reference) {
    this.reference = `WD_${Date.now()}_${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
  }
  
  // Calculate net amount
  this.netAmount = this.amount - this.charges.totalCharges;
  
  next();
});

// Add timeline entry
withdrawalSchema.methods.addTimelineEntry = function(status, updatedBy, notes = '') {
  this.timeline.push({
    status,
    updatedBy,
    timestamp: new Date(),
    notes
  });
  
  this.status = status;
  
  if (status === 'completed') {
    this.completedAt = new Date();
  } else if (status === 'rejected') {
    this.rejectedAt = new Date();
    this.rejectionReason = notes;
  }
  
  return this.save();
};

// Add approval
withdrawalSchema.methods.addApproval = function(approvedBy, level, action, comments = '') {
  this.approvalChain.push({
    approvedBy,
    level,
    action,
    comments,
    approvedAt: new Date()
  });
  
  if (action === 'approved') {
    this.status = 'approved';
  } else if (action === 'rejected') {
    this.status = 'rejected';
    this.rejectionReason = comments;
    this.rejectedAt = new Date();
  }
  
  return this.save();
};

// Calculate charges
withdrawalSchema.methods.calculateCharges = function() {
  let processingFee = 0;
  let penaltyFee = 0;
  
  // Basic processing fee (configurable)
  processingFee = Math.min(100, this.amount * 0.01); // 1% or ₦100 max
  
  // Penalty for early withdrawal
  if (this.withdrawalType === 'emergency' || this.withdrawalType === 'partial') {
    penaltyFee = this.amount * 0.05; // 5% penalty
  }
  
  this.charges.processingFee = processingFee;
  this.charges.penaltyFee = penaltyFee;
  this.charges.totalCharges = processingFee + penaltyFee;
  this.netAmount = this.amount - this.charges.totalCharges;
  
  return this.charges;
};

// Get withdrawals pending approval
withdrawalSchema.statics.getPendingApproval = function(level = null) {
  const query = { status: 'pending' };
  
  if (level) {
    query['approvalChain.level'] = { $ne: level };
  }
  
  return this.find(query)
    .populate('userId', 'firstName lastName email')
    .populate('savingsPlanId', 'name planType targetAmount')
    .sort({ priority: -1, createdAt: 1 });
};

// Get withdrawals due for processing
withdrawalSchema.statics.getDueForProcessing = function() {
  const now = new Date();
  return this.find({
    status: 'approved',
    $or: [
      { scheduledDate: { $lte: now } },
      { scheduledDate: null }
    ]
  }).sort({ priority: -1, createdAt: 1 });
};

// Mark as verified
withdrawalSchema.methods.markVerified = function(verificationType) {
  this.verification[verificationType] = true;
  this.verification.verifiedAt = new Date();
  
  // Check if fully verified
  const requiredVerifications = ['otpVerified', 'pinVerified'];
  const isFullyVerified = requiredVerifications.every(
    verification => this.verification[verification]
  );
  
  if (isFullyVerified && this.status === 'pending') {
    this.status = 'approved';
  }
  
  return this.save();
};

module.exports = mongoose.model('Withdrawal', withdrawalSchema);