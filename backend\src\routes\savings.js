const express = require('express');
const router = express.Router();
const { authenticateToken: auth } = require('../middleware/auth');
const SavingsPlan = require('../models/SavingsPlan');
const User = require('../models/User');
const { v4: uuidv4 } = require('uuid');

// Get all user's savings plans
router.get('/plans', auth, async (req, res) => {
  try {
    const { status, planType, page = 1, limit = 20 } = req.query;
    
    const filter = { userId: req.user.id };
    if (status) filter.status = status;
    if (planType) filter.planType = planType;
    
    const plans = await SavingsPlan.find(filter)
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await SavingsPlan.countDocuments(filter);
    
    res.json({
      success: true,
      data: plans,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching savings plans:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch savings plans' 
    });
  }
});

// Create new savings plan
router.post('/plans', auth, async (req, res) => {
  try {
    const {
      planType,
      name,
      description,
      targetAmount,
      contributionAmount,
      frequency,
      endDate,
      interestRate,
      autoDebit,
      lockSettings
    } = req.body;

    // Validate required fields
    if (!planType || !name || !targetAmount || !contributionAmount || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: planType, name, targetAmount, contributionAmount, endDate'
      });
    }

    // Validate plan type
    const validPlanTypes = ['flex', 'fixed', 'safelock', 'target', 'autosave', 'roundup'];
    if (!validPlanTypes.includes(planType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid plan type'
      });
    }

    // Validate amounts
    if (targetAmount <= 0 || contributionAmount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Target amount and contribution amount must be greater than 0'
      });
    }

    // Validate end date
    const endDateTime = new Date(endDate);
    if (endDateTime <= new Date()) {
      return res.status(400).json({
        success: false,
        message: 'End date must be in the future'
      });
    }

    // Create savings plan
    const savingsPlan = new SavingsPlan({
      userId: req.user.id,
      planType,
      name,
      description,
      targetAmount,
      contributionAmount,
      frequency: frequency || 'monthly',
      endDate: endDateTime,
      interestRate: interestRate || 0,
      autoDebit: autoDebit || { enabled: false },
      lockSettings: lockSettings || { isLocked: false }
    });

    // Set next debit date for auto-debit plans
    if (autoDebit?.enabled) {
      const nextDebitDate = new Date();
      switch (frequency) {
        case 'daily':
          nextDebitDate.setDate(nextDebitDate.getDate() + 1);
          break;
        case 'weekly':
          nextDebitDate.setDate(nextDebitDate.getDate() + 7);
          break;
        case 'monthly':
        default:
          nextDebitDate.setMonth(nextDebitDate.getMonth() + 1);
          break;
      }
      savingsPlan.autoDebit.nextDebitDate = nextDebitDate;
    }

    await savingsPlan.save();

    res.status(201).json({
      success: true,
      data: savingsPlan,
      message: 'Savings plan created successfully'
    });
  } catch (error) {
    console.error('Error creating savings plan:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to create savings plan' 
    });
  }
});

// Get specific savings plan
router.get('/plans/:planId', auth, async (req, res) => {
  try {
    const plan = await SavingsPlan.findOne({
      _id: req.params.planId,
      userId: req.user.id
    });

    if (!plan) {
      return res.status(404).json({
        success: false,
        message: 'Savings plan not found'
      });
    }

    res.json({
      success: true,
      data: plan
    });
  } catch (error) {
    console.error('Error fetching savings plan:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch savings plan' 
    });
  }
});

// Update savings plan
router.put('/plans/:planId', auth, async (req, res) => {
  try {
    const allowedUpdates = [
      'name', 'description', 'contributionAmount', 'frequency', 
      'endDate', 'autoDebit', 'notifications'
    ];

    const updates = {};
    Object.keys(req.body).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updates[key] = req.body[key];
      }
    });

    const plan = await SavingsPlan.findOneAndUpdate(
      { _id: req.params.planId, userId: req.user.id },
      { $set: updates },
      { new: true, runValidators: true }
    );

    if (!plan) {
      return res.status(404).json({
        success: false,
        message: 'Savings plan not found'
      });
    }

    res.json({
      success: true,
      data: plan,
      message: 'Savings plan updated successfully'
    });
  } catch (error) {
    console.error('Error updating savings plan:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to update savings plan' 
    });
  }
});

// Add money to savings plan
router.post('/plans/:planId/deposit', auth, async (req, res) => {
  try {
    const { amount, description } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid amount is required'
      });
    }

    const plan = await SavingsPlan.findOne({
      _id: req.params.planId,
      userId: req.user.id
    });

    if (!plan) {
      return res.status(404).json({
        success: false,
        message: 'Savings plan not found'
      });
    }

    if (plan.status !== 'active') {
      return res.status(400).json({
        success: false,
        message: 'Cannot add money to inactive plan'
      });
    }

    // Add transaction
    await plan.addTransaction({
      type: 'deposit',
      amount: amount,
      description: description || `Deposit to ${plan.name}`,
      metadata: {
        source: 'manual_deposit',
        timestamp: new Date()
      }
    });

    // Check if plan should mature
    plan.checkMaturity();

    res.json({
      success: true,
      data: plan,
      message: 'Money added successfully'
    });
  } catch (error) {
    console.error('Error adding money to plan:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to add money to plan' 
    });
  }
});

// Pause/Resume savings plan
router.patch('/plans/:planId/status', auth, async (req, res) => {
  try {
    const { status } = req.body;

    if (!['active', 'paused'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status. Must be "active" or "paused"'
      });
    }

    const plan = await SavingsPlan.findOne({
      _id: req.params.planId,
      userId: req.user.id
    });

    if (!plan) {
      return res.status(404).json({
        success: false,
        message: 'Savings plan not found'
      });
    }

    plan.status = status;
    await plan.save();

    res.json({
      success: true,
      data: plan,
      message: `Savings plan ${status === 'active' ? 'resumed' : 'paused'} successfully`
    });
  } catch (error) {
    console.error('Error updating plan status:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to update plan status' 
    });
  }
});

// Get plan transactions
router.get('/plans/:planId/transactions', auth, async (req, res) => {
  try {
    const { page = 1, limit = 20, type } = req.query;

    const plan = await SavingsPlan.findOne({
      _id: req.params.planId,
      userId: req.user.id
    });

    if (!plan) {
      return res.status(404).json({
        success: false,
        message: 'Savings plan not found'
      });
    }

    let transactions = plan.transactions;

    // Filter by type if specified
    if (type) {
      transactions = transactions.filter(txn => txn.type === type);
    }

    // Sort by date (newest first)
    transactions.sort((a, b) => new Date(b.date) - new Date(a.date));

    // Paginate
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedTransactions = transactions.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: paginatedTransactions,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: transactions.length,
        pages: Math.ceil(transactions.length / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching transactions:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch transactions' 
    });
  }
});

// Calculate interest for all eligible plans
router.post('/calculate-interest', auth, async (req, res) => {
  try {
    const plans = await SavingsPlan.find({
      userId: req.user.id,
      status: 'active',
      interestRate: { $gt: 0 }
    });

    let totalInterestAdded = 0;

    for (const plan of plans) {
      const interest = plan.calculateInterest();
      
      if (interest > 0) {
        await plan.addTransaction({
          type: 'interest',
          amount: interest,
          description: 'Daily interest payment',
          metadata: {
            rate: plan.interestRate,
            calculation_date: new Date()
          }
        });
        
        totalInterestAdded += interest;
      }
    }

    res.json({
      success: true,
      data: {
        plansProcessed: plans.length,
        totalInterestAdded
      },
      message: 'Interest calculated and added successfully'
    });
  } catch (error) {
    console.error('Error calculating interest:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to calculate interest' 
    });
  }
});

// Get savings summary
router.get('/summary', auth, async (req, res) => {
  try {
    const plans = await SavingsPlan.find({ userId: req.user.id });

    const summary = {
      totalPlans: plans.length,
      activePlans: plans.filter(p => p.status === 'active').length,
      completedPlans: plans.filter(p => p.status === 'completed').length,
      totalSaved: plans.reduce((sum, p) => sum + p.currentAmount, 0),
      totalTarget: plans.reduce((sum, p) => sum + p.targetAmount, 0),
      totalInterestEarned: plans.reduce((sum, p) => sum + p.analytics.totalInterest, 0),
      plansByType: {},
      recentActivity: []
    };

    // Group by plan type
    plans.forEach(plan => {
      if (!summary.plansByType[plan.planType]) {
        summary.plansByType[plan.planType] = {
          count: 0,
          totalAmount: 0,
          activeCount: 0
        };
      }
      
      summary.plansByType[plan.planType].count++;
      summary.plansByType[plan.planType].totalAmount += plan.currentAmount;
      if (plan.status === 'active') {
        summary.plansByType[plan.planType].activeCount++;
      }
    });

    // Get recent activity
    const allTransactions = [];
    plans.forEach(plan => {
      plan.transactions.forEach(txn => {
        allTransactions.push({
          ...txn.toObject(),
          planName: plan.name,
          planType: plan.planType
        });
      });
    });

    summary.recentActivity = allTransactions
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, 10);

    res.json({
      success: true,
      data: summary
    });
  } catch (error) {
    console.error('Error fetching savings summary:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch savings summary' 
    });
  }
});

module.exports = router;