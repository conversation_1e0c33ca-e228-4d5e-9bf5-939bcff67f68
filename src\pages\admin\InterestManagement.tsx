import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Pencil, Plus, Trash2, Percent, DollarSign, AlertTriangle } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const InterestManagement = () => {
  const [interestRates, setInterestRates] = useState([
    { id: 1, planType: "Basic Savings", rate: 8.5, minAmount: 1000, maxAmount: 50000, duration: "Monthly" },
    { id: 2, planType: "Premium Savings", rate: 12.0, minAmount: 50000, maxAmount: 500000, duration: "Quarterly" },
    { id: 3, planType: "Fixed Deposit", rate: 15.5, minAmount: 100000, maxAmount: 1000000, duration: "Annually" },
  ]);

  const [planFees, setPlanFees] = useState([
    { id: 1, feeType: "Account Maintenance", amount: 500, frequency: "Monthly", isPercentage: false },
    { id: 2, feeType: "Transaction Fee", amount: 2.5, frequency: "Per Transaction", isPercentage: true },
    { id: 3, feeType: "Withdrawal Fee", amount: 100, frequency: "Per Withdrawal", isPercentage: false },
  ]);

  const [penalties, setPenalties] = useState([
    { id: 1, type: "Early Withdrawal", amount: 5.0, condition: "Before maturity", isPercentage: true },
    { id: 2, type: "Insufficient Balance", amount: 1000, condition: "Below minimum", isPercentage: false },
    { id: 3, type: "Late Payment", amount: 2.5, condition: "After due date", isPercentage: true },
  ]);

  const [editingItem, setEditingItem] = useState(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("interest");

  const handleSaveInterestRate = (formData) => {
    if (editingItem) {
      setInterestRates(rates => rates.map(rate => 
        rate.id === editingItem.id ? { ...formData, id: editingItem.id } : rate
      ));
      toast.success("Interest rate updated successfully");
    } else {
      setInterestRates(rates => [...rates, { ...formData, id: Date.now() }]);
      toast.success("Interest rate added successfully");
    }
    setIsDialogOpen(false);
    setEditingItem(null);
  };

  const handleSaveFee = (formData) => {
    if (editingItem) {
      setPlanFees(fees => fees.map(fee => 
        fee.id === editingItem.id ? { ...formData, id: editingItem.id } : fee
      ));
      toast.success("Fee updated successfully");
    } else {
      setPlanFees(fees => [...fees, { ...formData, id: Date.now() }]);
      toast.success("Fee added successfully");
    }
    setIsDialogOpen(false);
    setEditingItem(null);
  };

  const handleSavePenalty = (formData) => {
    if (editingItem) {
      setPenalties(penalties => penalties.map(penalty => 
        penalty.id === editingItem.id ? { ...formData, id: editingItem.id } : penalty
      ));
      toast.success("Penalty updated successfully");
    } else {
      setPenalties(penalties => [...penalties, { ...formData, id: Date.now() }]);
      toast.success("Penalty added successfully");
    }
    setIsDialogOpen(false);
    setEditingItem(null);
  };

  const InterestRateForm = () => {
    const [formData, setFormData] = useState(editingItem || {
      planType: "", rate: "", minAmount: "", maxAmount: "", duration: ""
    });

    return (
      <div className="space-y-4">
        <div>
          <Label htmlFor="planType">Plan Type</Label>
          <Input
            id="planType"
            value={formData.planType}
            onChange={(e) => setFormData({...formData, planType: e.target.value})}
            placeholder="e.g., Basic Savings"
          />
        </div>
        <div>
          <Label htmlFor="rate">Interest Rate (%)</Label>
          <Input
            id="rate"
            type="number"
            step="0.1"
            value={formData.rate}
            onChange={(e) => setFormData({...formData, rate: parseFloat(e.target.value)})}
            placeholder="8.5"
          />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="minAmount">Minimum Amount (₦)</Label>
            <Input
              id="minAmount"
              type="number"
              value={formData.minAmount}
              onChange={(e) => setFormData({...formData, minAmount: parseInt(e.target.value)})}
              placeholder="1000"
            />
          </div>
          <div>
            <Label htmlFor="maxAmount">Maximum Amount (₦)</Label>
            <Input
              id="maxAmount"
              type="number"
              value={formData.maxAmount}
              onChange={(e) => setFormData({...formData, maxAmount: parseInt(e.target.value)})}
              placeholder="50000"
            />
          </div>
        </div>
        <div>
          <Label htmlFor="duration">Duration</Label>
          <Select value={formData.duration} onValueChange={(value) => setFormData({...formData, duration: value})}>
            <SelectTrigger>
              <SelectValue placeholder="Select duration" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Monthly">Monthly</SelectItem>
              <SelectItem value="Quarterly">Quarterly</SelectItem>
              <SelectItem value="Annually">Annually</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button onClick={() => handleSaveInterestRate(formData)} className="w-full">
          {editingItem ? "Update" : "Add"} Interest Rate
        </Button>
      </div>
    );
  };

  const FeeForm = () => {
    const [formData, setFormData] = useState(editingItem || {
      feeType: "", amount: "", frequency: "", isPercentage: false
    });

    return (
      <div className="space-y-4">
        <div>
          <Label htmlFor="feeType">Fee Type</Label>
          <Input
            id="feeType"
            value={formData.feeType}
            onChange={(e) => setFormData({...formData, feeType: e.target.value})}
            placeholder="e.g., Account Maintenance"
          />
        </div>
        <div>
          <Label htmlFor="amount">Amount</Label>
          <Input
            id="amount"
            type="number"
            step="0.1"
            value={formData.amount}
            onChange={(e) => setFormData({...formData, amount: parseFloat(e.target.value)})}
            placeholder="500"
          />
        </div>
        <div>
          <Label htmlFor="frequency">Frequency</Label>
          <Select value={formData.frequency} onValueChange={(value) => setFormData({...formData, frequency: value})}>
            <SelectTrigger>
              <SelectValue placeholder="Select frequency" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Monthly">Monthly</SelectItem>
              <SelectItem value="Per Transaction">Per Transaction</SelectItem>
              <SelectItem value="Per Withdrawal">Per Withdrawal</SelectItem>
              <SelectItem value="One-time">One-time</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="isPercentage"
            checked={formData.isPercentage}
            onChange={(e) => setFormData({...formData, isPercentage: e.target.checked})}
          />
          <Label htmlFor="isPercentage">Is Percentage</Label>
        </div>
        <Button onClick={() => handleSaveFee(formData)} className="w-full">
          {editingItem ? "Update" : "Add"} Fee
        </Button>
      </div>
    );
  };

  const PenaltyForm = () => {
    const [formData, setFormData] = useState(editingItem || {
      type: "", amount: "", condition: "", isPercentage: false
    });

    return (
      <div className="space-y-4">
        <div>
          <Label htmlFor="type">Penalty Type</Label>
          <Input
            id="type"
            value={formData.type}
            onChange={(e) => setFormData({...formData, type: e.target.value})}
            placeholder="e.g., Early Withdrawal"
          />
        </div>
        <div>
          <Label htmlFor="amount">Amount</Label>
          <Input
            id="amount"
            type="number"
            step="0.1"
            value={formData.amount}
            onChange={(e) => setFormData({...formData, amount: parseFloat(e.target.value)})}
            placeholder="5.0"
          />
        </div>
        <div>
          <Label htmlFor="condition">Condition</Label>
          <Input
            id="condition"
            value={formData.condition}
            onChange={(e) => setFormData({...formData, condition: e.target.value})}
            placeholder="e.g., Before maturity"
          />
        </div>
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="isPercentage"
            checked={formData.isPercentage}
            onChange={(e) => setFormData({...formData, isPercentage: e.target.checked})}
          />
          <Label htmlFor="isPercentage">Is Percentage</Label>
        </div>
        <Button onClick={() => handleSavePenalty(formData)} className="w-full">
          {editingItem ? "Update" : "Add"} Penalty
        </Button>
      </div>
    );
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Interest Management</h1>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="interest" className="flex items-center gap-2">
            <Percent className="h-4 w-4" />
            Interest Rates
          </TabsTrigger>
          <TabsTrigger value="fees" className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Plan Fees
          </TabsTrigger>
          <TabsTrigger value="penalties" className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Penalties
          </TabsTrigger>
        </TabsList>

        <TabsContent value="interest">
          <Card className="border-2 border-green-600/20 dark:border-green-400/20 rounded-none">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Interest Rates Management</CardTitle>
              <Dialog open={isDialogOpen && activeTab === "interest"} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button onClick={() => {setEditingItem(null); setIsDialogOpen(true);}}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Interest Rate
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>{editingItem ? "Edit" : "Add"} Interest Rate</DialogTitle>
                  </DialogHeader>
                  <InterestRateForm />
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Plan Type</TableHead>
                    <TableHead>Rate (%)</TableHead>
                    <TableHead>Min Amount</TableHead>
                    <TableHead>Max Amount</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {interestRates.map((rate) => (
                    <TableRow key={rate.id}>
                      <TableCell>{rate.planType}</TableCell>
                      <TableCell>
                        <Badge variant="secondary">{rate.rate}%</Badge>
                      </TableCell>
                      <TableCell>₦{rate.minAmount.toLocaleString()}</TableCell>
                      <TableCell>₦{rate.maxAmount.toLocaleString()}</TableCell>
                      <TableCell>{rate.duration}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {setEditingItem(rate); setIsDialogOpen(true);}}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="destructive" 
                            size="sm"
                            onClick={() => {
                              setInterestRates(rates => rates.filter(r => r.id !== rate.id));
                              toast.success("Interest rate deleted");
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="fees">
          <Card className="border-2 border-green-600/20 dark:border-green-400/20 rounded-none">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Plan Fees Management</CardTitle>
              <Dialog open={isDialogOpen && activeTab === "fees"} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button onClick={() => {setEditingItem(null); setIsDialogOpen(true);}}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Fee
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>{editingItem ? "Edit" : "Add"} Fee</DialogTitle>
                  </DialogHeader>
                  <FeeForm />
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Fee Type</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Frequency</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {planFees.map((fee) => (
                    <TableRow key={fee.id}>
                      <TableCell>{fee.feeType}</TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          {fee.isPercentage ? `${fee.amount}%` : `₦${fee.amount}`}
                        </Badge>
                      </TableCell>
                      <TableCell>{fee.frequency}</TableCell>
                      <TableCell>
                        <Badge variant={fee.isPercentage ? "default" : "outline"}>
                          {fee.isPercentage ? "Percentage" : "Fixed"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {setEditingItem(fee); setIsDialogOpen(true);}}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="destructive" 
                            size="sm"
                            onClick={() => {
                              setPlanFees(fees => fees.filter(f => f.id !== fee.id));
                              toast.success("Fee deleted");
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="penalties">
          <Card className="border-2 border-green-600/20 dark:border-green-400/20 rounded-none">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Penalties Management</CardTitle>
              <Dialog open={isDialogOpen && activeTab === "penalties"} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button onClick={() => {setEditingItem(null); setIsDialogOpen(true);}}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Penalty
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>{editingItem ? "Edit" : "Add"} Penalty</DialogTitle>
                  </DialogHeader>
                  <PenaltyForm />
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Penalty Type</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Condition</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {penalties.map((penalty) => (
                    <TableRow key={penalty.id}>
                      <TableCell>{penalty.type}</TableCell>
                      <TableCell>
                        <Badge variant="destructive">
                          {penalty.isPercentage ? `${penalty.amount}%` : `₦${penalty.amount}`}
                        </Badge>
                      </TableCell>
                      <TableCell>{penalty.condition}</TableCell>
                      <TableCell>
                        <Badge variant={penalty.isPercentage ? "default" : "outline"}>
                          {penalty.isPercentage ? "Percentage" : "Fixed"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {setEditingItem(penalty); setIsDialogOpen(true);}}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="destructive" 
                            size="sm"
                            onClick={() => {
                              setPenalties(penalties => penalties.filter(p => p.id !== penalty.id));
                              toast.success("Penalty deleted");
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default InterestManagement;