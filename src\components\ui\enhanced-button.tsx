
import { useState } from "react";
import { ArrowRight } from "lucide-react";
import { cn } from "@/lib/utils";

interface EnhancedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  className?: string;
  variant?: "default" | "outline" | "accent" | "glass";
  size?: "sm" | "md" | "lg";
  loading?: boolean;
}

export function EnhancedButton({ 
  children, 
  className, 
  variant = "default",
  size = "md",
  loading = false,
  ...props 
}: EnhancedButtonProps) {
  const [isPressed, setIsPressed] = useState(false);

  return (
    <button
      className={cn(
        // Base 3D button styles with enhanced border radius
        "relative overflow-hidden transition-all duration-200 group",
        "rounded-[24px] border-2 border-primary/30",
        "transform-gpu will-change-transform",
        
        // Enhanced 3D shadow effects
        "shadow-[0_8px_24px_rgba(0,0,0,0.12),inset_0_1px_0_rgba(255,255,255,0.1)]",
        "hover:shadow-[0_12px_32px_rgba(0,0,0,0.18),inset_0_2px_0_rgba(255,255,255,0.15)]",
        "active:shadow-[0_4px_16px_rgba(0,0,0,0.2),inset_0_4px_8px_rgba(0,0,0,0.1)]",
        
        // 3D transform effects
        "hover:translate-y-[-2px] active:translate-y-[1px]",
        "hover:scale-[1.02] active:scale-[0.98]",
        
        // Background gradients
        "bg-gradient-to-b from-primary/10 via-primary/5 to-primary/20",
        "hover:from-primary/15 hover:via-primary/8 hover:to-primary/25",
        "active:from-primary/20 active:via-primary/15 active:to-primary/30",
        
        // Dark mode support
        "dark:from-primary/20 dark:via-primary/10 dark:to-primary/30",
        "dark:hover:from-primary/25 dark:hover:via-primary/15 dark:hover:to-primary/35",
        
        // Mobile responsiveness
        "flex items-center justify-between gap-2 sm:gap-4",
        "min-h-[48px] sm:min-h-[52px] lg:min-h-[56px]",
        "touch-manipulation select-none",
        
        // Focus states
        "focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2",
        
        // Size variants with mobile-first approach
        size === "sm" && "px-3 py-2 text-sm sm:px-4 sm:py-2",
        size === "md" && "px-4 py-3 text-base sm:px-6 sm:py-3",
        size === "lg" && "px-6 py-4 text-lg sm:px-8 sm:py-4",
        
        // Variant styles
        variant === "accent" && "border-accent bg-gradient-to-b from-accent/10 to-accent/20",
        variant === "outline" && "bg-transparent border-primary hover:bg-primary/5",
        variant === "glass" && "bg-white/10 backdrop-blur-md border-white/20 hover:bg-white/20",
        
        // Loading state
        loading && "pointer-events-none opacity-75",
        
        className
      )}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onMouseLeave={() => setIsPressed(false)}
      disabled={loading}
      {...props}
    >
      {/* Enhanced shimmer effect */}
      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 rounded-[22px]" />
      </div>
      
      {/* Content with proper spacing */}
      <span className="flex-1 text-left font-medium text-foreground relative z-10 truncate">
        {loading ? (
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            <span className="hidden sm:inline">Loading...</span>
          </div>
        ) : (
          children
        )}
      </span>
      
      {/* Enhanced arrow circle with better mobile sizing */}
      <div className={cn(
        "relative z-10 flex items-center justify-center transition-all duration-200",
        "w-8 h-8 sm:w-10 sm:h-10 rounded-full",
        "bg-primary/20 border border-primary/40",
        "group-hover:bg-primary/30 group-hover:border-primary/60",
        "group-active:bg-primary/40",
        isPressed && "scale-90"
      )}>
        <ArrowRight className={cn(
          "text-primary transition-transform duration-200",
          "w-4 h-4 sm:w-5 sm:h-5",
          "group-hover:translate-x-0.5 group-active:translate-x-1"
        )} />
      </div>
    </button>
  );
}
