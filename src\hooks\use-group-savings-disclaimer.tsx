import React, { useState } from 'react';
import { GroupSavingsDisclaimer } from '@/components/legal/GroupSavingsDisclaimer';

// Hook to manage group savings disclaimer
export const useGroupSavingsDisclaimer = () => {
  const [showDisclaimer, setShowDisclaimer] = useState(false);

  const showDisclaimerModal = () => setShowDisclaimer(true);
  const hideDisclaimerModal = () => setShowDisclaimer(false);

  const DisclaimerModal = ({ groupName, onAccept }: { groupName?: string; onAccept: () => void }) => (
    <GroupSavingsDisclaimer
      open={showDisclaimer}
      onOpenChange={setShowDisclaimer}
      onAccept={onAccept}
      groupName={groupName}
    />
  );

  return {
    showDisclaimerModal,
    hideDisclaimerModal,
    DisclaimerModal
  };
};