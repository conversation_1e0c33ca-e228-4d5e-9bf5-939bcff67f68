import { ExternalLink, FileText, Scale, Shield } from 'lucide-react';
import { Link } from 'react-router-dom';

export const LegalFooterLinks = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-card border-t mt-auto">
      <div className="container mx-auto px-4 py-6">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          {/* Company Info */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <img
                src="/lovable-uploads/371e0f57-a280-42c0-8e1b-50aee7ebddc6.png"
                alt="Better Interest Logo"
                className="h-6 w-6"
              />
              <span className="font-semibold">Better Interest</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Secure digital savings with better returns. Building financial futures together.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-medium mb-3">Quick Links</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link to="/dashboard" className="text-muted-foreground hover:text-primary transition-colors">
                  Dashboard
                </Link>
              </li>
              <li>
                <Link to="/savings" className="text-muted-foreground hover:text-primary transition-colors">
                  Savings Plans
                </Link>
              </li>
              <li>
                <Link to="/analytics" className="text-muted-foreground hover:text-primary transition-colors">
                  Analytics
                </Link>
              </li>
              <li>
                <Link to="/settings" className="text-muted-foreground hover:text-primary transition-colors">
                  Settings
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="font-medium mb-3">Support</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <a href="mailto:<EMAIL>" className="text-muted-foreground hover:text-primary transition-colors">
                  Help Center
                </a>
              </li>
              <li>
                <a href="mailto:<EMAIL>" className="text-muted-foreground hover:text-primary transition-colors">
                  Contact Support
                </a>
              </li>
              <li>
                <a href="tel:+2348000BETTER" className="text-muted-foreground hover:text-primary transition-colors">
                  Call Us
                </a>
              </li>
              <li>
                <Link to="/kyc" className="text-muted-foreground hover:text-primary transition-colors">
                  KYC Verification
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal */}
          <div>
            <h4 className="font-medium mb-3">Legal</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link 
                  to="/legal" 
                  className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-1"
                >
                  <FileText className="h-3 w-3" />
                  Legal Center
                </Link>
              </li>
              <li>
                <Link 
                  to="/legal" 
                  className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-1"
                >
                  <Shield className="h-3 w-3" />
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link 
                  to="/legal" 
                  className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-1"
                >
                  <Scale className="h-3 w-3" />
                  Terms of Service
                </Link>
              </li>
              <li>
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-1"
                >
                  <ExternalLink className="h-3 w-3" />
                  Legal Inquiries
                </a>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t pt-4">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div className="flex flex-wrap items-center gap-4 text-xs text-muted-foreground">
              <span>© {currentYear} Better Interest. All rights reserved.</span>
              <span>•</span>
              <span>Licensed by CBN</span>
              <span>•</span>
              <span>NDPR Compliant</span>
            </div>
            
            <div className="flex items-center gap-4">
              <Link 
                to="/legal" 
                className="text-xs text-muted-foreground hover:text-primary transition-colors flex items-center gap-1"
              >
                <Shield className="h-3 w-3" />
                Legal Information
              </Link>
              <a 
                href="mailto:<EMAIL>" 
                className="text-xs text-muted-foreground hover:text-primary transition-colors"
              >
                Report Issue
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
