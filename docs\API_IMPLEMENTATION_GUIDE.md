# API Implementation Guide

## Overview
This guide provides detailed specifications for implementing the required API endpoints for the Better Interest application.

## Authentication Endpoints

### POST /api/v1/auth/register
```javascript
// Request Body
{
  "firstName": "string",
  "lastName": "string", 
  "email": "string",
  "password": "string",
  "phone": "string"
}

// Response
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "firstName": "string",
      "lastName": "string",
      "email": "string",
      "role": "user",
      "kycStatus": "pending"
    },
    "token": "string",
    "refreshToken": "string"
  }
}
```

### POST /api/v1/auth/login
```javascript
// Request Body
{
  "email": "string",
  "password": "string"
}

// Response - Same as register
```

### POST /api/v1/auth/forgot-password
```javascript
// Request Body
{
  "email": "string"
}

// Response
{
  "success": true,
  "message": "Password reset email sent"
}
```

### POST /api/v1/auth/reset-password
```javascript
// Request Body
{
  "token": "string",
  "newPassword": "string"
}

// Response
{
  "success": true,
  "message": "Password reset successful"
}
```

### POST /api/v1/auth/change-password
```javascript
// Request Body
{
  "currentPassword": "string",
  "newPassword": "string"
}

// Response
{
  "success": true,
  "message": "Password changed successfully"
}
```

## User Management Endpoints

### GET /api/v1/user/profile
```javascript
// Headers: Authorization: Bearer <token>
// Response
{
  "success": true,
  "data": {
    "id": "string",
    "firstName": "string",
    "lastName": "string",
    "email": "string",
    "phone": "string",
    "address": "string",
    "kycStatus": "pending|verified|rejected",
    "balance": "number",
    "createdAt": "string",
    "updatedAt": "string"
  }
}
```

### PUT /api/v1/user/profile
```javascript
// Request Body
{
  "firstName": "string",
  "lastName": "string",
  "phone": "string",
  "address": "string"
}

// Response
{
  "success": true,
  "data": "updated user object"
}
```

## Savings Plans Endpoints

### GET /api/v1/savings-plans
```javascript
// Response
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "string",
      "description": "string",
      "minimumAmount": "number",
      "interestRate": "number",
      "durationDays": "number",
      "isActive": "boolean",
      "category": "string"
    }
  ]
}
```

### POST /api/v1/savings-plans
```javascript
// Request Body
{
  "planId": "string",
  "targetAmount": "number",
  "duration": "number",
  "frequency": "daily|weekly|monthly"
}

// Response
{
  "success": true,
  "data": {
    "id": "string",
    "userId": "string",
    "planId": "string",
    "targetAmount": "number",
    "savedAmount": "number",
    "status": "active",
    "createdAt": "string"
  }
}
```

## Fixed Deposit Endpoints

### POST /api/v1/fixed-deposits
```javascript
// Request Body
{
  "amount": "number",
  "duration": "number", // in days
  "autoRenewal": "boolean"
}

// Response
{
  "success": true,
  "data": {
    "id": "string",
    "userId": "string",
    "amount": "number",
    "duration": "number",
    "interestRate": "number",
    "startDate": "string",
    "maturityDate": "string",
    "status": "active"
  }
}
```

### GET /api/v1/fixed-deposits
```javascript
// Response
{
  "success": true,
  "data": [
    {
      "id": "string",
      "amount": "number",
      "duration": "number",
      "interestRate": "number",
      "accruedInterest": "number",
      "startDate": "string",
      "maturityDate": "string",
      "status": "active|matured|broken"
    }
  ]
}
```

### POST /api/v1/fixed-deposits/:id/break
```javascript
// Response
{
  "success": true,
  "data": {
    "principalAmount": "number",
    "interestEarned": "number",
    "penaltyAmount": "number",
    "netAmount": "number"
  }
}
```

## Flex Savings Endpoints

### GET /api/v1/flex-savings
```javascript
// Response
{
  "success": true,
  "data": {
    "balance": "number",
    "dailyInterestRate": "number",
    "totalInterestEarned": "number",
    "lastInterestCalculation": "string"
  }
}
```

### POST /api/v1/flex-savings/deposit
```javascript
// Request Body
{
  "amount": "number"
}

// Response
{
  "success": true,
  "data": {
    "transactionId": "string",
    "amount": "number",
    "newBalance": "number"
  }
}
```

### POST /api/v1/flex-savings/withdraw
```javascript
// Request Body
{
  "amount": "number"
}

// Response - Same as deposit
```

## SafeLock Endpoints

### POST /api/v1/safelock
```javascript
// Request Body
{
  "amount": "number",
  "lockDuration": "number", // in days
  "canBreak": "boolean"
}

// Response
{
  "success": true,
  "data": {
    "id": "string",
    "amount": "number",
    "lockDuration": "number",
    "interestRate": "number",
    "maturityDate": "string",
    "canBreak": "boolean",
    "status": "active"
  }
}
```

### GET /api/v1/safelock
```javascript
// Response
{
  "success": true,
  "data": [
    {
      "id": "string",
      "amount": "number",
      "lockDuration": "number",
      "interestRate": "number",
      "startDate": "string",
      "maturityDate": "string",
      "status": "active|matured|broken"
    }
  ]
}
```

## AutoSave Endpoints

### POST /api/v1/autosave/setup
```javascript
// Request Body
{
  "cardId": "string",
  "frequency": "daily|weekly|monthly",
  "amount": "number",
  "targetPlanId": "string"
}

// Response
{
  "success": true,
  "data": {
    "id": "string",
    "frequency": "string",
    "amount": "number",
    "nextDebitDate": "string",
    "isActive": "boolean"
  }
}
```

### GET /api/v1/autosave
```javascript
// Response
{
  "success": true,
  "data": [
    {
      "id": "string",
      "frequency": "string",
      "amount": "number",
      "nextDebitDate": "string",
      "isActive": "boolean",
      "lastSuccessfulDebit": "string"
    }
  ]
}
```

### PUT /api/v1/autosave/:id/toggle
```javascript
// Response
{
  "success": true,
  "data": {
    "id": "string",
    "isActive": "boolean"
  }
}
```

## Investment Endpoints

### GET /api/v1/investments/products
```javascript
// Response
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "string",
      "type": "treasury_bills|mutual_funds|bonds",
      "minimumAmount": "number",
      "expectedReturn": "number",
      "riskLevel": "low|medium|high",
      "duration": "number"
    }
  ]
}
```

### POST /api/v1/investments/buy
```javascript
// Request Body
{
  "productId": "string",
  "amount": "number"
}

// Response
{
  "success": true,
  "data": {
    "id": "string",
    "productId": "string",
    "amount": "number",
    "units": "number",
    "unitPrice": "number",
    "purchaseDate": "string"
  }
}
```

### GET /api/v1/investments/portfolio
```javascript
// Response
{
  "success": true,
  "data": {
    "totalValue": "number",
    "totalReturns": "number",
    "investments": [
      {
        "id": "string",
        "productName": "string",
        "amount": "number",
        "currentValue": "number",
        "returns": "number",
        "returnPercentage": "number"
      }
    ]
  }
}
```

## Group Savings Endpoints

### POST /api/v1/group-savings
```javascript
// Request Body
{
  "name": "string",
  "description": "string",
  "targetAmount": "number",
  "contributionAmount": "number",
  "frequency": "daily|weekly|monthly",
  "duration": "number",
  "maxMembers": "number"
}

// Response
{
  "success": true,
  "data": {
    "id": "string",
    "name": "string",
    "inviteCode": "string",
    "targetAmount": "number",
    "currentAmount": "number",
    "memberCount": "number",
    "status": "active"
  }
}
```

### GET /api/v1/group-savings
```javascript
// Response
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "string",
      "targetAmount": "number",
      "currentAmount": "number",
      "memberCount": "number",
      "role": "admin|member",
      "status": "active|completed|cancelled"
    }
  ]
}
```

### POST /api/v1/group-savings/join
```javascript
// Request Body
{
  "inviteCode": "string"
}

// Response
{
  "success": true,
  "data": {
    "groupId": "string",
    "role": "member",
    "joinedAt": "string"
  }
}
```

## Bill Payment Endpoints

### GET /api/v1/bills/providers/:type
```javascript
// Response
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "string",
      "type": "airtime|data|electricity|cable_tv",
      "logo": "string",
      "isActive": "boolean"
    }
  ]
}
```

### POST /api/v1/bills/verify-customer
```javascript
// Request Body
{
  "providerId": "string",
  "customerIdentifier": "string",
  "category": "string"
}

// Response
{
  "success": true,
  "data": {
    "customerName": "string",
    "address": "string",
    "isValid": "boolean"
  }
}
```

### POST /api/v1/bills/pay
```javascript
// Request Body
{
  "providerId": "string",
  "customerIdentifier": "string",
  "amount": "number",
  "category": "string"
}

// Response
{
  "success": true,
  "data": {
    "reference": "string",
    "status": "successful",
    "amount": "number",
    "commission": "number"
  }
}
```

## Transaction Endpoints

### GET /api/v1/transactions/recent
```javascript
// Query: ?limit=10
// Response
{
  "success": true,
  "data": [
    {
      "id": "string",
      "type": "deposit|withdrawal|transfer",
      "amount": "number",
      "description": "string",
      "status": "completed|pending|failed",
      "createdAt": "string",
      "reference": "string"
    }
  ]
}
```

### GET /api/v1/transactions
```javascript
// Query: ?page=1&limit=20&type=deposit&status=completed
// Response
{
  "success": true,
  "data": {
    "transactions": [],
    "pagination": {
      "page": "number",
      "limit": "number",
      "total": "number",
      "pages": "number"
    }
  }
}
```

## Payment Methods Endpoints

### GET /api/v1/payments/methods
```javascript
// Response
{
  "success": true,
  "data": [
    {
      "id": "string",
      "type": "card|bank_transfer",
      "name": "string",
      "details": {
        "last4": "string",
        "brand": "string",
        "bank": "string"
      },
      "isDefault": "boolean",
      "isActive": "boolean"
    }
  ]
}
```

### POST /api/v1/payments/methods
```javascript
// Request Body
{
  "type": "card",
  "cardToken": "string"
}

// Response
{
  "success": true,
  "data": {
    "id": "string",
    "type": "card",
    "last4": "string",
    "brand": "string",
    "isDefault": "boolean"
  }
}
```

## Referral Endpoints

### GET /api/v1/referrals/code
```javascript
// Response
{
  "success": true,
  "data": {
    "referralCode": "string",
    "totalReferrals": "number",
    "totalEarnings": "number"
  }
}
```

### POST /api/v1/referrals/use-code
```javascript
// Request Body
{
  "referralCode": "string"
}

// Response
{
  "success": true,
  "data": {
    "referrerReward": "number",
    "refereeReward": "number",
    "status": "pending|qualified"
  }
}
```

## Admin Endpoints

### GET /api/v1/admin/users
```javascript
// Query: ?page=1&limit=20&status=active&search=john
// Response
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "string",
        "firstName": "string",
        "lastName": "string",
        "email": "string",
        "phone": "string",
        "kycStatus": "string",
        "balance": "number",
        "status": "active|suspended",
        "createdAt": "string"
      }
    ],
    "pagination": {
      "page": "number",
      "limit": "number",
      "total": "number"
    }
  }
}
```

### GET /api/v1/admin/users/:id
```javascript
// Response
{
  "success": true,
  "data": {
    "id": "string",
    "firstName": "string",
    "lastName": "string",
    "email": "string",
    "phone": "string",
    "address": "string",
    "kycStatus": "string",
    "balance": "number",
    "totalSavings": "number",
    "totalWithdrawals": "number",
    "status": "string",
    "createdAt": "string",
    "lastLogin": "string"
  }
}
```

### GET /api/v1/admin/savings-plans
```javascript
// Response
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "string",
      "description": "string",
      "minimumAmount": "number",
      "interestRate": "number",
      "durationDays": "number",
      "isActive": "boolean",
      "usersCount": "number",
      "totalBalance": "number"
    }
  ]
}
```

### GET /api/v1/admin/analytics/platform-stats
```javascript
// Response
{
  "success": true,
  "data": {
    "activeUsers": "string",
    "totalSavings": "string",
    "maxReturns": "string",
    "uptime": "string",
    "totalTransactions": "number",
    "monthlyGrowth": "number"
  }
}
```

## Testimonials Endpoints

### GET /api/v1/testimonials/featured
```javascript
// Response
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "string",
      "role": "string",
      "content": "string",
      "rating": "number",
      "image": "string",
      "amount": "string"
    }
  ]
}
```

## Interest Calculation Endpoints

### POST /api/v1/interest/calculate
```javascript
// Admin only endpoint
// Response
{
  "success": true,
  "data": {
    "processedAccounts": "number",
    "totalInterestPaid": "number",
    "calculationDate": "string"
  }
}
```

## Error Response Format

All endpoints should return errors in this format:

```javascript
{
  "success": false,
  "error": {
    "code": "string",
    "message": "string",
    "details": "object" // optional
  }
}
```

## Common HTTP Status Codes

- 200: Success
- 201: Created
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 422: Validation Error
- 500: Internal Server Error
```
