# ===========================================
# BETTER INTEREST - FRONTEND PRODUCTION ENV
# ===========================================

# API Configuration
VITE_API_URL=https://api.kojaonline.store/api/v1
VITE_API_TIMEOUT=30000
VITE_BACKEND_URL=https://api.kojaonline.store
VITE_FRONTEND_URL=https://demo.kojaonline.store

# Application Configuration
VITE_APP_NAME=Better Interest
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Secure Digital Savings Platform
VITE_PORT=443

# Payment Integration
VITE_PAYSTACK_PUBLIC_KEY=pk_test_44f6dbb6159b73b84fff2fc441dcdd997e15c10d
VITE_PAYSTACK_SECRET_KEY=sk_test_8f19d29fa6205d27a4a254667515c788798842b9
VITE_PAYSTACK_BASE_URL=https://api.paystack.co
VITE_PAYSTACK_CALLBACK_URL=https://demo.kojaonline.store/payment/callback

# Feature Flags
VITE_DEMO_MODE=false
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_BILL_PAYMENTS=true
VITE_ENABLE_GROUP_SAVINGS=true
VITE_ENABLE_FIXED_DEPOSITS=true
VITE_ENABLE_INVESTMENTS=true

# Security Configuration
VITE_ENABLE_2FA=true
VITE_SESSION_TIMEOUT=1800000
VITE_MAX_LOGIN_ATTEMPTS=3

# File Upload Configuration
VITE_MAX_FILE_SIZE=5242880
VITE_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# External Services
VITE_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
VITE_SENTRY_DSN=https://<EMAIL>/project-id
VITE_CRISP_WEBSITE_ID=your-crisp-website-id

# Production Configuration
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=error

# ===========================================
# BACKEND PRODUCTION ENVIRONMENT
# ===========================================

# Application Configuration
NODE_ENV=production
PORT=5000

# Database Configuration
MONGO_URI=*************************************************************************
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=your_secure_mongo_password

# Redis Configuration
REDIS_URL=redis://:your_redis_password@redis:6379
REDIS_PASSWORD=your_secure_redis_password

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_key_here_minimum_32_characters
JWT_EXPIRES_IN=7d

# Paystack Configuration
PAYSTACK_SECRET_KEY=sk_live_your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=pk_live_your_paystack_public_key

# CORS Configuration
CORS_ORIGIN=https://yourdomain.com
FRONTEND_URL=https://yourdomain.com

# Email Service Configuration (Choose one)
# SendGrid
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>

# Mailgun
MAILGUN_API_KEY=your_mailgun_api_key
MAILGUN_DOMAIN=yourdomain.com

# SMS Service Configuration (Choose one)
# Twilio
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# Termii
TERMII_API_KEY=your_termii_api_key
TERMII_SENDER_ID=YourApp

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Monitoring Configuration
GRAFANA_ADMIN_PASSWORD=your_secure_grafana_password

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# Interest Calculation Configuration
INTEREST_CALCULATION_CRON=0 0 * * *
MATURITY_CHECK_CRON=0 */1 * * *

# Fixed Deposit Configuration
FD_MINIMUM_AMOUNT=10000
FD_MAXIMUM_AMOUNT=********
FD_PENALTY_RATE=25

# Investment Configuration
INVESTMENT_MINIMUM_AMOUNT=5000
INVESTMENT_MAXIMUM_AMOUNT=5000000

# Loan Configuration
LOAN_MINIMUM_AMOUNT=5000
LOAN_MAXIMUM_AMOUNT=500000
LOAN_DEFAULT_INTEREST_RATE=15
LOAN_COLLATERAL_PERCENTAGE=80

# Bill Payment Configuration
BILL_PAYMENT_PROVIDER=paystack
BILL_PAYMENT_COMMISSION_RATE=1.5

# Referral Configuration
REFERRAL_REWARD_AMOUNT=1000
REFERRAL_REFEREE_REWARD=500
REFERRAL_MINIMUM_SAVINGS=5000

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1

# SSL Configuration
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/private.key

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=5

# Performance Configuration
MAX_CONCURRENT_REQUESTS=1000
REQUEST_TIMEOUT=30000
KEEP_ALIVE_TIMEOUT=5000

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=100

# Session Configuration
SESSION_SECRET=your_secure_session_secret
SESSION_MAX_AGE=********

# API Rate Limiting
API_RATE_LIMIT_WINDOW=15
API_RATE_LIMIT_MAX=100

# Webhook Configuration
WEBHOOK_SECRET=your_webhook_secret_key

# Third-party Integrations
# Credit Bureau API
CREDIT_BUREAU_API_KEY=your_credit_bureau_api_key
CREDIT_BUREAU_BASE_URL=https://api.creditbureau.com

# Bank Verification API
BVN_VERIFICATION_API_KEY=your_bvn_api_key
BVN_VERIFICATION_BASE_URL=https://api.bvnverification.com

# Push Notification Configuration
FCM_SERVER_KEY=your_fcm_server_key
FCM_SENDER_ID=your_fcm_sender_id

# Analytics Configuration
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
MIXPANEL_TOKEN=your_mixpanel_token

# Error Tracking
SENTRY_DSN=https://<EMAIL>/project_id

# Feature Flags
ENABLE_INVESTMENTS=true
ENABLE_LOANS=true
ENABLE_BILL_PAYMENTS=true
ENABLE_GROUP_SAVINGS=true
ENABLE_FIXED_DEPOSITS=true
ENABLE_REFERRALS=true

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="We are currently performing scheduled maintenance. Please try again later."
