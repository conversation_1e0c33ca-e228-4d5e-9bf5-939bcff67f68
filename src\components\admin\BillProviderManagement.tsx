import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { Plus, Edit, Trash2, Eye, EyeOff, Settings, Phone, Zap, Tv, Wifi, Plane, GraduationCap } from 'lucide-react';
import axios from 'axios';

interface BillProvider {
  _id: string;
  name: string;
  category: string;
  provider: string;
  apiKey: string;
  secretKey: string;
  baseUrl: string;
  isActive: boolean;
  fee: number;
  feeType: 'fixed' | 'percentage';
  minAmount: number;
  maxAmount: number;
  configurations: Record<string, string>;
}

const categoryIcons = {
  airtime: Phone,
  data: Wifi,
  electricity: Zap,
  cable_tv: Tv,
  internet: Wifi,
  flight: Plane,
  education: GraduationCap,
  betting: Phone
};

const providerTypes = [
  'paystack',
  'flutterwave',
  'vtpass',
  'interswitch',
  'remita'
];

const categories = [
  'airtime',
  'data',
  'electricity',
  'cable_tv',
  'internet',
  'flight',
  'betting',
  'education'
];

export const BillProviderManagement = () => {
  const [providers, setProviders] = useState<BillProvider[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingProvider, setEditingProvider] = useState<BillProvider | null>(null);
  const [showSecrets, setShowSecrets] = useState<Record<string, boolean>>({});
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    provider: '',
    apiKey: '',
    secretKey: '',
    baseUrl: '',
    isActive: true,
    fee: 0,
    feeType: 'fixed' as 'fixed' | 'percentage',
    minAmount: 0,
    maxAmount: 1000000,
    configurations: ''
  });
  const { toast } = useToast();

  useEffect(() => {
    fetchProviders();
  }, []);

  const fetchProviders = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/v1/bills/admin/providers');
      setProviders(response.data.data);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch providers',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      category: '',
      provider: '',
      apiKey: '',
      secretKey: '',
      baseUrl: '',
      isActive: true,
      fee: 0,
      feeType: 'fixed',
      minAmount: 0,
      maxAmount: 1000000,
      configurations: ''
    });
    setEditingProvider(null);
  };

  const handleEdit = (provider: BillProvider) => {
    setEditingProvider(provider);
    setFormData({
      name: provider.name,
      category: provider.category,
      provider: provider.provider,
      apiKey: provider.apiKey,
      secretKey: provider.secretKey,
      baseUrl: provider.baseUrl,
      isActive: provider.isActive,
      fee: provider.fee,
      feeType: provider.feeType,
      minAmount: provider.minAmount,
      maxAmount: provider.maxAmount,
      configurations: JSON.stringify(provider.configurations, null, 2)
    });
    setIsDialogOpen(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const payload = {
        ...formData,
        fee: Number(formData.fee),
        minAmount: Number(formData.minAmount),
        maxAmount: Number(formData.maxAmount),
        configurations: formData.configurations ? JSON.parse(formData.configurations) : {}
      };

      if (editingProvider) {
        await axios.put(`/api/v1/bills/admin/providers/${editingProvider._id}`, payload);
        toast({
          title: 'Success',
          description: 'Provider updated successfully'
        });
      } else {
        await axios.post('/api/v1/bills/admin/providers', payload);
        toast({
          title: 'Success',
          description: 'Provider created successfully'
        });
      }

      setIsDialogOpen(false);
      resetForm();
      fetchProviders();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to save provider',
        variant: 'destructive'
      });
    }
  };

  const toggleProviderStatus = async (id: string, isActive: boolean) => {
    try {
      await axios.put(`/api/v1/bills/admin/providers/${id}`, { isActive });
      setProviders(prev => prev.map(p => p._id === id ? { ...p, isActive } : p));
      toast({
        title: 'Success',
        description: `Provider ${isActive ? 'activated' : 'deactivated'} successfully`
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update provider status',
        variant: 'destructive'
      });
    }
  };

  const deleteProvider = async (id: string) => {
    if (!confirm('Are you sure you want to delete this provider?')) return;

    try {
      await axios.delete(`/api/v1/bills/admin/providers/${id}`);
      setProviders(prev => prev.filter(p => p._id !== id));
      toast({
        title: 'Success',
        description: 'Provider deleted successfully'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete provider',
        variant: 'destructive'
      });
    }
  };

  const toggleSecretVisibility = (id: string) => {
    setShowSecrets(prev => ({ ...prev, [id]: !prev[id] }));
  };

  const maskSecret = (secret: string, visible: boolean) => {
    if (visible) return secret;
    return '*'.repeat(secret.length);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Bill Payment Providers
          </CardTitle>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => { resetForm(); setIsDialogOpen(true); }}>
                <Plus className="h-4 w-4 mr-2" />
                Add Provider
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>
                  {editingProvider ? 'Edit Provider' : 'Add New Provider'}
                </DialogTitle>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Provider Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="category">Category</Label>
                    <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((cat) => (
                          <SelectItem key={cat} value={cat}>
                            {cat.replace('_', ' ').toUpperCase()}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="provider">Provider Type</Label>
                    <Select value={formData.provider} onValueChange={(value) => setFormData({ ...formData, provider: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select provider" />
                      </SelectTrigger>
                      <SelectContent>
                        {providerTypes.map((provider) => (
                          <SelectItem key={provider} value={provider}>
                            {provider.toUpperCase()}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="baseUrl">Base URL</Label>
                    <Input
                      id="baseUrl"
                      value={formData.baseUrl}
                      onChange={(e) => setFormData({ ...formData, baseUrl: e.target.value })}
                      placeholder="https://api.provider.com"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="apiKey">API Key</Label>
                    <Input
                      id="apiKey"
                      value={formData.apiKey}
                      onChange={(e) => setFormData({ ...formData, apiKey: e.target.value })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="secretKey">Secret Key</Label>
                    <Input
                      id="secretKey"
                      type="password"
                      value={formData.secretKey}
                      onChange={(e) => setFormData({ ...formData, secretKey: e.target.value })}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="fee">Fee</Label>
                    <Input
                      id="fee"
                      type="number"
                      value={formData.fee}
                      onChange={(e) => setFormData({ ...formData, fee: Number(e.target.value) })}
                      min="0"
                      step="0.01"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="feeType">Fee Type</Label>
                    <Select value={formData.feeType} onValueChange={(value: 'fixed' | 'percentage') => setFormData({ ...formData, feeType: value })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fixed">Fixed Amount</SelectItem>
                        <SelectItem value="percentage">Percentage</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center space-x-2 pt-6">
                    <Switch
                      checked={formData.isActive}
                      onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
                    />
                    <Label>Active</Label>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="minAmount">Min Amount (₦)</Label>
                    <Input
                      id="minAmount"
                      type="number"
                      value={formData.minAmount}
                      onChange={(e) => setFormData({ ...formData, minAmount: Number(e.target.value) })}
                      min="0"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="maxAmount">Max Amount (₦)</Label>
                    <Input
                      id="maxAmount"
                      type="number"
                      value={formData.maxAmount}
                      onChange={(e) => setFormData({ ...formData, maxAmount: Number(e.target.value) })}
                      min="0"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="configurations">Additional Configurations (JSON)</Label>
                  <Textarea
                    id="configurations"
                    value={formData.configurations}
                    onChange={(e) => setFormData({ ...formData, configurations: e.target.value })}
                    placeholder='{"webhook_url": "https://example.com/webhook"}'
                    rows={3}
                  />
                </div>

                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">
                    {editingProvider ? 'Update' : 'Create'} Provider
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Provider</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>API Keys</TableHead>
                <TableHead>Fee</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {providers.map((provider) => {
                const Icon = categoryIcons[provider.category as keyof typeof categoryIcons];
                return (
                  <TableRow key={provider._id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        <div>
                          <div className="font-medium">{provider.name}</div>
                          <div className="text-sm text-muted-foreground capitalize">
                            {provider.category.replace('_', ' ')}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {provider.provider.toUpperCase()}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <code className="text-xs bg-muted px-2 py-1 rounded">
                            {maskSecret(provider.apiKey, showSecrets[provider._id])}
                          </code>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => toggleSecretVisibility(provider._id)}
                          >
                            {showSecrets[provider._id] ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                          </Button>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Secret: {maskSecret(provider.secretKey, showSecrets[provider._id])}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {provider.feeType === 'percentage' ? `${provider.fee}%` : `₦${provider.fee}`}
                    </TableCell>
                    <TableCell>
                      <Switch
                        checked={provider.isActive}
                        onCheckedChange={(checked) => toggleProviderStatus(provider._id, checked)}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEdit(provider)}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => deleteProvider(provider._id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>

        {providers.length === 0 && !loading && (
          <div className="text-center py-8 text-muted-foreground">
            No providers configured
          </div>
        )}
      </CardContent>
    </Card>
  );
};