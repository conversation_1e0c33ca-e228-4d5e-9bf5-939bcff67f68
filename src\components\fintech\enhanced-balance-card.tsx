import React, { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Eye, 
  EyeOff, 
  Plus, 
  Target,
  TrendingUp,
  Wallet,
  ArrowUpRight,
  ArrowDownLeft,
  Settings,
  PiggyBank
} from 'lucide-react';
import { useBalance } from '@/hooks/use-balance';
import { toast } from 'sonner';

interface EnhancedBalanceCardProps {
  className?: string;
}

export const EnhancedBalanceCard = ({ className }: EnhancedBalanceCardProps) => {
  const { balance, updateBalance } = useBalance();
  const [showBalance, setShowBalance] = useState(true);
  const [targetAmount, setTargetAmount] = useState<number>(
    () => Number(localStorage.getItem('savings-target')) || 100000
  );
  const [newTarget, setNewTarget] = useState(targetAmount.toString());
  const [targetDialogOpen, setTargetDialogOpen] = useState(false);

  // Mock recent activity data
  const recentActivities = [
    { type: 'credit', amount: 5000, description: 'Salary deposit', time: '2 hours ago' },
    { type: 'debit', amount: 1200, description: 'Bill payment', time: '1 day ago' },
    { type: 'credit', amount: 2500, description: 'Interest earned', time: '3 days ago' },
  ];

  const progressPercentage = (balance / targetAmount) * 100;
  const remainingAmount = Math.max(0, targetAmount - balance);

  const handleSetTarget = () => {
    const amount = Number(newTarget);
    if (amount > 0) {
      setTargetAmount(amount);
      localStorage.setItem('savings-target', amount.toString());
      toast.success(`Savings target set to ₦${amount.toLocaleString()}`);
      setTargetDialogOpen(false);
    } else {
      toast.error('Please enter a valid target amount');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Balance Card */}
      <Card className="relative overflow-hidden bg-gradient-to-br from-primary via-primary/95 to-primary/90 text-primary-foreground" style={{ borderRadius: '0px' }}>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Wallet className="h-5 w-5" />
              <span className="text-sm font-medium opacity-90">Total Balance</span>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowBalance(!showBalance)}
                className="h-8 w-8 p-0 hover:bg-primary-foreground/20"
              >
                {showBalance ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
              <Dialog open={targetDialogOpen} onOpenChange={setTargetDialogOpen}>
                <DialogTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 hover:bg-primary-foreground/20"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Set Savings Target</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Target Amount (₦)</label>
                      <Input
                        type="number"
                        value={newTarget}
                        onChange={(e) => setNewTarget(e.target.value)}
                        placeholder="Enter target amount"
                        className="mt-1"
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button onClick={handleSetTarget} className="flex-1">
                        Set Target
                      </Button>
                      <Button variant="outline" onClick={() => setTargetDialogOpen(false)}>
                        Cancel
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="text-3xl font-bold font-mono">
              {showBalance ? formatCurrency(balance) : '₦ •••••••'}
            </div>
            <p className="text-sm opacity-80 mt-1">
              Available balance • Updated just now
            </p>
          </div>

          {/* Savings Progress */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-1">
                <Target className="h-4 w-4" />
                <span>Savings Goal</span>
              </div>
              <span className="font-medium">
                {Math.round(progressPercentage)}% of {formatCurrency(targetAmount)}
              </span>
            </div>
            <Progress 
              value={progressPercentage} 
              className="h-2 bg-primary-foreground/20"
            />
            {remainingAmount > 0 && (
              <p className="text-xs opacity-80">
                {formatCurrency(remainingAmount)} remaining to reach your goal
              </p>
            )}
          </div>

          {/* Quick Actions */}
          <div className="flex gap-2 pt-2">
            <Button
              variant="secondary"
              size="sm"
              className="flex-1 bg-primary-foreground/10 hover:bg-primary-foreground/20 text-primary-foreground border-primary-foreground/20"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Money
            </Button>
            <Button
              variant="secondary"
              size="sm"
              className="flex-1 bg-primary-foreground/10 hover:bg-primary-foreground/20 text-primary-foreground border-primary-foreground/20"
            >
              <ArrowUpRight className="h-4 w-4 mr-1" />
              Send
            </Button>
          </div>
        </CardContent>

        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-primary-foreground/10 rounded-full -translate-y-16 translate-x-16" />
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-primary-foreground/5 rounded-full translate-y-12 -translate-x-12" />
      </Card>

      {/* Secondary Cards */}
      <div className="grid grid-cols-2 gap-4">
        {/* Savings Stats */}
        <Card className="mobile-card" style={{ borderRadius: '0px' }}>
          <CardContent className="p-4 space-y-2">
            <div className="flex items-center gap-2">
              <PiggyBank className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">Monthly Savings</span>
            </div>
            <div className="text-lg font-bold">₦15,750</div>
            <div className="flex items-center gap-1 text-xs text-green-600">
              <TrendingUp className="h-3 w-3" />
              <span>+12% vs last month</span>
            </div>
          </CardContent>
        </Card>

        {/* Interest Earned */}
        <Card className="mobile-card" style={{ borderRadius: '0px' }}>
          <CardContent className="p-4 space-y-2">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">Interest Earned</span>
            </div>
            <div className="text-lg font-bold">₦2,340</div>
            <div className="text-xs text-muted-foreground">This month</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};