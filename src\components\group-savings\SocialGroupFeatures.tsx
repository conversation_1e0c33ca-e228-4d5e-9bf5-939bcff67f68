import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Heart, MessageCircle, Share2, Trophy, Users, Star, Send } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface GroupMember {
  id: string;
  name: string;
  avatar?: string;
  contributions: number;
  streak: number;
  rank: number;
  reputation: number;
}

interface GroupPost {
  id: string;
  author: GroupMember;
  content: string;
  timestamp: string;
  likes: number;
  comments: number;
  isLiked: boolean;
}

interface Milestone {
  id: string;
  title: string;
  description: string;
  achievedBy: GroupMember[];
  date: string;
}

export const SocialGroupFeatures = () => {
  const { toast } = useToast();
  const [newPost, setNewPost] = useState('');
  const [selectedTab, setSelectedTab] = useState<'feed' | 'leaderboard' | 'milestones'>('feed');

  const [groupMembers] = useState<GroupMember[]>([
    {
      id: '1',
      name: 'Adebayo Johnson',
      avatar: '/lovable-uploads/76F34E92-BC40-43B8-BD74-F20B1A8A2FF9.png',
      contributions: 125000,
      streak: 12,
      rank: 1,
      reputation: 98
    },
    {
      id: '2',
      name: 'Fatima Ahmad',
      avatar: '/lovable-uploads/a95ab938-222b-48b2-a5dc-67971a2ae055.png',
      contributions: 118000,
      streak: 8,
      rank: 2,
      reputation: 94
    },
    {
      id: '3',
      name: 'Chinedu Okafor',
      contributions: 95000,
      streak: 6,
      rank: 3,
      reputation: 87
    }
  ]);

  const [groupFeed, setGroupFeed] = useState<GroupPost[]>([
    {
      id: '1',
      author: groupMembers[0],
      content: "Just made my contribution for this week! 💪 Only 3 more weeks until my payout turn. Excited to finally get that laptop I've been saving for!",
      timestamp: '2024-01-15T10:30:00Z',
      likes: 8,
      comments: 3,
      isLiked: false
    },
    {
      id: '2',
      author: groupMembers[1],
      content: "Thanks everyone for the motivation! This group has really helped me stay consistent with my savings. 🙏",
      timestamp: '2024-01-14T15:45:00Z',
      likes: 12,
      comments: 5,
      isLiked: true
    }
  ]);

  const [milestones] = useState<Milestone[]>([
    {
      id: '1',
      title: 'First Million Saved',
      description: 'Group collectively saved ₦1,000,000',
      achievedBy: groupMembers,
      date: '2024-01-10'
    },
    {
      id: '2',
      title: 'Perfect Attendance',
      description: 'All members contributed on time this month',
      achievedBy: groupMembers,
      date: '2024-01-01'
    }
  ]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const handleLike = (postId: string) => {
    setGroupFeed(prev => prev.map(post => 
      post.id === postId 
        ? { 
            ...post, 
            isLiked: !post.isLiked, 
            likes: post.isLiked ? post.likes - 1 : post.likes + 1 
          }
        : post
    ));
  };

  const handlePost = () => {
    if (!newPost.trim()) return;

    const post: GroupPost = {
      id: Date.now().toString(),
      author: {
        id: 'current-user',
        name: 'You',
        contributions: 89000,
        streak: 4,
        rank: 4,
        reputation: 82
      },
      content: newPost,
      timestamp: new Date().toISOString(),
      likes: 0,
      comments: 0,
      isLiked: false
    };

    setGroupFeed(prev => [post, ...prev]);
    setNewPost('');
    toast({
      title: "Posted!",
      description: "Your message has been shared with the group.",
    });
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return <Trophy className="h-4 w-4 text-yellow-500" />;
      case 2: return <Trophy className="h-4 w-4 text-gray-400" />;
      case 3: return <Trophy className="h-4 w-4 text-orange-500" />;
      default: return <Star className="h-4 w-4 text-muted-foreground" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Social Navigation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-primary" />
            <span>Group Social Hub</span>
          </CardTitle>
          <CardDescription>
            Stay connected with your savings group members
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-2">
            {[
              { key: 'feed', label: 'Group Feed', icon: MessageCircle },
              { key: 'leaderboard', label: 'Leaderboard', icon: Trophy },
              { key: 'milestones', label: 'Milestones', icon: Star }
            ].map(({ key, label, icon: Icon }) => (
              <Button
                key={key}
                variant={selectedTab === key ? "default" : "outline"}
                onClick={() => setSelectedTab(key as any)}
                className="flex items-center space-x-2"
              >
                <Icon className="h-4 w-4" />
                <span>{label}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Group Feed */}
      {selectedTab === 'feed' && (
        <div className="space-y-4">
          {/* Post Composer */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <Textarea
                  placeholder="Share an update with your group..."
                  value={newPost}
                  onChange={(e) => setNewPost(e.target.value)}
                  className="resize-none"
                />
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    Encourage your group members or share your progress
                  </p>
                  <Button onClick={handlePost} disabled={!newPost.trim()}>
                    <Send className="h-4 w-4 mr-2" />
                    Post
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Feed Posts */}
          {groupFeed.map((post) => (
            <Card key={post.id}>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  {/* Post Header */}
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarImage src={post.author.avatar} />
                      <AvatarFallback>
                        {post.author.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <p className="font-medium">{post.author.name}</p>
                        {getRankIcon(post.author.rank)}
                        <Badge variant="secondary" className="text-xs">
                          Rank #{post.author.rank}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {new Date(post.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </div>

                  {/* Post Content */}
                  <p className="text-sm leading-relaxed">{post.content}</p>

                  {/* Post Actions */}
                  <div className="flex items-center space-x-4 pt-2 border-t">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleLike(post.id)}
                      className={`flex items-center space-x-1 ${
                        post.isLiked ? 'text-red-500' : 'text-muted-foreground'
                      }`}
                    >
                      <Heart className={`h-4 w-4 ${post.isLiked ? 'fill-current' : ''}`} />
                      <span>{post.likes}</span>
                    </Button>
                    <Button variant="ghost" size="sm" className="flex items-center space-x-1">
                      <MessageCircle className="h-4 w-4" />
                      <span>{post.comments}</span>
                    </Button>
                    <Button variant="ghost" size="sm" className="flex items-center space-x-1">
                      <Share2 className="h-4 w-4" />
                      <span>Share</span>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Leaderboard */}
      {selectedTab === 'leaderboard' && (
        <Card>
          <CardHeader>
            <CardTitle>Group Leaderboard</CardTitle>
            <CardDescription>
              See how you rank against other group members
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {groupMembers.map((member, index) => (
                <div
                  key={member.id}
                  className={`flex items-center space-x-4 p-4 rounded-lg ${
                    index === 0 ? 'bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200' : 'bg-muted/50'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    {getRankIcon(member.rank)}
                    <span className="font-bold text-lg">#{member.rank}</span>
                  </div>
                  
                  <Avatar>
                    <AvatarImage src={member.avatar} />
                    <AvatarFallback>
                      {member.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1">
                    <p className="font-medium">{member.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatCurrency(member.contributions)} contributed
                    </p>
                  </div>
                  
                  <div className="text-right space-y-1">
                    <Badge variant="outline" className="text-xs">
                      {member.streak} day streak
                    </Badge>
                    <p className="text-xs text-muted-foreground">
                      {member.reputation}% reputation
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Milestones */}
      {selectedTab === 'milestones' && (
        <div className="space-y-4">
          {milestones.map((milestone) => (
            <Card key={milestone.id}>
              <CardContent className="pt-6">
                <div className="flex items-start space-x-4">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Star className="h-6 w-6 text-primary" />
                  </div>
                  
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg">{milestone.title}</h3>
                    <p className="text-muted-foreground mb-3">{milestone.description}</p>
                    
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-sm text-muted-foreground">Achieved by:</span>
                      <div className="flex -space-x-2">
                        {milestone.achievedBy.slice(0, 3).map((member) => (
                          <Avatar key={member.id} className="h-6 w-6 border-2 border-background">
                            <AvatarImage src={member.avatar} />
                            <AvatarFallback className="text-xs">
                              {member.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                        ))}
                        {milestone.achievedBy.length > 3 && (
                          <div className="h-6 w-6 bg-muted rounded-full border-2 border-background flex items-center justify-center">
                            <span className="text-xs">+{milestone.achievedBy.length - 3}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <p className="text-xs text-muted-foreground">
                      {new Date(milestone.date).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};