import { AutoSavePayment } from "@/components/payments/AutoSavePayment";
import { DebitCardManager } from "@/components/payments/DebitCardManager";
import { PaymentProofUpload } from "@/components/payments/PaymentProofUpload";
import { PayoutModeDialog } from "@/components/payments/PayoutModeDialog";
import { PaystackIntegration } from "@/components/payments/PaystackIntegration";
import { WithdrawalRequestForm } from "@/components/payments/WithdrawalRequestForm";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useBalance } from "@/hooks/use-balance";
import { CreditCard, DollarSign, Repeat, Upload, Wallet } from "lucide-react";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";

// Remove mock data - payment methods and transactions will be fetched from API

export default function Payments() {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | null>(null);
  const [depositAmount, setDepositAmount] = useState("");
  const [depositStep, setDepositStep] = useState<"amount" | "method" | "upload">("amount");
  const [activeTab, setActiveTab] = useState("paystack");
  const [transactions, setTransactions] = useState([]);
  const [paymentMethods, setPaymentMethods] = useState<any[]>([]);
  const [dataLoading, setDataLoading] = useState(true);
  const [showPayoutSettings, setShowPayoutSettings] = useState(false);
  const [showPaymentProofModal, setShowPaymentProofModal] = useState(false);
  const { balance, updateBalance } = useBalance();

  useEffect(() => {
    const fetchPaymentData = async () => {
      setDataLoading(true);
      try {
        // Fetch payment methods
        const methodsResponse = await fetch('/api/v1/payments/methods', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (methodsResponse.ok) {
          const methodsData = await methodsResponse.json();
          setPaymentMethods(methodsData.data || []);
        }

        // Fetch transactions
        const transactionsResponse = await fetch('/api/v1/payments/transactions', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (transactionsResponse.ok) {
          const transactionsData = await transactionsResponse.json();
          setTransactions(transactionsData.data || []);
        }
      } catch (error) {
        console.error('Error fetching payment data:', error);
        setPaymentMethods([]);
        setTransactions([]);
      } finally {
        setDataLoading(false);
      }
    };

    fetchPaymentData();
  }, []);

  // Remove mock user data - use actual auth context

  // Load transactions from localStorage
  useEffect(() => {
    const savedTransactions = localStorage.getItem('user_transactions');
    if (savedTransactions) {
      setTransactions(JSON.parse(savedTransactions));
    }
  }, []);

  const handleDepositAmountSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const amount = parseFloat(depositAmount);
    
    if (isNaN(amount) || amount <= 0) {
      toast.error("Please enter a valid deposit amount");
      return;
    }
    
    setDepositStep("method");
  };

  const handleSelectPaymentMethod = (id: string) => {
    setSelectedPaymentMethod(id);
    setDepositStep("upload");
  };

  const handlePaymentProofUpload = async (formData) => {
    // Add amount to user's balance
    const amount = parseFloat(depositAmount);
    await updateBalance(amount, 'add');
    
    // Create a transaction record in localStorage
    const newTransaction = {
      id: `txn-${Date.now()}`,
      type: 'deposit',
      amount: amount,
      date: new Date().toISOString().split('T')[0],
      status: 'completed',
      reference: `DEP${Math.floor(Math.random() * 100000)}`
    };
    
    const savedTransactions = JSON.parse(localStorage.getItem('user_transactions') || '[]');
    savedTransactions.push(newTransaction);
    localStorage.setItem('user_transactions', JSON.stringify(savedTransactions));
    setTransactions(savedTransactions);
    
    toast.success("Deposit submitted successfully!");
    
    setDepositAmount("");
    setSelectedPaymentMethod(null);
    setDepositStep("amount");
    setShowPaymentProofModal(false);
  };

  const handleDirectPaymentProofSubmit = (formData) => {
    // Process the payment proof submission
    const amount = parseFloat(formData.amount);
    if (!isNaN(amount) && amount > 0) {
      updateBalance(amount, 'add');
      
      // Create a transaction record
      const newTransaction = {
        id: `txn-${Date.now()}`,
        type: 'deposit',
        amount: amount,
        date: new Date().toISOString().split('T')[0],
        status: 'pending',
        reference: formData.reference || `DEP${Math.floor(Math.random() * 100000)}`
      };
      
      const savedTransactions = JSON.parse(localStorage.getItem('user_transactions') || '[]');
      savedTransactions.push(newTransaction);
      localStorage.setItem('user_transactions', JSON.stringify(savedTransactions));
      setTransactions(savedTransactions);
      
      toast.success("Payment proof submitted successfully! We'll verify your payment soon.");
    } else {
      toast.error("Please enter a valid amount");
    }
    
    setShowPaymentProofModal(false);
  };

  const handleWithdrawalSubmit = async (formData) => {
    toast.success("Withdrawal request submitted successfully!");
    setActiveTab("transactions");
  };

  // Helper function to determine badge variant based on status
  const getStatusBadgeVariant = (status: string) => {
    if (status === "completed") return "success";
    if (status === "failed") return "destructive";
    return "outline"; // For "pending" status
  };

  const renderDepositStep = () => {
    switch (depositStep) {
      case "amount":
        return (
          <form onSubmit={handleDepositAmountSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="amount">Deposit Amount (₦)</Label>
              <Input
                id="amount"
                type="number"
                placeholder="Enter amount to deposit"
                value={depositAmount}
                onChange={(e) => setDepositAmount(e.target.value)}
                required
              />
            </div>
            <Button type="submit" className="w-full">Continue</Button>
          </form>
        );
      
      case "method":
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Select Payment Method</h3>
            <div className="grid gap-4">
              {paymentMethods.map((method) => (
                <Card 
                  key={method.id} 
                  className="cursor-pointer hover:border-primary transition-colors"
                  onClick={() => handleSelectPaymentMethod(method.id)}
                >
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">{method.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm"><strong>Bank:</strong> {method.bankName}</p>
                    <p className="text-sm"><strong>Account:</strong> {method.accountNumber}</p>
                    <p className="text-sm text-gray-500 mt-2">{method.instructions}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
            <Button variant="outline" onClick={() => setDepositStep("amount")}>Back</Button>
          </div>
        );
      
      case "upload":
        const selectedMethod = paymentMethods.find(m => m.id === selectedPaymentMethod);
        
        return (
          <div className="space-y-4">
            <div className="bg-muted p-4 rounded-lg space-y-2 mb-4">
              <h3 className="font-medium">Payment Details</h3>
              <p className="text-sm"><strong>Amount:</strong> ₦{depositAmount}</p>
              <p className="text-sm"><strong>Method:</strong> {selectedMethod?.name}</p>
              <p className="text-sm"><strong>Bank:</strong> {selectedMethod?.bankName}</p>
              <p className="text-sm"><strong>Account Number:</strong> {selectedMethod?.accountNumber}</p>
              <div className="text-sm mt-2">
                <strong>Instructions:</strong>
                <p className="text-gray-500">{selectedMethod?.instructions}</p>
              </div>
            </div>
            
            <PaymentProofUpload 
              onSubmit={handlePaymentProofUpload}
              isLoading={false}
            />
            
            <Button variant="outline" onClick={() => setDepositStep("method")}>Back</Button>
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold">Payments</h1>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            className="flex items-center gap-2"
            onClick={() => setShowPaymentProofModal(true)}
          >
            <Upload className="h-4 w-4" />
            I've Made a Payment
          </Button>
          <Button 
            variant="outline" 
            className="flex items-center gap-2"
            onClick={() => setShowPayoutSettings(true)}
          >
            <Wallet className="h-4 w-4" />
            Payout Settings
          </Button>
        </div>
      </div>
      
      <Dialog open={showPayoutSettings} onOpenChange={setShowPayoutSettings}>
        <PayoutModeDialog onClose={() => setShowPayoutSettings(false)} />
      </Dialog>
      
      <Dialog open={showPaymentProofModal} onOpenChange={setShowPaymentProofModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Submit Payment Proof</DialogTitle>
          </DialogHeader>
          <PaymentProofUpload 
            onSubmit={handleDirectPaymentProofSubmit}
            isLoading={false}
          />
        </DialogContent>
      </Dialog>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="paystack" className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Paystack
          </TabsTrigger>
          <TabsTrigger value="cards" className="flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            Cards
          </TabsTrigger>
          <TabsTrigger value="auto-save" className="flex items-center gap-2">
            <Repeat className="h-4 w-4" />
            Auto-Save
          </TabsTrigger>
          <TabsTrigger value="withdraw" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Withdraw
          </TabsTrigger>
          <TabsTrigger value="transactions" className="flex items-center gap-2">
            <Wallet className="h-4 w-4" />
            History
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="paystack" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Paystack Payment
              </CardTitle>
              <CardDescription>
                Make instant payments and deposits using Paystack
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PaystackIntegration />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cards" className="space-y-4">
          <DebitCardManager
            userId={currentUser.id}
            userEmail={currentUser.email}
            userName={currentUser.name}
          />
        </TabsContent>

        <TabsContent value="auto-save" className="space-y-4">
          <AutoSavePayment
            userId={currentUser.id}
            userEmail={currentUser.email}
            userName={currentUser.name}
          />
        </TabsContent>
        
        <TabsContent value="withdraw" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Request Withdrawal</CardTitle>
              <CardDescription>
                Withdraw funds from your savings account
              </CardDescription>
            </CardHeader>
            <CardContent>
              <WithdrawalRequestForm onSubmit={handleWithdrawalSubmit} />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Transaction History</CardTitle>
              <CardDescription>
                View your recent deposit and withdrawal transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Type</TableHead>
                    <TableHead>Amount (₦)</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Reference</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.length > 0 ? (
                    transactions.map((transaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell className="font-medium capitalize">{transaction.type}</TableCell>
                        <TableCell>{transaction.amount.toLocaleString()}</TableCell>
                        <TableCell>{transaction.date}</TableCell>
                        <TableCell>{transaction.reference}</TableCell>
                        <TableCell>
                          <Badge 
                            variant={getStatusBadgeVariant(transaction.status)}
                          >
                            {transaction.status}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-4 text-muted-foreground">
                        No transactions yet
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
