import { Link } from 'react-router-dom';
import { PiggyBank, Mail, Phone, MapPin } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-black/40 border-t border-[#39E59E]/20 py-12">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Logo & Description */}
          <div className="space-y-4">
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-[#39E59E] rounded-lg flex items-center justify-center">
                <PiggyBank className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-white">Better Interest</span>
            </Link>
            <p className="text-gray-400 text-sm">
              Nigeria's leading smart savings platform helping you build wealth with intelligent financial tools.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-white font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li><Link to="/about" className="text-gray-400 hover:text-[#39E59E] text-sm transition-colors">About Us</Link></li>
              <li><Link to="/features" className="text-gray-400 hover:text-[#39E59E] text-sm transition-colors">Features</Link></li>
              <li><Link to="/plans" className="text-gray-400 hover:text-[#39E59E] text-sm transition-colors">Plans</Link></li>
              <li><Link to="/contact" className="text-gray-400 hover:text-[#39E59E] text-sm transition-colors">Contact</Link></li>
            </ul>
          </div>

          {/* Legal */}
          <div>
            <h3 className="text-white font-semibold mb-4">Legal</h3>
            <ul className="space-y-2">
              <li><Link to="/privacy" className="text-gray-400 hover:text-[#39E59E] text-sm transition-colors">Privacy Policy</Link></li>
              <li><Link to="/terms" className="text-gray-400 hover:text-[#39E59E] text-sm transition-colors">Terms of Service</Link></li>
              <li><Link to="/security" className="text-gray-400 hover:text-[#39E59E] text-sm transition-colors">Security</Link></li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-white font-semibold mb-4">Contact</h3>
            <ul className="space-y-2">
              <li className="flex items-center gap-2 text-gray-400 text-sm">
                <Mail className="w-4 h-4" />
                <EMAIL>
              </li>
              <li className="flex items-center gap-2 text-gray-400 text-sm">
                <Phone className="w-4 h-4" />
                +234 ************
              </li>
              <li className="flex items-center gap-2 text-gray-400 text-sm">
                <MapPin className="w-4 h-4" />
                Lagos, Nigeria
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-[#39E59E]/20 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            © 2024 Better Interest. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <span className="text-gray-400 text-sm">NDIC Insured</span>
            <span className="text-gray-400 text-sm">SSL Secured</span>
            <span className="text-gray-400 text-sm">ISO 27001</span>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
