import React from 'react';
import { PaystackPayment } from './PaystackPayment';
import { useAuth } from '@/hooks/use-auth';
import { useBalance } from '@/hooks/use-balance';
import { FintechCard } from '@/components/ui/fintech-card';
import { toast } from 'sonner';

interface PaystackIntegrationProps {
  amount?: number;
  onSuccess?: (amount: number) => void;
  onError?: (error: string) => void;
  purpose?: string;
  title?: string;
}

export const PaystackIntegration = ({ 
  amount, 
  onSuccess, 
  onError, 
  purpose = 'wallet_funding',
  title = 'Fund Your Wallet'
}: PaystackIntegrationProps) => {
  const { user } = useAuth();
  const { updateBalance } = useBalance();

  const handlePaymentSuccess = async (depositAmount: number) => {
    try {
      // Update user balance
      await updateBalance(depositAmount, 'add');
      
      toast.success(`Payment successful! ₦${depositAmount.toLocaleString()} added to your wallet.`);
      
      if (onSuccess) {
        onSuccess(depositAmount);
      }
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to update balance';
      toast.error(errorMessage);
      
      if (onError) {
        onError(errorMessage);
      }
    }
  };

  const handlePaymentError = (error: string) => {
    toast.error(error);
    if (onError) {
      onError(error);
    }
  };

  if (!user) {
    return (
      <FintechCard className="p-6 text-center">
        <p className="text-muted-foreground">Please log in to make payments</p>
      </FintechCard>
    );
  }

  return (
    <div className="w-full">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold mb-2">{title}</h2>
        <p className="text-muted-foreground">
          Secure payments powered by Paystack
        </p>
      </div>
      
      <div className="flex justify-center">
        <PaystackPayment 
          onSuccess={handlePaymentSuccess}
          onError={handlePaymentError}
        />
      </div>

      <div className="mt-6 text-center text-xs text-muted-foreground">
        <p className="mb-2">INSURED BY THE NDIC & AMAC MFB LTD</p>
        <p>Your deposits are secured and insured up to ₦500,000 per depositor</p>
      </div>
    </div>
  );
};