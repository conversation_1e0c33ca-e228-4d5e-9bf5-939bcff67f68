import {
  BarChart3,
  CreditCard,
  DollarSign,
  PiggyBank,
  PlusCircle,
  Send,
  TrendingUp,
  Clock,
  Target,
  CalendarDays,
  ArrowUpRight,
  Percent,
  ArrowRight,
  Wallet,
  Gift,
  Sparkles,
  Users,
  Eye,
  EyeOff,
  Upload,
  Settings,
  User,
  Home,
} from "lucide-react";
import { StatCard } from "@/components/ui/stat-card";
import { Button } from "@/components/ui/button";
import { NeumorphismCard, NeumorphismCardContent, NeumorphismCardDescription, NeumorphismCardHeader, NeumorphismCardTitle, NeumorphismCardFooter, NeumorphismButton } from "@/components/ui/neumorphism-card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { AvatarGroup } from "@/components/ui/avatar-group";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useState, useEffect } from "react";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { PaymentProofUpload } from "@/components/payments/PaymentProofUpload";
import { useBalance } from "@/hooks/use-balance";
import { PayoutModeDialog } from "@/components/payments/PayoutModeDialog";
import { QuickPayoutDialog } from "@/components/payments/QuickPayoutDialog";
import { AdsBanner } from "@/components/ui/ads-banner";
import { EnhancedBalanceCard } from "@/components/fintech/enhanced-balance-card";
import { InterestCalculator } from "@/components/savings/InterestCalculator";

const recentTransactions = [
  {
    id: "t1",
    description: "Daily Deposit",
    amount: 5000,
    date: "Today",
    type: "deposit",
  },
  {
    id: "t2", 
    description: "Withdrawal",
    amount: -2000,
    date: "Yesterday",
    type: "withdrawal",
  },
  {
    id: "t3",
    description: "Bonus Credit",
    amount: 1000,
    date: "May 21, 2023",
    type: "deposit",
  },
  {
    id: "t4",
    description: "Daily Deposit",
    amount: 5000,
    date: "May 20, 2023",
    type: "deposit",
  },
];

const userData = {
  name: "Ade",
  phone: "8105551234",
};

const UserDashboard = () => {
  const navigate = useNavigate();
  const [showBalance, setShowBalance] = useState(true);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showPayoutModeModal, setShowPayoutModeModal] = useState(false);
  const [showPayoutModal, setShowPayoutModal] = useState(false);
  const { balance, isLoading: balanceLoading } = useBalance();

  const [transactions, setTransactions] = useState(recentTransactions);

  // Enhanced stats with modern design
  const enhancedStats = [
    {
      title: "Total Balance",
      value: showBalance ? `₦${balance.toLocaleString()}` : "••••••",
      change: "+12.5%",
      changeType: "increase",
      icon: <Wallet className="h-5 w-5" />,
      color: "from-green-500 to-emerald-600",
      bgGradient: "from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20"
    },
    {
      title: "This Month",
      value: showBalance ? "₦85,640" : "••••••",
      change: "+8.2%",
      changeType: "increase", 
      icon: <TrendingUp className="h-5 w-5" />,
      color: "from-green-500 to-emerald-600",
      bgGradient: "from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20"
    },
    {
      title: "Savings Goal",
      value: "73%",
      change: "+5.1%",
      changeType: "increase",
      icon: <Target className="h-5 w-5" />,
      color: "from-purple-500 to-violet-600", 
      bgGradient: "from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20"
    },
    {
      title: "Interest Earned",
      value: showBalance ? "₦12,340" : "••••••",
      change: "+15.3%",
      changeType: "increase",
      icon: <Percent className="h-5 w-5" />,
      color: "from-orange-500 to-red-500",
      bgGradient: "from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20"
    }
  ];

  useEffect(() => {
    const savedTransactions = localStorage.getItem('user_transactions');
    if (savedTransactions) {
      const parsedTransactions = JSON.parse(savedTransactions);
      const combinedTransactions = [...parsedTransactions, ...recentTransactions]
        .slice(0, 4);
      setTransactions(combinedTransactions);
    }
  }, []);

  const handlePaymentProofSubmit = (formData) => {
    console.log("Payment proof submitted:", formData);
    toast.success("Payment proof submitted successfully! We'll verify your payment soon.");
    setShowPaymentModal(false);
  };

  const handleConfigurePayoutSettings = () => {
    setShowPayoutModal(false);
    setShowPayoutModeModal(true);
  };

  return (
    <div className="space-y-6 p-4 lg:p-6 pb-20 md:pb-6">
      {/* Welcome Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8" data-aos="fade-down">
        <div>
          <h1 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
            Good Morning Boss! 👋
          </h1>
          <p className="text-muted-foreground mt-1">See as e dey go</p>
        </div>
        <div className="flex items-center gap-3 mt-4 lg:mt-0">
          <Button variant="outline" size="sm" onClick={() => setShowBalance(!showBalance)}>
            {showBalance ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            {showBalance ? "Hide" : "Show"}
          </Button>
          <Button onClick={() => setShowPaymentModal(true)} className="gap-2">
            <PlusCircle className="h-4 w-4" />
            Add Money
          </Button>
        </div>
      </div>

      {/* Balance Card and Stats - Side by Side Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6" data-aos="fade-up" data-aos-delay="50">
        {/* Enhanced Balance Card - Standalone */}
        <div>
          <EnhancedBalanceCard className="h-full border-2 border-green-600/20 dark:border-green-400/20 rounded-none" />
        </div>

        {/* Right Side Cards - 3 cards stacked vertically on mobile, horizontally on desktop */}
        <div className="space-y-4">
          {/* Monthly Savings Card */}
          <NeumorphismCard 
            className="relative overflow-hidden bg-gradient-to-br from-background to-muted/10 hover:scale-105 active:scale-95 transition-all duration-300 w-full border border-green-600 dark:border-green-400"
            data-aos="zoom-in"
            data-aos-delay={150}
          >
            <NeumorphismCardContent className="p-4 lg:p-6">
              <div className="flex items-start justify-between gap-3">
                <div className="space-y-2 flex-1">
                  <p className="text-sm font-medium text-muted-foreground">Monthly Savings</p>
                  <p className="text-2xl lg:text-3xl font-bold">₦15,750</p>
                  <div className="flex items-center gap-1">
                    <TrendingUp className="h-4 w-4 text-green-600 flex-shrink-0" />
                    <span className="text-sm font-medium text-green-600">+12% vs last month</span>
                  </div>
                </div>
                <div className="p-3 rounded-none bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg flex-shrink-0">
                  <TrendingUp className="h-5 w-5" />
                </div>
              </div>
            </NeumorphismCardContent>
          </NeumorphismCard>

          {/* Interest Earned Card */}
          <NeumorphismCard 
            className="relative overflow-hidden bg-gradient-to-br from-background to-muted/10 hover:scale-105 active:scale-95 transition-all duration-300 w-full border border-green-600 dark:border-green-400"
            data-aos="zoom-in"
            data-aos-delay={200}
          >
            <NeumorphismCardContent className="p-4 lg:p-6">
              <div className="flex items-start justify-between gap-3">
                <div className="space-y-2 flex-1">
                  <p className="text-sm font-medium text-muted-foreground">Interest Earned</p>
                  <p className="text-2xl lg:text-3xl font-bold">₦2,340</p>
                  <div className="flex items-center gap-1">
                    <span className="text-sm font-medium text-muted-foreground">This month</span>
                  </div>
                </div>
                <div className="p-3 rounded-none bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg flex-shrink-0">
                  <Percent className="h-5 w-5" />
                </div>
              </div>
            </NeumorphismCardContent>
          </NeumorphismCard>
        </div>
      </div>

      {/* Ads Banner */}
      <div data-aos="fade-up" data-aos-delay="150">
        <AdsBanner className="mb-4" />
      </div>

      {/* Quick Actions & Main Content Grid - Mobile Responsive */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6" data-aos="fade-up" data-aos-delay="200">
        
        {/* Quick Actions - Mobile Responsive */}
        <div className="lg:col-span-1 order-2 lg:order-1">
          <NeumorphismCard className="p-4 sm:p-6 bg-gradient-to-br from-card to-accent/5 w-full">
            <NeumorphismCardHeader className="pb-4">
              <NeumorphismCardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-primary" />
                Quick Actions
              </NeumorphismCardTitle>
            </NeumorphismCardHeader>
            <NeumorphismCardContent className="space-y-2 sm:space-y-3">
              <NeumorphismButton 
                onClick={() => navigate('/savings')} 
                variant="primary"
                className="mobile-button w-full justify-start gap-2 sm:gap-3 active:scale-95"
                data-aos="slide-right"
                data-aos-delay="300"
              >
                <PiggyBank className="h-4 w-4" />
                Start New Savings Plan
              </NeumorphismButton>
              <NeumorphismButton 
                onClick={() => setShowPaymentModal(true)}
                variant="secondary"
                className="mobile-button w-full justify-start gap-2 sm:gap-3 active:scale-95"
                data-aos="slide-right" 
                data-aos-delay="350"
              >
                <PlusCircle className="h-4 w-4" />
                Add Money
              </NeumorphismButton>
              <NeumorphismButton 
                onClick={() => setShowPayoutModal(true)}
                variant="ghost"
                className="mobile-button w-full justify-start gap-2 sm:gap-3 text-orange-700 active:scale-95"
                data-aos="slide-right"
                data-aos-delay="400"
              >
                <Send className="h-4 w-4" />
                Quick Withdrawal
              </NeumorphismButton>
              <NeumorphismButton 
                onClick={() => navigate('/analytics')}
                variant="ghost"
                className="mobile-button w-full justify-start gap-2 sm:gap-3 text-green-700 active:scale-95"
                data-aos="slide-right"
                data-aos-delay="450"
              >
                <BarChart3 className="h-4 w-4" />
                View Analytics
              </NeumorphismButton>
            </NeumorphismCardContent>
          </NeumorphismCard>
        </div>

        {/* Main Content Area - Mobile Responsive */}
        <div className="lg:col-span-2 order-1 lg:order-2 space-y-4 lg:space-y-6 w-full">
          
          {/* Activity Overview - Inspired by accounting dashboard */}
          <NeumorphismCard className="bg-gradient-to-br from-card to-muted/5" data-aos="fade-left" data-aos-delay="300">
            <NeumorphismCardHeader className="flex flex-row items-center justify-between">
              <div>
                <NeumorphismCardTitle className="flex items-center gap-2 text-sm sm:text-base">
                  <Clock className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
                  Recent Activity
                </NeumorphismCardTitle>
                <NeumorphismCardDescription>Your latest financial activities</NeumorphismCardDescription>
              </div>
              <Button variant="ghost" size="sm" onClick={() => navigate('/transactions')}>
                View All <ArrowRight className="h-4 w-4 ml-1" />
              </Button>
            </NeumorphismCardHeader>
            <NeumorphismCardContent className="space-y-4">
              {transactions.slice(0, 4).map((transaction, index) => (
                <div 
                  key={transaction.id}
                  className="flex items-center justify-between p-3 rounded-lg neumorphism-base hover:shadow-md transition-all duration-200"
                  data-aos="slide-up"
                  data-aos-delay={350 + index * 50}
                >
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-full ${
                      transaction.type === 'deposit' 
                        ? 'bg-green-100 text-green-600 dark:bg-green-900/20' 
                        : 'bg-red-100 text-red-600 dark:bg-red-900/20'
                    }`}>
                      {transaction.type === 'deposit' ? (
                        <ArrowUpRight className="h-4 w-4" />
                      ) : (
                        <Send className="h-4 w-4" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{transaction.description}</p>
                      <p className="text-sm text-muted-foreground">{transaction.date}</p>
                    </div>
                  </div>
                  <div className={`font-semibold ${
                    transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {transaction.amount > 0 ? '+' : ''}₦{Math.abs(transaction.amount).toLocaleString()}
                  </div>
                </div>
              ))}
            </NeumorphismCardContent>
          </NeumorphismCard>

          {/* Savings Goals Progress - Mini cards inspired by dashboard */}
          <NeumorphismCard className="bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20" data-aos="fade-left" data-aos-delay="400">
            <NeumorphismCardHeader>
              <NeumorphismCardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-purple-600" />
                Savings Goals Progress
              </NeumorphismCardTitle>
            </NeumorphismCardHeader>
            <NeumorphismCardContent>
              <div className="grid grid-cols-1 gap-3 sm:gap-4">
                <div className="mobile-card-compact rounded-lg bg-white/70 dark:bg-background/70 border border-purple-200/50" data-aos="zoom-in" data-aos-delay="450">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-purple-700">Emergency Fund</span>
                    <span className="text-xs text-muted-foreground">73%</span>
                  </div>
                  <Progress value={73} className="h-2 mb-2" />
                  <p className="text-xs text-muted-foreground">₦73,000 of ₦100,000</p>
                </div>
                <div className="mobile-card-compact rounded-lg bg-white/70 dark:bg-background/70 border border-green-200/50" data-aos="zoom-in" data-aos-delay="500">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-green-700">Vacation Fund</span>
                    <span className="text-xs text-muted-foreground">45%</span>
                  </div>
                  <Progress value={45} className="h-2 mb-2" />
                  <p className="text-xs text-muted-foreground">₦22,500 of ₦50,000</p>
                </div>
              </div>
            </NeumorphismCardContent>
          </NeumorphismCard>
        </div>
      </div>

      {/* Interest Calculator */}
      <div data-aos="fade-up" data-aos-delay="500">
        <InterestCalculator />
      </div>

      {/* Feature Highlight Card - Inspired by promotional sections */}
      <NeumorphismCard className="overflow-hidden bg-gradient-to-r from-primary/10 via-accent/10 to-secondary/10" data-aos="fade-up" data-aos-delay="550">
        <NeumorphismCardContent className="p-6">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-4">
            <div className="flex items-center gap-4">
              <div className="p-3 rounded-full bg-gradient-to-r from-primary to-primary/80 text-white neumorphism-base">
                <Gift className="h-6 w-6" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">Better Interest Rates Available!</h3>
                <p className="text-sm text-muted-foreground">Upgrade to premium savings plans and earn up to 15% annually</p>
              </div>
            </div>
            <NeumorphismButton variant="primary" className="whitespace-nowrap">
              Learn More
            </NeumorphismButton>
          </div>
        </NeumorphismCardContent>
      </NeumorphismCard>

      {/* Mobile Navigation Bar - Only visible on mobile */}
      <div className="mobile-nav-bar">
        <div className="mobile-nav-grid">
          <button 
            onClick={() => navigate('/dashboard')}
            className="mobile-nav-item bg-primary/10 text-primary"
          >
            <Home className="mobile-nav-icon" />
            <span>Home</span>
          </button>
          <button 
            onClick={() => navigate('/savings')}
            className="mobile-nav-item"
          >
            <PiggyBank className="mobile-nav-icon" />
            <span>Savings</span>
          </button>
          <button 
            onClick={() => setShowPaymentModal(true)}
            className="mobile-nav-item"
          >
            <PlusCircle className="mobile-nav-icon" />
            <span>Add Money</span>
          </button>
          <button 
            onClick={() => navigate('/profile')}
            className="mobile-nav-item"
          >
            <User className="mobile-nav-icon" />
            <span>Profile</span>
          </button>
        </div>
      </div>

      {/* Dialogs */}
      <Dialog open={showPaymentModal} onOpenChange={setShowPaymentModal}>
        <DialogContent className="sm:max-w-[425px] neumorphism-base">
          <DialogHeader>
            <DialogTitle>Submit Payment Proof</DialogTitle>
          </DialogHeader>
          <PaymentProofUpload 
            onSubmit={handlePaymentProofSubmit}
            isLoading={false}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={showPayoutModeModal} onOpenChange={setShowPayoutModeModal}>
        <PayoutModeDialog onClose={() => setShowPayoutModeModal(false)} />
      </Dialog>

      <Dialog open={showPayoutModal} onOpenChange={setShowPayoutModal}>
        <QuickPayoutDialog 
          onClose={() => setShowPayoutModal(false)} 
          onConfigureSettings={handleConfigurePayoutSettings}
        />
      </Dialog>
    </div>
  );
};

export default UserDashboard;