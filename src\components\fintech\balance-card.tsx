import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { useBalance } from "@/hooks/use-balance";
import { Wallet, TrendingUp, TrendingDown } from "lucide-react";

interface BalanceCardProps {
  className?: string;
}

export function BalanceCard({ className = "" }: BalanceCardProps) {
  const { balance, formatBalance, isLoading } = useBalance();
  
  // Determine card color based on balance
  const isLowBalance = balance < 1000;
  const cardColorClass = isLowBalance 
    ? "bg-gradient-to-br from-destructive to-destructive/80 text-destructive-foreground" 
    : "black-green-gradient text-white balance-card-green";
  
  const iconColorClass = isLowBalance ? "text-destructive-foreground" : "text-success-foreground";
  
  if (isLoading) {
    return (
      <Card className={`${className} animate-pulse`}>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-4 bg-muted rounded w-24"></div>
              <div className="h-8 bg-muted rounded w-32"></div>
            </div>
            <div className="h-12 w-12 bg-muted rounded-full"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${cardColorClass} border-0 shadow-lg transition-all duration-300 hover:shadow-xl card-with-circles ${className}`}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="text-sm opacity-90 font-medium">
              Account Balance
            </p>
            <div className="flex items-center gap-2">
              <h3 className="text-2xl font-bold">
                {formatBalance(balance)}
              </h3>
              {isLowBalance ? (
                <TrendingDown className={`h-5 w-5 ${iconColorClass}`} />
              ) : (
                <TrendingUp className={`h-5 w-5 ${iconColorClass}`} />
              )}
            </div>
            {isLowBalance && (
              <p className="text-xs opacity-75">
                Low balance - consider topping up
              </p>
            )}
          </div>
          <div className={`p-3 rounded-full bg-white/20 ${iconColorClass}`}>
            <Wallet className="h-6 w-6" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}