const mongoose = require('mongoose');

const billPaymentSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  providerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'BillProvider',
    required: true
  },
  category: {
    type: String,
    required: true,
    enum: ['airtime', 'data', 'electricity', 'cable_tv', 'internet', 'flight', 'betting', 'education']
  },
  amount: {
    type: Number,
    required: true
  },
  fee: {
    type: Number,
    default: 0
  },
  totalAmount: {
    type: Number,
    required: true
  },
  reference: {
    type: String,
    required: true,
    unique: true
  },
  customerIdentifier: {
    type: String,
    required: true // phone number, meter number, etc.
  },
  customerName: {
    type: String
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'cancelled'],
    default: 'pending'
  },
  providerReference: {
    type: String
  },
  providerResponse: {
    type: Object
  },
  metadata: {
    type: Object,
    default: {}
  },
  completedAt: {
    type: Date
  },
  failedAt: {
    type: Date
  },
  errorMessage: {
    type: String
  }
}, {
  timestamps: true
});

// Index for efficient queries
billPaymentSchema.index({ userId: 1, status: 1 });
billPaymentSchema.index({ reference: 1 });
billPaymentSchema.index({ createdAt: -1 });

module.exports = mongoose.model('BillPayment', billPaymentSchema);