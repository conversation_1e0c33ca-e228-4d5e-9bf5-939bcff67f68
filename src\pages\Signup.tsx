
import { SocialLogin } from '@/components/auth/SocialLogin';
import { EnhancedButton } from "@/components/ui/enhanced-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from '@/hooks/use-auth';
import { CheckCircle, Lock, Mail, Phone, Shield, User } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

const Signup = () => {
  const navigate = useNavigate();
  const { signUp } = useAuth();
  const [loading, setLoading] = useState(false);
  const [userData, setUserData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setUserData(prev => ({ ...prev, [id]: value }));
  };

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      navigate('/dashboard');
    }
  }, [navigate]);

  const handleEmailSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      if (userData.password !== userData.confirmPassword) {
        throw new Error('Passwords do not match');
      }
      
      if (!userData.firstName || !userData.lastName) {
        throw new Error('First name and last name are required');
      }
      
      await signUp(
        userData.email, 
        userData.password, 
        userData.firstName,
        userData.lastName,
        userData.phone
      );
    } catch (error: any) {
      console.error("Signup error:", error);
      toast.error(error.message || "Failed to create account");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 sm:p-6 lg:p-8" style={{ background: 'var(--gradient-hero)' }}>
      <div className="w-full max-w-sm sm:max-w-md lg:max-w-lg xl:max-w-xl">
        
        <div className="w-full p-6 sm:p-8 lg:p-10 animate-scale-in">
          <div className="text-center mb-6 sm:mb-8 lg:mb-10">
            <div className="flex justify-center mb-4">
              <div className="h-12 sm:h-16 lg:h-20 w-auto">
                <img
                  src="/lovable-uploads/371e0f57-a280-42c0-8e1b-50aee7ebddc6.png"
                  alt="Better Interest Logo"
                  className="h-full w-auto object-contain"
                />
              </div>
            </div>
            <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-2 text-white">
              Create Your Account
            </h2>
            <p className="text-sm sm:text-base lg:text-lg text-gray-300 leading-relaxed">
              Join thousands building wealth with Better Interest
            </p>
          </div>
          
          {/* Social Login Section */}
          <SocialLogin mode="signup" className="mb-6 sm:mb-8" />
          
          <form onSubmit={handleEmailSignup} className="space-y-4 sm:space-y-6">
            {/* Enhanced Name Fields */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName" className="text-sm font-semibold text-gray-300">First Name</Label>
                <div className="relative">
                  <User className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="firstName"
                    value={userData.firstName}
                    onChange={handleInputChange}
                    placeholder="John"
                    required
                    className="pl-12 h-11 sm:h-12 lg:h-14 text-sm sm:text-base lg:text-lg rounded-xl border-2 bg-white/10 backdrop-blur-sm transition-all duration-300 focus:border-emerald-400/60 focus:shadow-[0_0_0_4px_rgba(34,197,94,0.1)] hover:bg-white/15 text-white placeholder:text-gray-400"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName" className="text-sm font-semibold text-gray-300">Last Name</Label>
                <div className="relative">
                  <User className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="lastName"
                    value={userData.lastName}
                    onChange={handleInputChange}
                    placeholder="Doe"
                    required
                    className="pl-12 h-11 sm:h-12 lg:h-14 text-sm sm:text-base lg:text-lg rounded-xl border-2 bg-white/10 backdrop-blur-sm transition-all duration-300 focus:border-emerald-400/60 focus:shadow-[0_0_0_4px_rgba(34,197,94,0.1)] hover:bg-white/15 text-white placeholder:text-gray-400"
                  />
                </div>
              </div>
            </div>

            {/* Enhanced Email Field */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-semibold text-gray-300">Email</Label>
              <div className="relative">
                <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="email"
                  type="email"
                  value={userData.email}
                  onChange={handleInputChange}
                  placeholder="<EMAIL>"
                  required
                  className="pl-12 h-11 sm:h-12 lg:h-14 text-sm sm:text-base lg:text-lg rounded-xl border-2 bg-white/10 backdrop-blur-sm transition-all duration-300 focus:border-emerald-400/60 focus:shadow-[0_0_0_4px_rgba(34,197,94,0.1)] hover:bg-white/15 text-white placeholder:text-gray-400"
                />
              </div>
            </div>

            {/* Enhanced Phone Field */}
            <div className="space-y-2">
              <Label htmlFor="phone" className="text-sm font-semibold text-gray-300">Phone Number (Optional)</Label>
              <div className="relative">
                <Phone className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="phone"
                  type="tel"
                  value={userData.phone}
                  onChange={handleInputChange}
                  placeholder="+234 ************"
                  className="pl-12 h-11 sm:h-12 lg:h-14 text-sm sm:text-base lg:text-lg rounded-xl border-2 bg-white/10 backdrop-blur-sm transition-all duration-300 focus:border-emerald-400/60 focus:shadow-[0_0_0_4px_rgba(34,197,94,0.1)] hover:bg-white/15 text-white placeholder:text-gray-400"
                />
              </div>
            </div>

            {/* Enhanced Password Fields */}
            <div className="space-y-3 sm:space-y-4">
              <div className="space-y-2">
                <Label htmlFor="password" className="text-sm font-semibold text-gray-300">Password</Label>
                <div className="relative">
                  <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="password"
                    type="password"
                    value={userData.password}
                    onChange={handleInputChange}
                    placeholder="••••••••"
                    required
                    className="pl-12 h-11 sm:h-12 lg:h-14 text-sm sm:text-base lg:text-lg rounded-xl border-2 bg-white/10 backdrop-blur-sm transition-all duration-300 focus:border-emerald-400/60 focus:shadow-[0_0_0_4px_rgba(34,197,94,0.1)] hover:bg-white/15 text-white placeholder:text-gray-400"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="text-sm font-semibold text-gray-300">Confirm Password</Label>
                <div className="relative">
                  <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={userData.confirmPassword}
                    onChange={handleInputChange}
                    placeholder="••••••••"
                    required
                    className="pl-12 h-11 sm:h-12 lg:h-14 text-sm sm:text-base lg:text-lg rounded-xl border-2 bg-white/10 backdrop-blur-sm transition-all duration-300 focus:border-emerald-400/60 focus:shadow-[0_0_0_4px_rgba(34,197,94,0.1)] hover:bg-white/15 text-white placeholder:text-gray-400"
                  />
                </div>
              </div>
            </div>

            {/* Terms and Conditions */}
            <div className="text-xs sm:text-sm text-gray-400 leading-relaxed">
              By creating an account, you agree to our{" "}
              <button className="text-emerald-400 hover:text-emerald-300 underline">
                Terms of Service
              </button>{" "}
              and{" "}
              <button className="text-emerald-400 hover:text-emerald-300 underline">
                Privacy Policy
              </button>
            </div>

            {/* Enhanced Submit Button */}
            <EnhancedButton 
              type="submit" 
              className="w-full h-12 sm:h-14 lg:h-16 text-base sm:text-lg lg:text-xl font-semibold rounded-xl bg-gradient-to-r from-emerald-500 to-emerald-400 hover:from-emerald-600 hover:to-emerald-500 text-white border-0 shadow-lg hover:shadow-emerald-500/25 transition-all duration-300"
              loading={loading}
              disabled={loading}
            >
              {loading ? "Creating account..." : "Create Account"}
            </EnhancedButton>
          </form>
          
          <div className="mt-6 sm:mt-8 text-center">
            <p className="text-sm sm:text-base lg:text-lg text-gray-300">
              Already have an account?{" "}
              <button
                onClick={() => navigate('/login')}
                className="text-emerald-400 hover:text-emerald-300 font-semibold transition-colors duration-200 hover:underline"
              >
                Sign in
              </button>
            </p>
          </div>
        </div>

        {/* Trust indicators */}
        <div className="flex items-center justify-center gap-3 sm:gap-4 lg:gap-6 mt-4 sm:mt-6 text-xs sm:text-sm lg:text-base text-gray-400">
          <div className="flex items-center gap-1 sm:gap-2">
            <Shield className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 text-emerald-400" />
            <span>Secure</span>
          </div>
          <div className="flex items-center gap-1 sm:gap-2">
            <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 text-emerald-400" />
            <span>Verified</span>
          </div>
        </div>
        
        {/* Regulatory Badge */}
        <div className="mt-4 sm:mt-6 lg:mt-8 pt-4 sm:pt-6 border-t border-white/20">
          <div className="flex justify-center mb-3 sm:mb-4">
            <img 
              src="/lovable-uploads/ba52c32b-b343-4d80-ae98-51c43a63c65c.png" 
              alt="NDIC & AMAC MFB Regulatory Badge" 
              className="h-10 sm:h-12 lg:h-14 xl:h-16 w-auto object-contain opacity-90 hover:opacity-100 transition-opacity duration-300" 
            />
          </div>
          <p className="text-xs sm:text-sm lg:text-base text-gray-400 text-center leading-relaxed">
            NDIC Insured • AMAC MFB Licensed • Your deposits are protected
          </p>
        </div>
      </div>
    </div>
  );
};

export default Signup;
