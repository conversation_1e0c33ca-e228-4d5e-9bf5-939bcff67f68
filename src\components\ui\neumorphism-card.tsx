import * as React from "react"
import { cn } from "@/lib/utils"

const NeumorphismCard = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "rounded-none bg-background/95 backdrop-blur-sm transition-all duration-300",
      "shadow-[inset_8px_8px_16px_rgba(0,0,0,0.1),inset_-8px_-8px_16px_rgba(255,255,255,0.1)]",
      "dark:shadow-[inset_8px_8px_16px_rgba(0,0,0,0.3),inset_-8px_-8px_16px_rgba(255,255,255,0.02)]",
      "hover:shadow-[inset_12px_12px_24px_rgba(0,0,0,0.15),inset_-12px_-12px_24px_rgba(255,255,255,0.15)]",
      "dark:hover:shadow-[inset_12px_12px_24px_rgba(0,0,0,0.4),inset_-12px_-12px_24px_rgba(255,255,255,0.03)]",
      "active:shadow-[inset_16px_16px_32px_rgba(0,0,0,0.2),inset_-16px_-16px_32px_rgba(255,255,255,0.05)] active:scale-[0.98]",
      "border border-white/10 dark:border-white/5",
      className
    )}
    {...props}
  />
))
NeumorphismCard.displayName = "NeumorphismCard"

const NeumorphismCardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex flex-col space-y-1.5 p-4 sm:p-6 relative",
      "after:content-[''] after:absolute after:left-4 after:right-4 after:bottom-0 after:h-[1px]",
      "after:bg-gradient-to-r after:from-transparent after:via-primary/20 after:to-transparent",
      className
    )}
    {...props}
  />
))
NeumorphismCardHeader.displayName = "NeumorphismCardHeader"

const NeumorphismCardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-lg sm:text-xl font-semibold leading-none tracking-tight",
      "bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent",
      className
    )}
    {...props}
  />
))
NeumorphismCardTitle.displayName = "NeumorphismCardTitle"

const NeumorphismCardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn(
      "text-sm text-muted-foreground/80",
      className
    )}
    {...props}
  />
))
NeumorphismCardDescription.displayName = "NeumorphismCardDescription"

const NeumorphismCardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-4 sm:p-6 pt-0", className)} {...props} />
))
NeumorphismCardContent.displayName = "NeumorphismCardContent"

const NeumorphismCardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex items-center p-4 sm:p-6 pt-0",
      className
    )}
    {...props}
  />
))
NeumorphismCardFooter.displayName = "NeumorphismCardFooter"

// Enhanced Neumorphism Button
const NeumorphismButton = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    variant?: "default" | "primary" | "secondary" | "ghost"
    size?: "sm" | "md" | "lg"
  }
>(({ className, variant = "default", size = "md", ...props }, ref) => {
  const variants = {
    default: "bg-background text-foreground hover:bg-background/90",
    primary: "bg-background text-primary hover:bg-background/90",
    secondary: "bg-background text-secondary hover:bg-background/90",
    ghost: "bg-transparent text-foreground hover:bg-background/50"
  }

  const sizes = {
    sm: "h-8 px-3 text-sm",
    md: "h-10 px-4 py-2 text-sm",
    lg: "h-12 px-6 py-3 text-base"
  }

  return (
    <button
      ref={ref}
      className={cn(
        "inline-flex items-center justify-center font-medium transition-all duration-200 rounded-none",
        "shadow-[inset_4px_4px_8px_rgba(0,0,0,0.1),inset_-4px_-4px_8px_rgba(255,255,255,0.1)]",
        "dark:shadow-[inset_4px_4px_8px_rgba(0,0,0,0.2),inset_-4px_-4px_8px_rgba(255,255,255,0.02)]",
        "hover:shadow-[inset_6px_6px_12px_rgba(0,0,0,0.15),inset_-6px_-6px_12px_rgba(255,255,255,0.15)]",
        "dark:hover:shadow-[inset_6px_6px_12px_rgba(0,0,0,0.3),inset_-6px_-6px_12px_rgba(255,255,255,0.03)]",
        "active:shadow-[inset_8px_8px_16px_rgba(0,0,0,0.2),inset_-8px_-8px_16px_rgba(255,255,255,0.05)]",
        "active:scale-[0.96] hover:scale-[1.01]",
        "border border-white/10 dark:border-white/5",
        "disabled:opacity-50 disabled:pointer-events-none",
        "focus:outline-none focus:ring-2 focus:ring-primary/50",
        variants[variant],
        sizes[size],
        className
      )}
      {...props}
    />
  )
})
NeumorphismButton.displayName = "NeumorphismButton"

export {
  NeumorphismCard,
  NeumorphismCardHeader,
  NeumorphismCardFooter,
  NeumorphismCardTitle,
  NeumorphismCardDescription,
  NeumorphismCardContent,
  NeumorphismButton,
}