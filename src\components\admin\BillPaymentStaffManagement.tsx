import React, { useState, useEffect } from "react";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  CreditCard,
  Users,
  Shield,
  UserPlus,
  Edit,
  Trash2,
  Search,
  Plus,
  Settings,
  Eye,
  Calendar,
  AlertCircle
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import axios from "axios";

interface StaffMember {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  assignments: StaffAssignment[];
  createdAt: string;
  isActive: boolean;
}

interface StaffRole {
  _id: string;
  name: string;
  description: string;
  module: string;
  permissions: Permission[];
  priority: number;
  isActive: boolean;
  createdBy: {
    firstName: string;
    lastName: string;
    email: string;
  };
}

interface Permission {
  action: string;
  resource: string;
  conditions?: Record<string, string>;
}

interface StaffAssignment {
  _id: string;
  userId: string;
  roleId: StaffRole;
  assignedBy: {
    firstName: string;
    lastName: string;
  };
  module: string;
  customPermissions: CustomPermission[];
  isActive: boolean;
  startDate: string;
  endDate?: string;
  notes?: string;
}

interface CustomPermission {
  action: string;
  resource: string;
  granted: boolean;
}

const MODULES = [
  { value: 'bill_payment', label: 'Bill Payment' },
  { value: 'user_management', label: 'User Management' },
  { value: 'savings_plans', label: 'Savings Plans' },
  { value: 'withdrawals', label: 'Withdrawals' },
  { value: 'kyc', label: 'KYC' },
  { value: 'notifications', label: 'Notifications' },
  { value: 'analytics', label: 'Analytics' },
  { value: 'all', label: 'All Modules' }
];

const ACTIONS = ['view', 'create', 'edit', 'delete', 'approve', 'reject', 'export', 'import', 'assign'];
const RESOURCES = ['users', 'staff', 'roles', 'bills', 'providers', 'payments', 'withdrawals', 'savings', 'kyc', 'notifications', 'reports'];

const BillPaymentStaffManagement = () => {
  const [staffMembers, setStaffMembers] = useState<StaffMember[]>([]);
  const [roles, setRoles] = useState<StaffRole[]>([]);
  const [selectedModule, setSelectedModule] = useState('bill_payment');
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  // Dialog states
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [roleDialogOpen, setRoleDialogOpen] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState<StaffMember | null>(null);
  const [selectedRole, setSelectedRole] = useState<StaffRole | null>(null);

  // Form states
  const [roleForm, setRoleForm] = useState({
    name: '',
    description: '',
    module: 'bill_payment',
    permissions: [] as Permission[],
    priority: 0
  });

  const [assignmentForm, setAssignmentForm] = useState({
    userId: '',
    roleId: '',
    module: 'bill_payment',
    customPermissions: [] as CustomPermission[],
    notes: '',
    endDate: ''
  });

  // Fetch data
  useEffect(() => {
    fetchStaffAndRoles();
  }, [selectedModule]);

  const fetchStaffAndRoles = async () => {
    setIsLoading(true);
    try {
      const [staffResponse, rolesResponse] = await Promise.all([
        axios.get('/api/staff-management/staff', {
          params: { module: selectedModule }
        }),
        axios.get('/api/staff-management/roles', {
          params: { module: selectedModule }
        })
      ]);

      setStaffMembers(staffResponse.data.data);
      setRoles(rolesResponse.data.data);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter staff based on search
  const filteredStaff = staffMembers.filter(staff =>
    `${staff.firstName} ${staff.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
    staff.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle role creation/update
  const handleRoleSubmit = async () => {
    setIsLoading(true);
    try {
      if (selectedRole) {
        await axios.put(`/api/staff-management/roles/${selectedRole._id}`, roleForm);
        toast.success('Role updated successfully');
      } else {
        await axios.post('/api/staff-management/roles', roleForm);
        toast.success('Role created successfully');
      }
      
      setRoleDialogOpen(false);
      resetRoleForm();
      fetchStaffAndRoles();
    } catch (error) {
      console.error('Error saving role:', error);
      toast.error('Failed to save role');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle staff assignment
  const handleAssignmentSubmit = async () => {
    setIsLoading(true);
    try {
      await axios.post('/api/staff-management/assign', assignmentForm);
      toast.success('Role assigned successfully');
      setAssignDialogOpen(false);
      resetAssignmentForm();
      fetchStaffAndRoles();
    } catch (error) {
      console.error('Error assigning role:', error);
      toast.error('Failed to assign role');
    } finally {
      setIsLoading(false);
    }
  };

  // Reset forms
  const resetRoleForm = () => {
    setRoleForm({
      name: '',
      description: '',
      module: 'bill_payment',
      permissions: [],
      priority: 0
    });
    setSelectedRole(null);
  };

  const resetAssignmentForm = () => {
    setAssignmentForm({
      userId: '',
      roleId: '',
      module: 'bill_payment',
      customPermissions: [],
      notes: '',
      endDate: ''
    });
    setSelectedStaff(null);
  };

  // Handle permission changes
  const handlePermissionChange = (action: string, resource: string, checked: boolean) => {
    if (checked) {
      setRoleForm(prev => ({
        ...prev,
        permissions: [...prev.permissions, { action, resource }]
      }));
    } else {
      setRoleForm(prev => ({
        ...prev,
        permissions: prev.permissions.filter(p => !(p.action === action && p.resource === resource))
      }));
    }
  };

  // Open dialogs
  const openRoleDialog = (role?: StaffRole) => {
    if (role) {
      setSelectedRole(role);
      setRoleForm({
        name: role.name,
        description: role.description,
        module: role.module,
        permissions: role.permissions,
        priority: role.priority
      });
    } else {
      resetRoleForm();
    }
    setRoleDialogOpen(true);
  };

  const openAssignDialog = (staff: StaffMember) => {
    setSelectedStaff(staff);
    setAssignmentForm(prev => ({
      ...prev,
      userId: staff._id,
      module: selectedModule
    }));
    setAssignDialogOpen(true);
  };

  // Remove assignment
  const removeAssignment = async (assignmentId: string) => {
    try {
      await axios.delete(`/api/staff-management/assign/${assignmentId}`);
      toast.success('Assignment removed successfully');
      fetchStaffAndRoles();
    } catch (error) {
      console.error('Error removing assignment:', error);
      toast.error('Failed to remove assignment');
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Bill Payment Staff Management</h2>
          <p className="text-muted-foreground">
            Manage staff roles and permissions for bill payment operations
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => openRoleDialog()}>
            <Shield className="mr-2 h-4 w-4" />
            Create Role
          </Button>
          <Button onClick={() => setAssignDialogOpen(true)}>
            <UserPlus className="mr-2 h-4 w-4" />
            Assign Staff
          </Button>
        </div>
      </div>

      {/* Module Selector */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Module Selection
          </CardTitle>
          <CardDescription>
            Select the module to manage staff roles and assignments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Select value={selectedModule} onValueChange={setSelectedModule}>
            <SelectTrigger className="w-full max-w-xs">
              <SelectValue placeholder="Select module" />
            </SelectTrigger>
            <SelectContent>
              {MODULES.map(module => (
                <SelectItem key={module.value} value={module.value}>
                  {module.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      <Tabs defaultValue="staff" className="space-y-4">
        <TabsList>
          <TabsTrigger value="staff" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Staff Assignments
          </TabsTrigger>
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Roles & Permissions
          </TabsTrigger>
        </TabsList>

        {/* Staff Assignments Tab */}
        <TabsContent value="staff">
          <Card>
            <CardHeader>
              <CardTitle>Staff Assignments</CardTitle>
              <CardDescription>
                View and manage staff role assignments for {MODULES.find(m => m.value === selectedModule)?.label}
              </CardDescription>
              <div className="flex items-center gap-2 mt-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search staff..."
                    className="pl-10"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Staff Member</TableHead>
                      <TableHead>Current Role</TableHead>
                      <TableHead>Permissions</TableHead>
                      <TableHead>Assigned Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredStaff.map((staff) => {
                      const moduleAssignment = staff.assignments.find(a => a.module === selectedModule);
                      
                      return (
                        <TableRow key={staff._id}>
                          <TableCell>
                            <div className="flex items-center gap-3">
                              <Avatar className="h-9 w-9">
                                <AvatarFallback>
                                  {staff.firstName[0]}{staff.lastName[0]}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-medium">{staff.firstName} {staff.lastName}</div>
                                <div className="text-sm text-muted-foreground">{staff.email}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            {moduleAssignment ? (
                              <Badge variant="outline">
                                {moduleAssignment.roleId.name}
                              </Badge>
                            ) : (
                              <Badge variant="secondary">No Assignment</Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            {moduleAssignment && (
                              <div className="flex flex-wrap gap-1 max-w-md">
                                {moduleAssignment.roleId.permissions.slice(0, 3).map((perm, idx) => (
                                  <Badge key={idx} variant="outline" className="text-xs">
                                    {perm.action}:{perm.resource}
                                  </Badge>
                                ))}
                                {moduleAssignment.roleId.permissions.length > 3 && (
                                  <Badge variant="outline" className="text-xs">
                                    +{moduleAssignment.roleId.permissions.length - 3} more
                                  </Badge>
                                )}
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            {moduleAssignment ? (
                              new Date(moduleAssignment.startDate).toLocaleDateString()
                            ) : (
                              "-"
                            )}
                          </TableCell>
                          <TableCell>
                            {moduleAssignment ? (
                              <Badge variant={moduleAssignment.isActive ? "default" : "secondary"}>
                                {moduleAssignment.isActive ? "Active" : "Inactive"}
                              </Badge>
                            ) : (
                              <Badge variant="secondary">Unassigned</Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openAssignDialog(staff)}
                              >
                                <Edit className="h-4 w-4 mr-1" />
                                {moduleAssignment ? 'Update' : 'Assign'}
                              </Button>
                              {moduleAssignment && (
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={() => removeAssignment(moduleAssignment._id)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Roles Tab */}
        <TabsContent value="roles">
          <Card>
            <CardHeader>
              <CardTitle>Roles & Permissions</CardTitle>
              <CardDescription>
                Manage roles and their permissions for {MODULES.find(m => m.value === selectedModule)?.label}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Role</TableHead>
                      <TableHead>Module</TableHead>
                      <TableHead>Permissions</TableHead>
                      <TableHead>Priority</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {roles.map((role) => (
                      <TableRow key={role._id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{role.name}</div>
                            <div className="text-sm text-muted-foreground">{role.description}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {MODULES.find(m => m.value === role.module)?.label}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1 max-w-md">
                            {role.permissions.slice(0, 3).map((perm, idx) => (
                              <Badge key={idx} variant="outline" className="text-xs">
                                {perm.action}:{perm.resource}
                              </Badge>
                            ))}
                            {role.permissions.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{role.permissions.length - 3} more
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">{role.priority}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={role.isActive ? "default" : "secondary"}>
                            {role.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openRoleDialog(role)}
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Role Creation/Edit Dialog */}
      <Dialog open={roleDialogOpen} onOpenChange={setRoleDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedRole ? 'Edit Role' : 'Create New Role'}
            </DialogTitle>
            <DialogDescription>
              Define role permissions for {MODULES.find(m => m.value === roleForm.module)?.label}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role-name" className="text-right">Name</Label>
              <Input
                id="role-name"
                className="col-span-3"
                value={roleForm.name}
                onChange={(e) => setRoleForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Role name"
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role-description" className="text-right">Description</Label>
              <Textarea
                id="role-description"
                className="col-span-3"
                value={roleForm.description}
                onChange={(e) => setRoleForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Role description"
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role-module" className="text-right">Module</Label>
              <Select value={roleForm.module} onValueChange={(value) => setRoleForm(prev => ({ ...prev, module: value }))}>
                <SelectTrigger className="col-span-3">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {MODULES.map(module => (
                    <SelectItem key={module.value} value={module.value}>
                      {module.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role-priority" className="text-right">Priority</Label>
              <Input
                id="role-priority"
                type="number"
                min="0"
                max="10"
                className="col-span-3"
                value={roleForm.priority}
                onChange={(e) => setRoleForm(prev => ({ ...prev, priority: parseInt(e.target.value) || 0 }))}
              />
            </div>
            
            <div className="space-y-4">
              <Label>Permissions</Label>
              <div className="border rounded-md p-4 max-h-[300px] overflow-y-auto">
                <div className="grid grid-cols-1 gap-4">
                  {ACTIONS.map(action => (
                    <div key={action} className="space-y-2">
                      <Label className="font-medium capitalize">{action}</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {RESOURCES.map(resource => {
                          const hasPermission = roleForm.permissions.some(p => p.action === action && p.resource === resource);
                          return (
                            <div key={`${action}-${resource}`} className="flex items-center space-x-2">
                              <Checkbox
                                id={`${action}-${resource}`}
                                checked={hasPermission}
                                onCheckedChange={(checked) => handlePermissionChange(action, resource, checked as boolean)}
                              />
                              <label
                                htmlFor={`${action}-${resource}`}
                                className="text-sm capitalize cursor-pointer"
                              >
                                {resource}
                              </label>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setRoleDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleRoleSubmit} disabled={isLoading || !roleForm.name || !roleForm.description}>
              {isLoading ? "Saving..." : selectedRole ? "Update Role" : "Create Role"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Assignment Dialog */}
      <Dialog open={assignDialogOpen} onOpenChange={setAssignDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Assign Role to Staff</DialogTitle>
            <DialogDescription>
              {selectedStaff && `Assigning role to ${selectedStaff.firstName} ${selectedStaff.lastName}`}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="assign-role" className="text-right">Role</Label>
              <Select value={assignmentForm.roleId} onValueChange={(value) => setAssignmentForm(prev => ({ ...prev, roleId: value }))}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.filter(r => r.isActive).map(role => (
                    <SelectItem key={role._id} value={role._id}>
                      {role.name} - {role.description}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="assign-notes" className="text-right">Notes</Label>
              <Textarea
                id="assign-notes"
                className="col-span-3"
                value={assignmentForm.notes}
                onChange={(e) => setAssignmentForm(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Assignment notes (optional)"
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="assign-end-date" className="text-right">End Date</Label>
              <Input
                id="assign-end-date"
                type="date"
                className="col-span-3"
                value={assignmentForm.endDate}
                onChange={(e) => setAssignmentForm(prev => ({ ...prev, endDate: e.target.value }))}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setAssignDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleAssignmentSubmit} 
              disabled={isLoading || !assignmentForm.roleId}
            >
              {isLoading ? "Assigning..." : "Assign Role"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default BillPaymentStaffManagement;