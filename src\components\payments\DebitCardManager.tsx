import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { paystackService } from '@/services/paystack';
import { useToast } from '@/hooks/use-toast';
import { CreditCard, Plus, Trash2, Shield } from 'lucide-react';

interface DebitCardManagerProps {
  userId: string;
  userEmail: string;
  userName: string;
}

interface SavedCard {
  id: string;
  authorizationCode: string;
  cardType: string;
  last4: string;
  expMonth: string;
  expYear: string;
  bank: string;
  isDefault: boolean;
  isActive: boolean;
}

export const DebitCardManager: React.FC<DebitCardManagerProps> = ({
  userId,
  userEmail,
  userName
}) => {
  const { toast } = useToast();
  const [savedCards, setSavedCards] = useState<SavedCard[]>([]);
  const [isAddingCard, setIsAddingCard] = useState(false);
  const [testAmount] = useState(100); // ₦1 test charge for card verification

  // Add new debit card
  const handleAddCard = async () => {
    setIsAddingCard(true);
    try {
      const paymentData = {
        email: userEmail,
        amount: testAmount * 100, // ₦1 in kobo for verification
        reference: paystackService.generateReference(),
        metadata: {
          userId,
          type: 'card_verification',
          custom_fields: [
            {
              display_name: "Purpose",
              variable_name: "purpose",
              value: "Card Authorization"
            }
          ]
        }
      };

      const result = await paystackService.initializePayment(paymentData);
      
      if (result.status) {
        paystackService.openPaystackPopup(
          paymentData,
          async (reference) => {
            // Verify payment and save card authorization
            try {
              const verification = await paystackService.verifyPayment(reference.reference);
              
              if (verification.status && verification.data.status === 'success') {
                const authorization = verification.data.authorization;
                
                const newCard: SavedCard = {
                  id: `card_${Date.now()}`,
                  authorizationCode: authorization.authorization_code,
                  cardType: authorization.card_type,
                  last4: authorization.last4,
                  expMonth: authorization.exp_month,
                  expYear: authorization.exp_year,
                  bank: authorization.bank,
                  isDefault: savedCards.length === 0,
                  isActive: true
                };

                setSavedCards(prev => [...prev, newCard]);
                
                toast({
                  title: "Card Added Successfully",
                  description: "Your debit card has been saved for future payments",
                });
              }
            } catch (error) {
              toast({
                title: "Verification Failed",
                description: "Failed to verify card. Please try again.",
                variant: "destructive"
              });
            }
          },
          () => {
            toast({
              title: "Card Addition Cancelled",
              description: "Card addition was cancelled",
              variant: "destructive"
            });
          }
        );
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add card. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsAddingCard(false);
    }
  };

  // Set default card
  const handleSetDefault = (cardId: string) => {
    setSavedCards(prev => prev.map(card => ({
      ...card,
      isDefault: card.id === cardId
    })));
    
    toast({
      title: "Default Card Updated",
      description: "Default payment card has been updated",
    });
  };

  // Remove card
  const handleRemoveCard = (cardId: string) => {
    setSavedCards(prev => {
      const filtered = prev.filter(card => card.id !== cardId);
      // If we removed the default card, make the first remaining card default
      if (filtered.length > 0 && !filtered.some(card => card.isDefault)) {
        filtered[0].isDefault = true;
      }
      return filtered;
    });
    
    toast({
      title: "Card Removed",
      description: "Card has been removed from your account",
    });
  };

  // Process automatic payment using saved card
  const processAutomaticPayment = async (amount: number, purpose: string, cardId?: string): Promise<boolean> => {
    const card = cardId 
      ? savedCards.find(c => c.id === cardId)
      : savedCards.find(c => c.isDefault);

    if (!card) {
      toast({
        title: "No Card Available",
        description: "Please add a debit card first",
        variant: "destructive"
      });
      return false;
    }

    try {
      // Create charge using saved authorization
      const chargeData = {
        authorization_code: card.authorizationCode,
        email: userEmail,
        amount: amount * 100, // Convert to kobo
        reference: paystackService.generateReference(),
        metadata: {
          userId,
          purpose,
          cardId: card.id
        }
      };

      // In a real implementation, this would be a server-side call
      const response = await fetch('https://api.paystack.co/transaction/charge_authorization', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(chargeData)
      });

      const result = await response.json();
      
      if (result.status && result.data.status === 'success') {
        toast({
          title: "Payment Successful",
          description: `₦${amount.toLocaleString()} charged successfully for ${purpose}`,
        });
        return true;
      } else {
        toast({
          title: "Payment Failed",
          description: result.message || "Failed to process automatic payment",
          variant: "destructive"
        });
        return false;
      }
    } catch (error) {
      console.error('Automatic payment error:', error);
      toast({
        title: "Payment Error",
        description: "Failed to process automatic payment",
        variant: "destructive"
      });
      return false;
    }
  };

  // Load Paystack script
  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://js.paystack.co/v1/inline.js';
    script.async = true;
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  // Expose automatic payment function globally for use by other components
  useEffect(() => {
    (window as any).processAutomaticPayment = processAutomaticPayment;
  }, [savedCards]);

  return (
    <div className="space-y-6">
      {/* Add New Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Manage Debit Cards
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Add your debit card for automatic savings and payments. We'll charge ₦1 to verify your card.
            </p>
            <Button 
              onClick={handleAddCard} 
              disabled={isAddingCard}
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              {isAddingCard ? 'Adding Card...' : 'Add New Debit Card'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Saved Cards */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Saved Cards</h3>
        {savedCards.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <CreditCard className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">No saved cards yet. Add a card to enable automatic payments.</p>
            </CardContent>
          </Card>
        ) : (
          savedCards.map((card) => (
            <Card key={card.id}>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <CreditCard className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">
                          **** **** **** {card.last4}
                        </span>
                        {card.isDefault && (
                          <Badge variant="default" className="text-xs">
                            Default
                          </Badge>
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground space-y-1">
                        <div>{card.cardType.toUpperCase()} • {card.bank}</div>
                        <div>Expires {card.expMonth}/{card.expYear}</div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-green-500" />
                    {!card.isDefault && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSetDefault(card.id)}
                      >
                        Set Default
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveCard(card.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Security Notice */}
      <Card className="border-green-200 bg-green-50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <Shield className="h-5 w-5 text-green-600 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-green-800 mb-1">Secure & Encrypted</p>
              <p className="text-green-700">
                Your card details are securely stored by Paystack using bank-level encryption. 
                We never store your full card details on our servers.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};