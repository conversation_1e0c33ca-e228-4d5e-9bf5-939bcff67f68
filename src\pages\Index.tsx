
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { <PERSON>R<PERSON>, Award, BarChart, CheckCircle, Globe, Lock, PiggyBank, Shield, Smartphone, Star, TrendingUp, Users } from "lucide-react";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";

const Index = () => {
  const [testimonials, setTestimonials] = useState<any[]>([]);
  const [testimonialsLoading, setTestimonialsLoading] = useState(true);

  useEffect(() => {
    const fetchTestimonials = async () => {
      setTestimonialsLoading(true);
      try {
        const response = await fetch('/api/v1/testimonials/featured');
        if (response.ok) {
          const data = await response.json();
          setTestimonials(data.data || []);
        }
      } catch (error) {
        console.error('Error fetching testimonials:', error);
        setTestimonials([]);
      } finally {
        setTestimonialsLoading(false);
      }
    };

    fetchTestimonials();
  }, []);
  
  // --- Data for the new Animated Features Hub ---
  const features = [
    { id: 'feature-1', title: 'Automated Savings', icon: <PiggyBank className="w-8 h-8 text-emerald-300" />, position: { top: '0%', left: '50%', transform: 'translate(-50%, -50%)' }, lineEnd: { x: '50%', y: '0%' } },
    { id: 'feature-2', title: 'High-Yield Vaults', icon: <TrendingUp className="w-8 h-8 text-emerald-300" />, position: { top: '30%', left: '95%', transform: 'translate(-50%, -50%)' }, lineEnd: { x: '95%', y: '30%' } },
    { id: 'feature-3', title: 'Goal Setting', icon: <Award className="w-8 h-8 text-emerald-300" />, position: { top: '80%', left: '95%', transform: 'translate(-50%, -50%)' }, lineEnd: { x: '95%', y: '80%' } },
    { id: 'feature-4', title: 'Portfolio Tracking', icon: <BarChart className="w-8 h-8 text-emerald-300" />, position: { top: '100%', left: '50%', transform: 'translate(-50%, -50%)' }, lineEnd: { x: '50%', y: '100%' } },
    { id: 'feature-5', title: '24/7 Support', icon: <Users className="w-8 h-8 text-emerald-300" />, position: { top: '80%', left: '5%', transform: 'translate(-50%, -50%)' }, lineEnd: { x: '5%S', y: '80%' } },
    { id: 'feature-6', title: 'Secure Investments', icon: <Lock className="w-8 h-8 text-emerald-300" />, position: { top: '30%', left: '5%', transform: 'translate(-50%, -50%)' }, lineEnd: { x: '5%', y: '30%' } },
  ];

  return (
    <div className="min-h-screen flex flex-col" style={{ background: 'var(--gradient-hero)' }}>
      {/* Add CSS animations for the hub directly */}
      <style>
        {`
          @keyframes pulse-glow {
            0%, 100% { box-shadow: 0 0 20px 5px rgba(52, 211, 153, 0.3); }
            50% { box-shadow: 0 0 35px 10px rgba(52, 211, 153, 0.5); }
          }
          .animate-pulse-glow {
            animation: pulse-glow 4s infinite ease-in-out;
          }
          @keyframes flow {
            to { stroke-dashoffset: 1000; }
          }
          .animated-line {
            stroke-dasharray: 5 10;
            stroke-linecap: round;
            animation: flow 20s linear infinite;
          }
          .feature-node:hover .node-icon {
            transform: scale(1.1);
            box-shadow: 0 0 25px rgba(52, 211, 153, 0.7);
          }
        `}
      </style>

      {/* Navigation */}
      <header className="border-b backdrop-blur-md sticky top-0 z-50" style={{
        background: 'rgba(255, 255, 255, 0.1)',
        borderColor: 'hsl(142, 76%, 46% / 0.2)'
      }}>
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-12 w-auto">
              <img
                src="/lovable-uploads/new-logo.svg"
                alt="Better Interest Logo"
                className="h-full w-auto object-contain"
              />
            </div>
          </div>
          
          <nav className="hidden md:flex gap-8">
            <a href="#features" className="text-sm font-medium text-gray-300 hover:text-emerald-400 transition-colors">Features</a>
            <a href="#how-it-works" className="text-sm font-medium text-gray-300 hover:text-emerald-400 transition-colors">How It Works</a>
            <a href="#security" className="text-sm font-medium text-gray-300 hover:text-emerald-400 transition-colors">Security</a>
            <a href="#testimonials" className="text-sm font-medium text-gray-300 hover:text-emerald-400 transition-colors">Testimonials</a>
          </nav>
          
          <div className="flex items-center gap-3">
            <ThemeToggle />
            <Link to="/login">
              <Button variant="outline" className="border-emerald-400/50 text-emerald-300 hover:bg-emerald-400/10">
                Log In
              </Button>
            </Link>
            <Link to="/signup">
              <Button className="bg-gradient-to-r from-emerald-500 to-emerald-400 hover:from-emerald-600 hover:to-emerald-500 text-white">
                Get Started
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative overflow-hidden min-h-screen flex items-center">
        {/* Geometric Background Pattern */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0" style={{
            backgroundImage: `
              radial-gradient(circle at 25% 25%, hsl(142, 76%, 46% / 0.1) 0%, transparent 50%),
              radial-gradient(circle at 75% 75%, hsl(142, 76%, 46% / 0.1) 0%, transparent 50%),
              linear-gradient(45deg, transparent 40%, hsl(142, 76%, 46% / 0.05) 50%, transparent 60%)
            `,
            backgroundSize: '100px 100px, 150px 150px, 200px 200px'
          }}></div>
          {/* Hexagonal Grid Overlay */}
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2322c55e' fill-opacity='0.05'%3E%3Cpolygon points='30 15 45 30 30 45 15 30'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '60px 60px'
          }}></div>
        </div>

        {/* Glass Morphism Background Elements */}
        <div className="absolute top-20 left-10 w-72 h-72 rounded-full" style={{ background: 'var(--gradient-glow)' }}></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 rounded-full" style={{ background: 'var(--gradient-glow)' }}></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="flex flex-col lg:flex-row items-center gap-16">
            <div className="lg:w-1/2 space-y-8 animate-fade-in">
              {/* Trust Badge */}
              <div className="flex items-center gap-4">
                <div className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-full backdrop-blur-md border" style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  borderColor: 'hsl(142, 76%, 46% / 0.3)',
                  color: 'hsl(142, 76%, 70%)'
                }}>
                  <Shield className="w-4 h-4" />
                  Bank-Level Security
                </div>
                <div className="flex items-center gap-1">
                  {Array(5).fill(0).map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  ))}
                  <span className="text-sm text-gray-300 ml-2">4.9/5 Rating</span>
                </div>
              </div>
              
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight tracking-tight text-white">
                Better Interest, <span className="bg-gradient-to-r from-emerald-400 to-emerald-300 bg-clip-text text-transparent">Better Future</span>
              </h1>
              
              <p className="text-xl text-gray-300 max-w-lg leading-relaxed">
                Transform your financial future with Nigeria's most trusted digital savings platform. Join over 50,000 Nigerians building wealth with Better Interest.
              </p>

              {/* Key Benefits */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {[
                  { icon: <TrendingUp className="w-5 h-5" />, text: "15% Annual Returns" },
                  { icon: <Shield className="w-5 h-5" />, text: "NDIC Insured" },
                  { icon: <Smartphone className="w-5 h-5" />, text: "Mobile First" },
                  { icon: <Globe className="w-5 h-5" />, text: "Available 24/7" }
                ].map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3 text-emerald-300">
                    {benefit.icon}
                    <span className="font-medium">{benefit.text}</span>
                  </div>
                ))}
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4 pt-6">
                <Link to="/signup">
                  <Button size="lg" className="bg-gradient-to-r from-emerald-500 to-emerald-400 hover:from-emerald-600 hover:to-emerald-500 text-white border-0 shadow-lg hover:shadow-emerald-500/25 transition-all duration-300 text-lg px-8 py-6">
                    Start Saving
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Button size="lg" variant="outline" className="border-emerald-400/50 text-emerald-300 hover:bg-emerald-400/10 backdrop-blur-sm text-lg px-8 py-6">
                  Learn How It Works
                </Button>
              </div>

              {/* Social Proof */}
              <div className="flex items-center gap-6 pt-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">50K+</div>
                  <div className="text-sm text-gray-400">Active Savers</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">₦2.5B+</div>
                  <div className="text-sm text-gray-400">Total Saved</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">99.9%</div>
                  <div className="text-sm text-gray-400">Uptime</div>
                </div>
              </div>
            </div>
            
            {/*******************************************************************}
            {/* NEW ANIMATED FEATURES HUB - REPLACES THE PHONE MOCKUP         */}
            {/*******************************************************************}
            <div className="lg:w-1/2 flex items-center justify-center min-h-[500px] lg:min-h-full animate-scale-in">
              <div className="w-full max-w-lg aspect-square relative">
                
                {/* 1. SVG Layer for Animated Lines (drawn first, so it's in the back) */}
                <svg viewBox="0 0 100 100" className="absolute inset-0 w-full h-full overflow-visible z-0">
                  <defs>
                    <filter id="glow">
                      <feGaussianBlur stdDeviation="1.5" result="coloredBlur" />
                      <feMerge>
                        <feMergeNode in="coloredBlur" />
                        <feMergeNode in="SourceGraphic" />
                      </feMerge>
                    </filter>
                  </defs>
                  {features.map(feature => (
                    <line
                      key={feature.id}
                      x1="50%" y1="50%"
                      x2={feature.lineEnd.x} y2={feature.lineEnd.y}
                      className="animated-line"
                      stroke="hsl(142, 76%, 46% / 0.4)"
                      strokeWidth="0.5"
                      filter="url(#glow)"
                    />
                  ))}
                </svg>

                {/* 2. Central Hub Logo */}
                <div
                  className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-40 h-40 
                             bg-gray-900/50 backdrop-blur-md border border-emerald-500/30 
                             rounded-full flex items-center justify-center z-20 animate-pulse-glow"
                >
                  <span className="font-bold text-2xl bg-gradient-to-r from-emerald-400 to-emerald-300 bg-clip-text text-transparent">Better Interest</span>
                </div>
                
                {/* 3. Feature Nodes Mapped from Array */}
                {features.map(feature => (
                  <div
                    key={feature.id}
                    id={feature.id}
                    className="absolute z-10 feature-node group"
                    style={{ ...feature.position }}
                  >
                    <div className="w-24 h-24 rounded-full bg-gray-900/40 backdrop-blur-sm 
                                    border border-emerald-500/30 flex items-center justify-center 
                                    flex-col gap-1 p-2 cursor-pointer transition-all duration-300 node-icon">
                      {feature.icon}
                      <span className="text-white text-xs text-center leading-tight opacity-0 group-hover:opacity-100 transition-opacity">
                        {feature.title}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            {/*******************************************************************}
            {/* END OF NEW ANIMATED FEATURES HUB                                */}
            {/*******************************************************************}

          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 relative" style={{ background: 'linear-gradient(180deg, var(--gradient-hero), rgba(0,0,0,0.9))' }}>
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
              Why Choose <span className="bg-gradient-to-r from-emerald-400 to-emerald-300 bg-clip-text text-transparent">Better Interest</span>
            </h2>
            <p className="text-xl text-gray-300 leading-relaxed">
              Experience the perfect blend of traditional Nigerian savings culture and cutting-edge technology
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: <Shield className="h-12 w-12 text-emerald-400" />,
                title: "Bank-Level Security",
                description: "Your money is protected with military-grade encryption and NDIC insurance coverage up to ₦500,000",
                highlight: "NDIC Insured"
              },
              {
                icon: <TrendingUp className="h-12 w-12 text-emerald-400" />,
                title: "High Returns",
                description: "Earn up to 15% annual returns on your savings with our competitive interest rates and investment options",
                highlight: "Up to 15% Returns"
              },
              {
                icon: <Smartphone className="h-12 w-12 text-emerald-400" />,
                title: "Mobile First",
                description: "Save, invest, and manage your money anytime, anywhere with our intuitive mobile app",
                highlight: "24/7 Access"
              },
              {
                icon: <Users className="h-12 w-12 text-emerald-400" />,
                title: "Group Savings",
                description: "Join savings groups or create your own to achieve financial goals together with friends and family",
                highlight: "Social Savings"
              },
              {
                icon: <Lock className="h-12 w-12 text-emerald-400" />,
                title: "Auto-Save",
                description: "Set up automatic daily, weekly, or monthly saves to build wealth consistently without thinking about it",
                highlight: "Hands-Free"
              },
              {
                icon: <Award className="h-12 w-12 text-emerald-400" />,
                title: "Rewards Program",
                description: "Earn points for consistent saving habits and redeem them for exciting rewards and bonuses",
                highlight: "Earn Rewards"
              }
            ].map((feature, index) => (
              <div
                key={index}
                className="relative p-8 rounded-2xl backdrop-blur-md border transition-all duration-300 hover:scale-105 group"
                style={{
                  background: 'rgba(255, 255, 255, 0.05)',
                  borderColor: 'hsl(142, 76%, 46% / 0.2)'
                }}
              >
                <div className="absolute top-4 right-4">
                  <span className="px-2 py-1 text-xs font-medium rounded-full bg-emerald-500/20 text-emerald-300">
                    {feature.highlight}
                  </span>
                </div>
                
                <div className="mb-6 w-fit p-3 rounded-full bg-emerald-500/10">
                  {feature.icon}
                </div>
                
                <h3 className="text-2xl font-bold mb-4 text-white">{feature.title}</h3>
                <p className="text-gray-300 leading-relaxed">{feature.description}</p>
                
                {/* Hover effect */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-emerald-500/5 to-emerald-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section id="how-it-works" className="py-20 bg-black/50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">How It Works</h2>
            <p className="text-xl text-gray-300">Start your savings journey in just 3 simple steps</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 relative">
            {/* Connection Lines */}
            <div className="hidden md:block absolute top-24 left-1/3 right-1/3 h-0.5 bg-gradient-to-r from-emerald-500 to-emerald-400"></div>
            
            {[
              {
                step: "01",
                title: "Sign Up & Verify",
                description: "Create your account in under 2 minutes and complete our simple KYC verification process",
                icon: <Users className="h-10 w-10 text-emerald-400" />
              },
              {
                step: "02",
                title: "Set Your Goals",
                description: "Define your savings targets and choose from our flexible savings plans that suit your lifestyle",
                icon: <BarChart className="h-10 w-10 text-emerald-400" />
              },
              {
                step: "03",
                title: "Start Saving",
                description: "Make automatic or manual contributions and watch your money grow with competitive interest rates",
                icon: <PiggyBank className="h-10 w-10 text-emerald-400" />
              }
            ].map((step, index) => (
              <div key={index} className="relative text-center">
                {/* Step Number */}
                <div className="mx-auto w-16 h-16 rounded-full bg-gradient-to-r from-emerald-500 to-emerald-400 flex items-center justify-center text-white font-bold text-xl mb-6 relative z-10">
                  {step.step}
                </div>
                
                {/* Content Card */}
                <div className="p-6 rounded-2xl backdrop-blur-md border" style={{
                  background: 'rgba(255, 255, 255, 0.05)',
                  borderColor: 'hsl(142, 76%, 46% / 0.2)'
                }}>
                  <div className="mb-4 w-fit mx-auto p-3 rounded-full bg-emerald-500/10">
                    {step.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-white">{step.title}</h3>
                  <p className="text-gray-300">{step.description}</p>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link to="/signup">
              <Button size="lg" className="bg-gradient-to-r from-emerald-500 to-emerald-400 hover:from-emerald-600 hover:to-emerald-500 text-white text-lg px-8 py-6">
                Get Started Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Security Section */}
      <section id="security" className="py-20" style={{ background: 'linear-gradient(180deg, rgba(0,0,0,0.9), var(--gradient-hero))' }}>
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
                Your Money is <span className="bg-gradient-to-r from-emerald-400 to-emerald-300 bg-clip-text text-transparent">100% Safe</span>
              </h2>
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                We use the same security standards as major banks to protect your money and personal information.
              </p>
              
              <div className="space-y-4">
                {[
                  "256-bit SSL encryption for all transactions",
                  "NDIC insurance coverage up to ₦500,000",
                  "Two-factor authentication (2FA)",
                  "Biometric login (fingerprint & face ID)",
                  "Real-time fraud monitoring",
                  "Regulated by CBN and SEC"
                ].map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-emerald-400 flex-shrink-0" />
                    <span className="text-gray-300">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="relative">
              <div className="p-8 rounded-3xl backdrop-blur-md border" style={{
                background: 'rgba(255, 255, 255, 0.05)',
                borderColor: 'hsl(142, 76%, 46% / 0.2)'
              }}>
                <div className="text-center">
                  <Shield className="w-24 h-24 text-emerald-400 mx-auto mb-6" />
                  <h3 className="text-2xl font-bold text-white mb-4">Bank-Level Security</h3>
                  <p className="text-gray-300 mb-6">Your funds are protected with the same security standards used by major financial institutions.</p>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 rounded-lg bg-emerald-500/10">
                      <Lock className="w-8 h-8 text-emerald-400 mx-auto mb-2" />
                      <p className="text-white font-semibold">Encrypted</p>
                    </div>
                    <div className="text-center p-4 rounded-lg bg-emerald-500/10">
                      <Award className="w-8 h-8 text-emerald-400 mx-auto mb-2" />
                      <p className="text-white font-semibold">Insured</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section id="testimonials" className="py-20 bg-black/30">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">What Our Users Say</h2>
            <p className="text-xl text-gray-300">
              Join thousands of Nigerians who have transformed their financial lives with Better Interest
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonialsLoading ? (
              // Loading skeleton
              Array.from({ length: 3 }).map((_, index) => (
                <div
                  key={index}
                  className="p-8 rounded-2xl backdrop-blur-md border animate-pulse"
                  style={{
                    background: 'rgba(255, 255, 255, 0.05)',
                    borderColor: 'hsl(142, 76%, 46% / 0.2)'
                  }}
                >
                  <div className="flex items-center mb-4">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <div key={i} className="w-5 h-5 bg-gray-600 rounded mr-1" />
                    ))}
                  </div>
                  <div className="space-y-3 mb-6">
                    <div className="h-4 bg-gray-600 rounded w-full" />
                    <div className="h-4 bg-gray-600 rounded w-3/4" />
                    <div className="h-4 bg-gray-600 rounded w-1/2" />
                  </div>
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gray-600 rounded-full mr-4" />
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-600 rounded w-24" />
                      <div className="h-3 bg-gray-600 rounded w-20" />
                    </div>
                  </div>
                </div>
              ))
            ) : testimonials.length > 0 ? (
              testimonials.map((testimonial, index) => (
              <div
                key={index}
                className="p-8 rounded-2xl backdrop-blur-md border transition-all duration-300 hover:scale-105"
                style={{
                  background: 'rgba(255, 255, 255, 0.05)',
                  borderColor: 'hsl(142, 76%, 46% / 0.2)'
                }}
              >
                <div className="flex items-center mb-4">
                  {Array(testimonial.rating).fill(0).map((_, i) => (
                    <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                
                <p className="text-gray-300 mb-6 italic leading-relaxed">
                  "{testimonial.quote}"
                </p>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">{testimonial.avatar}</div>
                    <div>
                      <p className="font-bold text-white">{testimonial.name}</p>
                      <p className="text-sm text-gray-400">{testimonial.role}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-emerald-400 font-bold">{testimonial.amount}</p>
                  </div>
                </div>
              </div>
            ))
            ) : (
              <div className="col-span-full text-center py-12">
                <p className="text-gray-300">No testimonials available at the moment.</p>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20" style={{ background: 'var(--gradient-primary)' }}>
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
            Ready to Transform Your Financial Future?
          </h2>
          <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed">
            Join over 50,000 Nigerians who are building wealth with Better Interest. Start with as little as ₦500 and watch your money grow.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Link to="/signup">
              <Button size="lg" className="bg-white text-emerald-600 hover:bg-gray-100 text-lg px-8 py-6 font-bold">
                Start Saving Today
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Button size="lg" variant="outline" className="border-white/50 text-white hover:bg-white/10 text-lg px-8 py-6">
              Download App
            </Button>
          </div>
          
          <p className="text-white/80 text-sm">
            ✓ No hidden fees  ✓ Start with ₦500  ✓ Withdraw anytime
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-16 bg-black text-white">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="flex items-center gap-3 mb-6">
                <div className="h-12 w-auto">
                  <img
                    src="/lovable-uploads/new-logo.svg"
                    alt="Better Interest Logo"
                    className="h-full w-auto object-contain"
                  />
                </div>
              </div>
              <p className="text-gray-400 mb-6 max-w-md">
                The most trusted digital savings platform in Nigeria. Building wealth through technology and community.
              </p>
              <div className="flex items-center gap-4">
                <span className="text-sm text-gray-400">Regulated by:</span>
                <div className="flex items-center gap-2">
                  <Shield className="w-4 h-4 text-emerald-400" />
                  <span className="text-sm">CBN • SEC • NDIC</span>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-bold mb-4">Company</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Careers</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Press</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Blog</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-bold mb-4">Support</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Contact Us</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Terms of Service</a></li>
              </ul>
            </div>
          </div>
          
          <div className="mt-12 pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400">© {new Date().getFullYear()} Better Interest. All rights reserved.</p>
            <div className="flex items-center gap-6 mt-4 md:mt-0">
              <span className="text-sm text-gray-400">Made with ❤️ in Nigeria</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
