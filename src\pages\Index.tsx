import { <PERSON><PERSON> } from "@/components/ui/button";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { <PERSON>R<PERSON>, Award, BarChart, CheckCircle, Globe, Lock, PiggyBank, Shield, Smartphone, Star, TrendingUp, Users } from "lucide-react";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";

const Index = () => {
  const [testimonials, setTestimonials] = useState<any[]>([]);
  const [testimonialsLoading, setTestimonialsLoading] = useState(true);

  useEffect(() => {
    const fetchTestimonials = async () => {
      setTestimonialsLoading(true);
      try {
        const response = await fetch('/api/v1/testimonials/featured');
        if (response.ok) {
          const data = await response.json();
          setTestimonials(data.data || []);
        }
      } catch (error) {
        console.error('Error fetching testimonials:', error);
        setTestimonials([]);
      } finally {
        setTestimonialsLoading(false);
      }
    };

    fetchTestimonials();
  }, []);
  return (
    <div className="min-h-screen flex flex-col" style={{ background: 'var(--gradient-hero)' }}>
      {/* Navigation */}
      <header className="border-b backdrop-blur-md sticky top-0 z-50" style={{
        background: 'rgba(255, 255, 255, 0.1)',
        borderColor: 'hsl(142, 76%, 46% / 0.2)'
      }}>
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-12 w-auto">
              <img
                src="/lovable-uploads/new-logo.svg"
                alt="Better Interest Logo"
                className="h-full w-auto object-contain"
              />
            </div>
          </div>
          
          <nav className="hidden md:flex gap-8">
            <a href="#features" className="text-sm font-medium text-gray-300 hover:text-emerald-400 transition-colors">Features</a>
            <a href="#how-it-works" className="text-sm font-medium text-gray-300 hover:text-emerald-400 transition-colors">How It Works</a>
            <a href="#security" className="text-sm font-medium text-gray-300 hover:text-emerald-400 transition-colors">Security</a>
            <a href="#testimonials" className="text-sm font-medium text-gray-300 hover:text-emerald-400 transition-colors">Testimonials</a>
          </nav>
          
          <div className="flex items-center gap-3">
            <ThemeToggle />
            <Link to="/login">
              <Button variant="outline" className="border-emerald-400/50 text-emerald-300 hover:bg-emerald-400/10">
                Log In
              </Button>
            </Link>
            <Link to="/signup">
              <Button className="bg-gradient-to-r from-emerald-500 to-emerald-400 hover:from-emerald-600 hover:to-emerald-500 text-white">
                Get Started
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative overflow-hidden min-h-screen flex items-center">
        {/* Geometric Background Pattern */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0" style={{
            backgroundImage: `
              radial-gradient(circle at 25% 25%, hsl(142, 76%, 46% / 0.1) 0%, transparent 50%),
              radial-gradient(circle at 75% 75%, hsl(142, 76%, 46% / 0.1) 0%, transparent 50%),
              linear-gradient(45deg, transparent 40%, hsl(142, 76%, 46% / 0.05) 50%, transparent 60%)
            `,
            backgroundSize: '100px 100px, 150px 150px, 200px 200px'
          }}></div>
          {/* Hexagonal Grid Overlay */}
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2322c55e' fill-opacity='0.05'%3E%3Cpolygon points='30 15 45 30 30 45 15 30'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '60px 60px'
          }}></div>
        </div>

        {/* Glass Morphism Background Elements */}
        <div className="absolute top-20 left-10 w-72 h-72 rounded-full" style={{ background: 'var(--gradient-glow)' }}></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 rounded-full" style={{ background: 'var(--gradient-glow)' }}></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="flex flex-col lg:flex-row items-center gap-16">
            <div className="lg:w-1/2 space-y-8 animate-fade-in">
              {/* Trust Badge */}
              <div className="flex items-center gap-4">
                <div className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-full backdrop-blur-md border" style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  borderColor: 'hsl(142, 76%, 46% / 0.3)',
                  color: 'hsl(142, 76%, 70%)'
                }}>
                  <Shield className="w-4 h-4" />
                  Bank-Level Security
                </div>
                <div className="flex items-center gap-1">
                  {Array(5).fill(0).map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  ))}
                  <span className="text-sm text-gray-300 ml-2">4.9/5 Rating</span>
                </div>
              </div>
              
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight tracking-tight text-white">
                Save Smarter with <span className="bg-gradient-to-r from-emerald-400 to-emerald-300 bg-clip-text text-transparent">Digital KOLO</span>
              </h1>
              
              <p className="text-xl text-gray-300 max-w-lg leading-relaxed">
                Transform your financial future with Nigeria's most trusted digital savings platform. Join over 50,000 Nigerians building wealth through modern ASUSU.
              </p>

              {/* Key Benefits */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {[
                  { icon: <TrendingUp className="w-5 h-5" />, text: "15% Annual Returns" },
                  { icon: <Shield className="w-5 h-5" />, text: "NDIC Insured" },
                  { icon: <Smartphone className="w-5 h-5" />, text: "Mobile First" },
                  { icon: <Globe className="w-5 h-5" />, text: "Available 24/7" }
                ].map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3 text-emerald-300">
                    {benefit.icon}
                    <span className="font-medium">{benefit.text}</span>
                  </div>
                ))}
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4 pt-6">
                <Link to="/signup">
                  <Button size="lg" className="bg-gradient-to-r from-emerald-500 to-emerald-400 hover:from-emerald-600 hover:to-emerald-500 text-white border-0 shadow-lg hover:shadow-emerald-500/25 transition-all duration-300 text-lg px-8 py-6">
                    Start Saving Today
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Button size="lg" variant="outline" className="border-emerald-400/50 text-emerald-300 hover:bg-emerald-400/10 backdrop-blur-sm text-lg px-8 py-6">
                  Watch Demo
                </Button>
              </div>

              {/* Social Proof */}
              <div className="flex items-center gap-6 pt-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">50K+</div>
                  <div className="text-sm text-gray-400">Active Savers</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">₦2.5B+</div>
                  <div className="text-sm text-gray-400">Total Saved</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">99.9%</div>
                  <div className="text-sm text-gray-400">Uptime</div>
                </div>
              </div>
            </div>
            
            <div className="lg:w-1/2 animate-scale-in relative">
              {/* Main Glass Card */}
              <div className="relative p-8 rounded-3xl backdrop-blur-md border" style={{
                background: 'rgba(255, 255, 255, 0.1)',
                borderColor: 'hsl(142, 76%, 46% / 0.3)',
                boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1) inset'
              }}>
                <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500/20 to-emerald-400/20 rounded-3xl blur-sm"></div>
                
                {/* Mobile App Preview */}
                <div className="relative rounded-2xl overflow-hidden" style={{
                  background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05))'
                }}>
                  <div className="p-8">
                    {/* Phone Mockup */}
                    <div className="mx-auto w-64 h-96 rounded-3xl border-4 border-gray-600 overflow-hidden" style={{
                      background: 'linear-gradient(145deg, #1a1a1a, #2d2d2d)'
                    }}>
                      {/* Phone Screen */}
                      <div className="w-full h-full bg-gradient-to-b from-gray-900 to-black p-4 flex flex-col">
                        {/* Status Bar */}
                        <div className="flex justify-between items-center text-white text-xs mb-4">
                          <span>9:41</span>
                          <span>●●●●○</span>
                        </div>
                        
                        {/* App Header */}
                        <div className="text-center mb-6">
                          <h3 className="text-white font-bold text-lg">ASUSU</h3>
                          <p className="text-gray-400 text-sm">Your Savings Dashboard</p>
                        </div>
                        
                        {/* Balance Card */}
                        <div className="bg-gradient-to-r from-emerald-500 to-emerald-400 rounded-xl p-4 mb-4">
                          <p className="text-white/80 text-sm">Total Savings</p>
                          <p className="text-white text-2xl font-bold">₦450,000</p>
                          <p className="text-white/80 text-xs">+₦15,000 this month</p>
                        </div>
                        
                        {/* Quick Actions */}
                        <div className="grid grid-cols-2 gap-2 mb-4">
                          <div className="bg-gray-800 rounded-lg p-3 text-center">
                            <PiggyBank className="w-6 h-6 text-emerald-400 mx-auto mb-1" />
                            <p className="text-white text-xs">Save</p>
                          </div>
                          <div className="bg-gray-800 rounded-lg p-3 text-center">
                            <TrendingUp className="w-6 h-6 text-emerald-400 mx-auto mb-1" />
                            <p className="text-white text-xs">Invest</p>
                          </div>
                        </div>
                        
                        {/* Recent Activity */}
                        <div className="flex-1">
                          <p className="text-gray-400 text-xs mb-2">Recent Activity</p>
                          <div className="space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-white text-xs">Daily Save</span>
                              <span className="text-emerald-400 text-xs">+₦1,000</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-white text-xs">Interest</span>
                              <span className="text-emerald-400 text-xs">+₦125</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Floating Elements */}
                <div className="absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-r from-emerald-400 to-emerald-300 rounded-full flex items-center justify-center animate-bounce-gentle shadow-lg">
                  <span className="text-lg font-bold text-gray-900">₦</span>
                </div>
                <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-300 rounded-full flex items-center justify-center animate-float">
                  <Star className="w-6 h-6 text-gray-900" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 relative" style={{ background: 'linear-gradient(180deg, var(--gradient-hero), rgba(0,0,0,0.9))' }}>
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
              Why Choose <span className="bg-gradient-to-r from-emerald-400 to-emerald-300 bg-clip-text text-transparent">ASUSU by Koja</span>
            </h2>
            <p className="text-xl text-gray-300 leading-relaxed">
              Experience the perfect blend of traditional Nigerian savings culture and cutting-edge technology
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: <Shield className="h-12 w-12 text-emerald-400" />,
                title: "Bank-Level Security",
                description: "Your money is protected with military-grade encryption and NDIC insurance coverage up to ₦500,000",
                highlight: "NDIC Insured"
              },
              {
                icon: <TrendingUp className="h-12 w-12 text-emerald-400" />,
                title: "High Returns",
                description: "Earn up to 15% annual returns on your savings with our competitive interest rates and investment options",
                highlight: "Up to 15% Returns"
              },
              {
                icon: <Smartphone className="h-12 w-12 text-emerald-400" />,
                title: "Mobile First",
                description: "Save, invest, and manage your money anytime, anywhere with our intuitive mobile app",
                highlight: "24/7 Access"
              },
              {
                icon: <Users className="h-12 w-12 text-emerald-400" />,
                title: "Group Savings",
                description: "Join savings groups or create your own to achieve financial goals together with friends and family",
                highlight: "Social Savings"
              },
              {
                icon: <Lock className="h-12 w-12 text-emerald-400" />,
                title: "Auto-Save",
                description: "Set up automatic daily, weekly, or monthly saves to build wealth consistently without thinking about it",
                highlight: "Hands-Free"
              },
              {
                icon: <Award className="h-12 w-12 text-emerald-400" />,
                title: "Rewards Program",
                description: "Earn points for consistent saving habits and redeem them for exciting rewards and bonuses",
                highlight: "Earn Rewards"
              }
            ].map((feature, index) => (
              <div
                key={index}
                className="relative p-8 rounded-2xl backdrop-blur-md border transition-all duration-300 hover:scale-105 group"
                style={{
                  background: 'rgba(255, 255, 255, 0.05)',
                  borderColor: 'hsl(142, 76%, 46% / 0.2)'
                }}
              >
                <div className="absolute top-4 right-4">
                  <span className="px-2 py-1 text-xs font-medium rounded-full bg-emerald-500/20 text-emerald-300">
                    {feature.highlight}
                  </span>
                </div>
                
                <div className="mb-6 w-fit p-3 rounded-full bg-emerald-500/10">
                  {feature.icon}
                </div>
                
                <h3 className="text-2xl font-bold mb-4 text-white">{feature.title}</h3>
                <p className="text-gray-300 leading-relaxed">{feature.description}</p>
                
                {/* Hover effect */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-emerald-500/5 to-emerald-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section id="how-it-works" className="py-20 bg-black/50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">How It Works</h2>
            <p className="text-xl text-gray-300">Start your savings journey in just 3 simple steps</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 relative">
            {/* Connection Lines */}
            <div className="hidden md:block absolute top-24 left-1/3 right-1/3 h-0.5 bg-gradient-to-r from-emerald-500 to-emerald-400"></div>
            
            {[
              {
                step: "01",
                title: "Sign Up & Verify",
                description: "Create your account in under 2 minutes and complete our simple KYC verification process",
                icon: <Users className="h-10 w-10 text-emerald-400" />
              },
              {
                step: "02",
                title: "Set Your Goals",
                description: "Define your savings targets and choose from our flexible savings plans that suit your lifestyle",
                icon: <BarChart className="h-10 w-10 text-emerald-400" />
              },
              {
                step: "03",
                title: "Start Saving",
                description: "Make automatic or manual contributions and watch your money grow with competitive interest rates",
                icon: <PiggyBank className="h-10 w-10 text-emerald-400" />
              }
            ].map((step, index) => (
              <div key={index} className="relative text-center">
                {/* Step Number */}
                <div className="mx-auto w-16 h-16 rounded-full bg-gradient-to-r from-emerald-500 to-emerald-400 flex items-center justify-center text-white font-bold text-xl mb-6 relative z-10">
                  {step.step}
                </div>
                
                {/* Content Card */}
                <div className="p-6 rounded-2xl backdrop-blur-md border" style={{
                  background: 'rgba(255, 255, 255, 0.05)',
                  borderColor: 'hsl(142, 76%, 46% / 0.2)'
                }}>
                  <div className="mb-4 w-fit mx-auto p-3 rounded-full bg-emerald-500/10">
                    {step.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-white">{step.title}</h3>
                  <p className="text-gray-300">{step.description}</p>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link to="/signup">
              <Button size="lg" className="bg-gradient-to-r from-emerald-500 to-emerald-400 hover:from-emerald-600 hover:to-emerald-500 text-white text-lg px-8 py-6">
                Get Started Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Security Section */}
      <section id="security" className="py-20" style={{ background: 'linear-gradient(180deg, rgba(0,0,0,0.9), var(--gradient-hero))' }}>
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
                Your Money is <span className="bg-gradient-to-r from-emerald-400 to-emerald-300 bg-clip-text text-transparent">100% Safe</span>
              </h2>
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                We use the same security standards as major banks to protect your money and personal information.
              </p>
              
              <div className="space-y-4">
                {[
                  "256-bit SSL encryption for all transactions",
                  "NDIC insurance coverage up to ₦500,000",
                  "Two-factor authentication (2FA)",
                  "Biometric login (fingerprint & face ID)",
                  "Real-time fraud monitoring",
                  "Regulated by CBN and SEC"
                ].map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-emerald-400 flex-shrink-0" />
                    <span className="text-gray-300">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="relative">
              <div className="p-8 rounded-3xl backdrop-blur-md border" style={{
                background: 'rgba(255, 255, 255, 0.05)',
                borderColor: 'hsl(142, 76%, 46% / 0.2)'
              }}>
                <div className="text-center">
                  <Shield className="w-24 h-24 text-emerald-400 mx-auto mb-6" />
                  <h3 className="text-2xl font-bold text-white mb-4">Bank-Level Security</h3>
                  <p className="text-gray-300 mb-6">Your funds are protected with the same security standards used by major financial institutions.</p>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 rounded-lg bg-emerald-500/10">
                      <Lock className="w-8 h-8 text-emerald-400 mx-auto mb-2" />
                      <p className="text-white font-semibold">Encrypted</p>
                    </div>
                    <div className="text-center p-4 rounded-lg bg-emerald-500/10">
                      <Award className="w-8 h-8 text-emerald-400 mx-auto mb-2" />
                      <p className="text-white font-semibold">Insured</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section id="testimonials" className="py-20 bg-black/30">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">What Our Users Say</h2>
            <p className="text-xl text-gray-300">
              Join thousands of Nigerians who have transformed their financial lives with ASUSU
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonialsLoading ? (
              // Loading skeleton
              Array.from({ length: 3 }).map((_, index) => (
                <div
                  key={index}
                  className="p-8 rounded-2xl backdrop-blur-md border animate-pulse"
                  style={{
                    background: 'rgba(255, 255, 255, 0.05)',
                    borderColor: 'hsl(142, 76%, 46% / 0.2)'
                  }}
                >
                  <div className="flex items-center mb-4">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <div key={i} className="w-5 h-5 bg-gray-600 rounded mr-1" />
                    ))}
                  </div>
                  <div className="space-y-3 mb-6">
                    <div className="h-4 bg-gray-600 rounded w-full" />
                    <div className="h-4 bg-gray-600 rounded w-3/4" />
                    <div className="h-4 bg-gray-600 rounded w-1/2" />
                  </div>
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gray-600 rounded-full mr-4" />
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-600 rounded w-24" />
                      <div className="h-3 bg-gray-600 rounded w-20" />
                    </div>
                  </div>
                </div>
              ))
            ) : testimonials.length > 0 ? (
              testimonials.map((testimonial, index) => (
              <div
                key={index}
                className="p-8 rounded-2xl backdrop-blur-md border transition-all duration-300 hover:scale-105"
                style={{
                  background: 'rgba(255, 255, 255, 0.05)',
                  borderColor: 'hsl(142, 76%, 46% / 0.2)'
                }}
              >
                <div className="flex items-center mb-4">
                  {Array(testimonial.rating).fill(0).map((_, i) => (
                    <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                
                <p className="text-gray-300 mb-6 italic leading-relaxed">
                  "{testimonial.quote}"
                </p>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">{testimonial.avatar}</div>
                    <div>
                      <p className="font-bold text-white">{testimonial.name}</p>
                      <p className="text-sm text-gray-400">{testimonial.role}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-emerald-400 font-bold">{testimonial.amount}</p>
                  </div>
                </div>
              </div>
            ))
            ) : (
              <div className="col-span-full text-center py-12">
                <p className="text-gray-300">No testimonials available at the moment.</p>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20" style={{ background: 'var(--gradient-primary)' }}>
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
            Ready to Transform Your Financial Future?
          </h2>
          <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed">
            Join over 50,000 Nigerians who are building wealth with ASUSU. Start with as little as ₦500 and watch your money grow.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Link to="/signup">
              <Button size="lg" className="bg-white text-emerald-600 hover:bg-gray-100 text-lg px-8 py-6 font-bold">
                Start Saving Today
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Button size="lg" variant="outline" className="border-white/50 text-white hover:bg-white/10 text-lg px-8 py-6">
              Download App
            </Button>
          </div>
          
          <p className="text-white/80 text-sm">
            ✓ No hidden fees  ✓ Start with ₦500  ✓ Withdraw anytime
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-16 bg-black text-white">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="flex items-center gap-3 mb-6">
                <div className="h-12 w-auto">
                  <img
                    src="/lovable-uploads/new-logo.svg"
                    alt="Better Interest Logo"
                    className="h-full w-auto object-contain"
                  />
                </div>
              </div>
              <p className="text-gray-400 mb-6 max-w-md">
                The most trusted digital savings platform in Nigeria. Building wealth through technology and community.
              </p>
              <div className="flex items-center gap-4">
                <span className="text-sm text-gray-400">Regulated by:</span>
                <div className="flex items-center gap-2">
                  <Shield className="w-4 h-4 text-emerald-400" />
                  <span className="text-sm">CBN • SEC • NDIC</span>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-bold mb-4">Company</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Careers</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Press</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Blog</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-bold mb-4">Support</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Contact Us</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Terms of Service</a></li>
              </ul>
            </div>
          </div>
          
          <div className="mt-12 pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400">© {new Date().getFullYear()} ASUSU by Koja. All rights reserved.</p>
            <div className="flex items-center gap-6 mt-4 md:mt-0">
              <span className="text-sm text-gray-400">Made with ❤️ in Nigeria</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
