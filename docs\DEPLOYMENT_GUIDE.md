# Deployment Guide for Better Interest

## Overview
This guide provides step-by-step instructions for deploying the Better Interest application to production.

## Prerequisites

### System Requirements
- Node.js 16+ 
- MongoDB 4.4+
- Redis 6+
- <PERSON>inx (for reverse proxy)
- SSL Certificate
- Domain name

### External Services
- Paystack account (live keys)
- SendGrid account (email service)
- Termii account (SMS service)
- Cloud storage (AWS S3 or similar)

## Frontend Deployment (Netlify)

### 1. Build Configuration
```bash
# Install dependencies
npm install

# Build for production
npm run build

# Test build locally
npm run preview
```

### 2. Netlify Configuration
Create `netlify.toml` in project root:
```toml
[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "16"

[[redirects]]
  from = "/api/*"
  to = "https://api.betterinterest.com/api/:splat"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[headers]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://api.betterinterest.com https://api.paystack.co;"

[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
```

### 3. Environment Variables
Set in Netlify dashboard:
```bash
VITE_API_URL=https://api.betterinterest.com/api/v1
VITE_PAYSTACK_PUBLIC_KEY=pk_live_your_live_key
VITE_DEMO_MODE=false
VITE_ENABLE_ANALYTICS=true
```

### 4. Deploy to Netlify
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login to Netlify
netlify login

# Deploy
netlify deploy --prod --dir=dist
```

## Backend Deployment (VPS/Cloud)

### 1. Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 (Process Manager)
sudo npm install -g pm2

# Install Nginx
sudo apt install nginx -y

# Install MongoDB
wget -qO - https://www.mongodb.org/static/pgp/server-4.4.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/4.4 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-4.4.list
sudo apt-get update
sudo apt-get install -y mongodb-org

# Install Redis
sudo apt install redis-server -y
```

### 2. Application Deployment
```bash
# Clone repository
git clone https://github.com/yourusername/betterinterest-backend.git
cd betterinterest-backend

# Install dependencies
npm install --production

# Copy environment file
cp .env.production .env

# Update environment variables
nano .env
```

### 3. Database Setup
```bash
# Start MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# Create database user
mongo
> use betterinterest
> db.createUser({
    user: "betterinterest_user",
    pwd: "secure_password",
    roles: [{ role: "readWrite", db: "betterinterest" }]
  })
> exit

# Update MongoDB configuration
sudo nano /etc/mongod.conf
# Enable authentication:
# security:
#   authorization: enabled

sudo systemctl restart mongod
```

### 4. Redis Configuration
```bash
# Configure Redis
sudo nano /etc/redis/redis.conf

# Set password
requirepass your_secure_redis_password

# Restart Redis
sudo systemctl restart redis-server
sudo systemctl enable redis-server
```

### 5. PM2 Configuration
Create `ecosystem.config.js`:
```javascript
module.exports = {
  apps: [{
    name: 'betterinterest-api',
    script: 'src/index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max_old_space_size=1024'
  }]
};
```

Start application:
```bash
# Start with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u $USER --hp $HOME
```

### 6. Nginx Configuration
Create `/etc/nginx/sites-available/betterinterest`:
```nginx
server {
    listen 80;
    server_name api.betterinterest.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.betterinterest.com;

    ssl_certificate /etc/letsencrypt/live/api.betterinterest.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.betterinterest.com/privkey.pem;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;
}
```

Enable site:
```bash
sudo ln -s /etc/nginx/sites-available/betterinterest /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 7. SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Get SSL certificate
sudo certbot --nginx -d api.betterinterest.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Database Migration

### 1. Create Migration Scripts
```javascript
// migrations/001_initial_setup.js
const mongoose = require('mongoose');

async function up() {
  // Create indexes
  await mongoose.connection.collection('users').createIndex({ email: 1 }, { unique: true });
  await mongoose.connection.collection('users').createIndex({ phone: 1 }, { unique: true });
  await mongoose.connection.collection('transactions').createIndex({ userId: 1, createdAt: -1 });
  
  // Create default savings plans
  await mongoose.connection.collection('savingsplans').insertMany([
    {
      name: 'Daily Saver',
      description: 'Save daily and earn interest',
      minimumAmount: 1000,
      interestRate: 5.5,
      durationDays: 30,
      isActive: true,
      category: 'daily'
    }
  ]);
}

async function down() {
  // Rollback operations
  await mongoose.connection.collection('savingsplans').deleteMany({});
}

module.exports = { up, down };
```

### 2. Run Migrations
```bash
# Create migration runner
node scripts/migrate.js up
```

## Monitoring and Logging

### 1. Application Monitoring
```bash
# Install monitoring tools
npm install --save express-prometheus-middleware prom-client

# Setup Grafana and Prometheus
docker run -d -p 3000:3000 grafana/grafana
docker run -d -p 9090:9090 prom/prometheus
```

### 2. Log Management
```bash
# Setup log rotation
sudo nano /etc/logrotate.d/betterinterest

/home/<USER>/betterinterest-backend/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 ubuntu ubuntu
    postrotate
        pm2 reloadLogs
    endscript
}
```

### 3. Health Checks
```javascript
// src/routes/health.js
const express = require('express');
const mongoose = require('mongoose');
const redis = require('../config/redis');

const router = express.Router();

router.get('/health', async (req, res) => {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    services: {}
  };

  try {
    // Check database
    await mongoose.connection.db.admin().ping();
    health.services.database = 'ok';
  } catch (error) {
    health.services.database = 'error';
    health.status = 'error';
  }

  try {
    // Check Redis
    await redis.ping();
    health.services.redis = 'ok';
  } catch (error) {
    health.services.redis = 'error';
    health.status = 'error';
  }

  const statusCode = health.status === 'ok' ? 200 : 503;
  res.status(statusCode).json(health);
});

module.exports = router;
```

## Backup Strategy

### 1. Database Backup
```bash
# Create backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/mongodb"
mkdir -p $BACKUP_DIR

mongodump --host localhost --port 27017 --db betterinterest --out $BACKUP_DIR/backup_$DATE

# Compress backup
tar -czf $BACKUP_DIR/backup_$DATE.tar.gz $BACKUP_DIR/backup_$DATE
rm -rf $BACKUP_DIR/backup_$DATE

# Upload to S3 (optional)
aws s3 cp $BACKUP_DIR/backup_$DATE.tar.gz s3://your-backup-bucket/mongodb/

# Keep only last 30 days
find $BACKUP_DIR -name "backup_*.tar.gz" -mtime +30 -delete
```

### 2. Automated Backups
```bash
# Add to crontab
0 2 * * * /home/<USER>/scripts/backup_mongodb.sh
```

## Security Checklist

### Production Security
- [ ] SSL/TLS certificates installed
- [ ] Database authentication enabled
- [ ] Redis password protection
- [ ] Firewall configured (only necessary ports open)
- [ ] Regular security updates
- [ ] Strong passwords for all services
- [ ] API rate limiting enabled
- [ ] Input validation and sanitization
- [ ] CORS properly configured
- [ ] Security headers implemented
- [ ] File upload restrictions
- [ ] Error messages don't expose sensitive info
- [ ] Logging configured (but not logging sensitive data)
- [ ] Backup encryption
- [ ] Environment variables secured

## Post-Deployment Verification

### 1. Smoke Tests
```bash
# Test API endpoints
curl -X GET https://api.betterinterest.com/api/v1/health
curl -X POST https://api.betterinterest.com/api/v1/auth/register -d '{"firstName":"Test","lastName":"User","email":"<EMAIL>","password":"password123","phone":"+2348012345678"}' -H "Content-Type: application/json"
```

### 2. Performance Tests
```bash
# Load testing
artillery run production-load-test.yml
```

### 3. Monitoring Setup
- Setup alerts for high CPU/memory usage
- Monitor API response times
- Track error rates
- Monitor database performance
- Setup uptime monitoring

## Rollback Plan

### 1. Application Rollback
```bash
# Rollback to previous version
pm2 stop betterinterest-api
git checkout previous-stable-tag
npm install --production
pm2 start ecosystem.config.js
```

### 2. Database Rollback
```bash
# Restore from backup
mongorestore --host localhost --port 27017 --db betterinterest /backups/mongodb/backup_YYYYMMDD_HHMMSS/betterinterest
```

## Maintenance

### Regular Tasks
- Monitor system resources
- Check application logs
- Update dependencies
- Security patches
- Database optimization
- Backup verification
- Performance monitoring
- SSL certificate renewal
