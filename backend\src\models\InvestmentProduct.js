const mongoose = require('mongoose');

const investmentProductSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    enum: ['mutual_fund', 'stock', 'bond'],
    required: true
  },
  description: {
    type: String,
    required: true
  },
  expectedReturns: {
    min: { type: Number, required: true },
    max: { type: Number, required: true }
  },
  riskLevel: {
    type: String,
    enum: ['low', 'medium', 'high'],
    required: true
  },
  minimumAmount: {
    type: Number,
    required: true,
    min: 0
  },
  duration: {
    type: String,
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  performance: [{
    date: Date,
    value: Number,
    percentage: Number
  }],
  ytdReturn: {
    type: Number,
    default: 0
  },
  fees: {
    managementFee: { type: Number, default: 0 },
    entryFee: { type: Number, default: 0 },
    exitFee: { type: Number, default: 0 }
  },
  metadata: {
    totalInvestments: { type: Number, default: 0 },
    totalInvestors: { type: Number, default: 0 },
    lastUpdated: { type: Date, default: Date.now }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
investmentProductSchema.index({ type: 1, isActive: 1 });
investmentProductSchema.index({ riskLevel: 1, minimumAmount: 1 });
investmentProductSchema.index({ ytdReturn: -1 });

// Update metadata on save
investmentProductSchema.pre('save', function(next) {
  this.metadata.lastUpdated = new Date();
  next();
});

module.exports = mongoose.model('InvestmentProduct', investmentProductSchema);