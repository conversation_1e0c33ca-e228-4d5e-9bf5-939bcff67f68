import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Users, 
  Heart,
  Target,
  Building2,
  Globe,
  CreditCard,
  TrendingUp,
  Shield,
  ChevronLeft,
  ChevronRight,
  Clock,
  MessageCircle,
  Handshake,
  Rocket,
  Settings,
  Github,
  Slack,
  Layout,
  Monitor,
  Palette,
  Bug,
  Megaphone,
  CheckCircle,
  ArrowRight,
  Star,
  MapPin
} from "lucide-react";
import { useNavigate } from "react-router-dom";

const KojaPresentation = () => {
  const navigate = useNavigate();
  const [currentSlide, setCurrentSlide] = useState(0);

  const slides = [
    {
      title: "Welcome to Koja",
      subtitle: "Onboarding & Introduction Meeting",
      type: "hero",
      duration: "30 Minutes",
      content: {
        headline: "Building the Future of African Digital Banking",
        description: "Welcome to the team! Today we'll introduce you to <PERSON><PERSON>, our mission, and how we work together to bring Revolut-inspired digital banking to Africa.",
        objective: "To establish a foundation for collaboration and provide a high-level overview of how we work, what we're building, and how this role contributes to our success.",
        agenda: [
          { title: "Welcome & Introductions", time: "5 min", icon: <Handshake className="h-5 w-5" /> },
          { title: "About Koja: Who We Are & What We Do", time: "10 min", icon: <Building2 className="h-5 w-5" /> },
          { title: "Team Structure & How We Work", time: "10 min", icon: <Users className="h-5 w-5" /> },
          { title: "Q&A / Open Discussion", time: "5 min", icon: <MessageCircle className="h-5 w-5" /> }
        ]
      }
    },
    {
      title: "Welcome & Introductions",
      subtitle: "Getting to know each other",
      type: "intro",
      duration: "5 Minutes",
      content: {
        icon: <Handshake className="h-12 w-12 text-primary" />,
        sections: [
          {
            title: "Team Introductions",
            description: "Quick introductions from the team",
            items: [
              "Name, role, and how they contribute to Koja",
              "What excites them about our mission",
              "Fun fact or hobby to break the ice"
            ]
          },
          {
            title: "Leadership Welcome",
            description: "Welcome message from leadership",
            items: [
              "Vision for the team and company",
              "Excitement about new team member",
              "Initial thoughts on collaboration"
            ]
          },
          {
            title: "New Team Member Introduction",
            description: "Your turn to introduce yourself",
            items: [
              "Brief background and previous experience",
              "What excites you about joining Koja",
              "Initial thoughts and questions"
            ]
          }
        ]
      }
    },
    {
      title: "About Koja: Who We Are",
      subtitle: "Our Mission & Vision",
      type: "mission",
      duration: "10 Minutes",
      content: {
        icon: <Target className="h-12 w-12 text-primary" />,
        mission: "Bringing secure, user-friendly, Revolut-inspired digital banking to Africa",
        vision: "To become Africa's leading digital banking platform, empowering millions with accessible financial services",
        values: [
          { 
            title: "Customer-Centric Innovation", 
            description: "Every feature we build starts with user needs",
            icon: <Heart className="h-6 w-6" />
          },
          { 
            title: "Simplicity", 
            description: "Complex financial services made beautifully simple",
            icon: <Layout className="h-6 w-6" />
          },
          { 
            title: "Trust", 
            description: "Security and transparency in everything we do",
            icon: <Shield className="h-6 w-6" />
          },
          { 
            title: "Accessibility", 
            description: "Financial inclusion for all Africans",
            icon: <Globe className="h-6 w-6" />
          }
        ]
      }
    },
    {
      title: "What We're Building",
      subtitle: "Current Products & Future Roadmap",
      type: "products",
      duration: "10 Minutes (continued)",
      content: {
        icon: <Rocket className="h-12 w-12 text-primary" />,
        currentProducts: [
          { 
            name: "Vaults", 
            description: "Smart savings with competitive interest rates",
            icon: <Shield className="h-5 w-5" />,
            status: "Live"
          },
          { 
            name: "Cards", 
            description: "Virtual and physical debit cards for seamless payments",
            icon: <CreditCard className="h-5 w-5" />,
            status: "Live"
          },
          { 
            name: "Dashboard", 
            description: "Comprehensive financial overview and analytics",
            icon: <Monitor className="h-5 w-5" />,
            status: "Live"
          },
          { 
            name: "Trading", 
            description: "Investment platform for stocks and crypto",
            icon: <TrendingUp className="h-5 w-5" />,
            status: "Beta"
          }
        ],
        roadmap: [
          { 
            title: "Short-term Wins",
            items: [
              "Enhanced mobile experience",
              "Advanced budgeting tools",
              "Peer-to-peer payments",
              "Bill payment integrations"
            ]
          },
          { 
            title: "Long-term Ambitions",
            items: [
              "Pan-African expansion",
              "SME banking solutions",
              "Cryptocurrency integration",
              "AI-powered financial advice"
            ]
          }
        ]
      }
    },
    {
      title: "Team Structure",
      subtitle: "How we're organized",
      type: "team",
      duration: "10 Minutes",
      content: {
        icon: <Users className="h-12 w-12 text-primary" />,
        teams: [
          {
            name: "Development",
            description: "Frontend, Backend, and Mobile engineers",
            icon: <Monitor className="h-6 w-6" />,
            responsibilities: ["Feature development", "System architecture", "Performance optimization"]
          },
          {
            name: "Design",
            description: "UI/UX designers and researchers",
            icon: <Palette className="h-6 w-6" />,
            responsibilities: ["User experience", "Visual design", "Design systems"]
          },
          {
            name: "QA",
            description: "Quality assurance and testing",
            icon: <Bug className="h-6 w-6" />,
            responsibilities: ["Testing automation", "Bug tracking", "Quality standards"]
          },
          {
            name: "Marketing",
            description: "Growth and customer acquisition",
            icon: <Megaphone className="h-6 w-6" />,
            responsibilities: ["User acquisition", "Brand awareness", "Content strategy"]
          }
        ]
      }
    },
    {
      title: "How We Work",
      subtitle: "Collaboration & Communication",
      type: "workflow",
      duration: "10 Minutes (continued)",
      content: {
        icon: <Settings className="h-12 w-12 text-primary" />,
        workflow: {
          methodology: "Agile with 2-week sprints",
          meetings: [
            { name: "Daily Stand-ups", frequency: "Every morning at 9:00 AM", purpose: "Progress updates and blockers" },
            { name: "Sprint Planning", frequency: "Every 2 weeks", purpose: "Plan upcoming sprint work" },
            { name: "Sprint Review", frequency: "End of each sprint", purpose: "Demo completed features" },
            { name: "Retrospectives", frequency: "End of each sprint", purpose: "Continuous improvement" }
          ]
        },
        tools: [
          { name: "Slack", purpose: "Team communication and quick updates", icon: <Slack className="h-5 w-5" /> },
          { name: "Notion", purpose: "Documentation and project management", icon: <Layout className="h-5 w-5" /> },
          { name: "GitHub", purpose: "Code repository and version control", icon: <Github className="h-5 w-5" /> },
          { name: "Figma", purpose: "Design collaboration and prototyping", icon: <Palette className="h-5 w-5" /> }
        ],
        expectations: [
          "Proactive communication about progress and challenges",
          "Collaborative feedback between Product and Development",
          "Async updates for remote team members",
          "Regular code reviews and knowledge sharing"
        ]
      }
    },
    {
      title: "Current Challenges & Opportunities",
      subtitle: "Where you can make immediate impact",
      type: "opportunities",
      duration: "10 Minutes (continued)",
      content: {
        icon: <Star className="h-12 w-12 text-primary" />,
        challenges: [
          {
            title: "User Experience Optimization",
            description: "Improving conversion rates and user engagement",
            impact: "High",
            urgency: "Medium"
          },
          {
            title: "Mobile Performance",
            description: "Enhancing app performance for low-end devices",
            impact: "High",
            urgency: "High"
          },
          {
            title: "Feature Scalability",
            description: "Building features that can scale across African markets",
            impact: "Medium",
            urgency: "Medium"
          },
          {
            title: "Regulatory Compliance",
            description: "Ensuring compliance across different African jurisdictions",
            impact: "High",
            urgency: "Low"
          }
        ],
        opportunities: [
          "Lead development of new financial products",
          "Architect solutions for multi-country expansion",
          "Implement best practices for team efficiency",
          "Mentor junior team members"
        ]
      }
    },
    {
      title: "Q&A & Next Steps",
      subtitle: "Open discussion and planning ahead",
      type: "qa",
      duration: "5 Minutes",
      content: {
        icon: <MessageCircle className="h-12 w-12 text-primary" />,
        sections: [
          {
            title: "Open Floor",
            description: "Your questions and thoughts",
            items: [
              "Any questions about Koja or the role?",
              "Clarifications about our processes?",
              "Technical questions about our stack?",
              "Cultural or team-related questions?"
            ]
          },
          {
            title: "Next Steps",
            description: "Planning your first weeks",
            items: [
              "Setup development environment",
              "Access to tools and repositories",
              "First project assignment",
              "Pair programming sessions with team leads"
            ]
          },
          {
            title: "Immediate Focus",
            description: "What to expect in your first 30 days",
            items: [
              "Codebase familiarization",
              "Team integration activities",
              "First feature contribution",
              "Regular check-ins with manager"
            ]
          }
        ]
      }
    }
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const currentSlideData = slides[currentSlide];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 border-b bg-background/95 backdrop-blur">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-primary to-primary/70 flex items-center justify-center">
                <Building2 className="h-6 w-6 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-xl font-bold">Koja</h1>
                <p className="text-sm text-muted-foreground">Digital Banking for Africa</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Badge variant="secondary" className="gap-1">
                <Clock className="h-3 w-3" />
                {currentSlideData.duration}
              </Badge>
              <span className="text-sm text-muted-foreground">
                {currentSlide + 1} / {slides.length}
              </span>
              <Button variant="outline" onClick={() => navigate('/dashboard')}>
                Dashboard
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Slide Container */}
      <div className="flex-1 flex flex-col">
        {/* Slide Content */}
        <main className="flex-1 py-12 px-4 min-h-[70vh]">
          <div className="container mx-auto max-w-6xl">
            {currentSlideData.type === "hero" && (
              <div className="text-center space-y-12">
                <div className="space-y-6">
                  <Badge variant="secondary" className="mb-4 gap-2">
                    <MapPin className="h-3 w-3" />
                    🌍 Built for Africa, by Africans
                  </Badge>
                  <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                    {currentSlideData.content.headline}
                  </h1>
                  <p className="text-xl text-muted-foreground max-w-4xl mx-auto">
                    {currentSlideData.content.description}
                  </p>
                  <Card className="max-w-3xl mx-auto">
                    <CardHeader>
                      <CardTitle className="text-left">Meeting Objective</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground text-left">{currentSlideData.content.objective}</p>
                    </CardContent>
                  </Card>
                </div>
                
                {/* Agenda Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 max-w-5xl mx-auto">
                  {currentSlideData.content.agenda.map((item, index) => (
                    <Card key={index} className="text-center hover:shadow-md transition-shadow cursor-pointer" onClick={() => goToSlide(index + 1)}>
                      <CardContent className="pt-6">
                        <div className="flex justify-center mb-3 text-primary">
                          {item.icon}
                        </div>
                        <div className="text-sm font-semibold mb-1">{item.title}</div>
                        <Badge variant="outline">{item.time}</Badge>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {currentSlideData.type === "intro" && (
              <div className="space-y-12">
                <div className="text-center space-y-4">
                  <div className="flex justify-center mb-6">
                    {currentSlideData.content.icon}
                  </div>
                  <h1 className="text-4xl md:text-5xl font-bold">{currentSlideData.title}</h1>
                  <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                    {currentSlideData.subtitle}
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
                  {currentSlideData.content.sections.map((section, index) => (
                    <Card key={index} className="h-full">
                      <CardHeader>
                        <CardTitle className="text-lg">{section.title}</CardTitle>
                        <CardDescription>{section.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2">
                          {section.items.map((item, itemIndex) => (
                            <li key={itemIndex} className="flex items-start gap-2">
                              <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                              <span className="text-sm">{item}</span>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {currentSlideData.type === "mission" && (
              <div className="space-y-12">
                <div className="text-center space-y-4">
                  <div className="flex justify-center mb-6">
                    {currentSlideData.content.icon}
                  </div>
                  <h1 className="text-4xl md:text-5xl font-bold">{currentSlideData.title}</h1>
                  <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                    {currentSlideData.subtitle}
                  </p>
                </div>

                <div className="space-y-8">
                  <div className="text-center space-y-4">
                    <Card className="max-w-4xl mx-auto">
                      <CardHeader>
                        <CardTitle className="text-2xl">Our Mission</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-lg">{currentSlideData.content.mission}</p>
                      </CardContent>
                    </Card>
                    
                    <Card className="max-w-4xl mx-auto">
                      <CardHeader>
                        <CardTitle className="text-2xl">Our Vision</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-lg">{currentSlideData.content.vision}</p>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="space-y-6">
                    <h2 className="text-2xl font-bold text-center">What Drives Us</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-5xl mx-auto">
                      {currentSlideData.content.values.map((value, index) => (
                        <Card key={index} className="h-full">
                          <CardHeader>
                            <CardTitle className="flex items-center gap-3">
                              <div className="text-primary">{value.icon}</div>
                              {value.title}
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <p className="text-muted-foreground">{value.description}</p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {currentSlideData.type === "products" && (
              <div className="space-y-12">
                <div className="text-center space-y-4">
                  <div className="flex justify-center mb-6">
                    {currentSlideData.content.icon}
                  </div>
                  <h1 className="text-4xl md:text-5xl font-bold">{currentSlideData.title}</h1>
                  <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                    {currentSlideData.subtitle}
                  </p>
                </div>

                <div className="space-y-8">
                  <div className="space-y-6">
                    <h2 className="text-2xl font-bold text-center">Current Products</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-5xl mx-auto">
                      {currentSlideData.content.currentProducts.map((product, index) => (
                        <Card key={index} className="h-full">
                          <CardHeader>
                            <CardTitle className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="text-primary">{product.icon}</div>
                                {product.name}
                              </div>
                              <Badge variant={product.status === "Live" ? "default" : "secondary"}>
                                {product.status}
                              </Badge>
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <p className="text-muted-foreground">{product.description}</p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-6">
                    <h2 className="text-2xl font-bold text-center">Roadmap Highlights</h2>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-5xl mx-auto">
                      {currentSlideData.content.roadmap.map((section, index) => (
                        <Card key={index} className="h-full">
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                              <ArrowRight className="h-5 w-5 text-primary" />
                              {section.title}
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-2">
                              {section.items.map((item, itemIndex) => (
                                <li key={itemIndex} className="flex items-start gap-2">
                                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                                  <span className="text-sm">{item}</span>
                                </li>
                              ))}
                            </ul>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {currentSlideData.type === "team" && (
              <div className="space-y-12">
                <div className="text-center space-y-4">
                  <div className="flex justify-center mb-6">
                    {currentSlideData.content.icon}
                  </div>
                  <h1 className="text-4xl md:text-5xl font-bold">{currentSlideData.title}</h1>
                  <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                    {currentSlideData.subtitle}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
                  {currentSlideData.content.teams.map((team, index) => (
                    <Card key={index} className="h-full">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-3">
                          <div className="text-primary">{team.icon}</div>
                          {team.name}
                        </CardTitle>
                        <CardDescription>{team.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <h4 className="font-medium text-sm">Key Responsibilities:</h4>
                          <ul className="space-y-1">
                            {team.responsibilities.map((responsibility, respIndex) => (
                              <li key={respIndex} className="flex items-start gap-2">
                                <CheckCircle className="h-3 w-3 text-primary mt-1 flex-shrink-0" />
                                <span className="text-sm text-muted-foreground">{responsibility}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {currentSlideData.type === "workflow" && (
              <div className="space-y-12">
                <div className="text-center space-y-4">
                  <div className="flex justify-center mb-6">
                    {currentSlideData.content.icon}
                  </div>
                  <h1 className="text-4xl md:text-5xl font-bold">{currentSlideData.title}</h1>
                  <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                    {currentSlideData.subtitle}
                  </p>
                </div>

                <div className="space-y-8">
                  <Card className="max-w-4xl mx-auto">
                    <CardHeader>
                      <CardTitle>Our Methodology</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-lg">{currentSlideData.content.workflow.methodology}</p>
                      <div className="mt-4 space-y-3">
                        {currentSlideData.content.workflow.meetings.map((meeting, index) => (
                          <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                            <div>
                              <h4 className="font-medium">{meeting.name}</h4>
                              <p className="text-sm text-muted-foreground">{meeting.purpose}</p>
                            </div>
                            <Badge variant="outline">{meeting.frequency}</Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  <div className="space-y-6">
                    <h2 className="text-2xl font-bold text-center">Our Tools</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 max-w-5xl mx-auto">
                      {currentSlideData.content.tools.map((tool, index) => (
                        <Card key={index} className="text-center">
                          <CardContent className="pt-6">
                            <div className="flex justify-center mb-3 text-primary">
                              {tool.icon}
                            </div>
                            <h4 className="font-medium mb-2">{tool.name}</h4>
                            <p className="text-xs text-muted-foreground">{tool.purpose}</p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>

                  <Card className="max-w-4xl mx-auto">
                    <CardHeader>
                      <CardTitle>Communication Expectations</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2">
                        {currentSlideData.content.expectations.map((expectation, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                            <span className="text-sm">{expectation}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </div>
              </div>
            )}

            {currentSlideData.type === "opportunities" && (
              <div className="space-y-12">
                <div className="text-center space-y-4">
                  <div className="flex justify-center mb-6">
                    {currentSlideData.content.icon}
                  </div>
                  <h1 className="text-4xl md:text-5xl font-bold">{currentSlideData.title}</h1>
                  <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                    {currentSlideData.subtitle}
                  </p>
                </div>

                <div className="space-y-8">
                  <div className="space-y-6">
                    <h2 className="text-2xl font-bold text-center">Current Challenges</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-5xl mx-auto">
                      {currentSlideData.content.challenges.map((challenge, index) => (
                        <Card key={index} className="h-full">
                          <CardHeader>
                            <CardTitle className="text-lg">{challenge.title}</CardTitle>
                            <CardDescription>{challenge.description}</CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="flex gap-2">
                              <Badge variant={challenge.impact === "High" ? "default" : "secondary"}>
                                Impact: {challenge.impact}
                              </Badge>
                              <Badge variant={challenge.urgency === "High" ? "destructive" : challenge.urgency === "Medium" ? "default" : "secondary"}>
                                Urgency: {challenge.urgency}
                              </Badge>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>

                  <Card className="max-w-4xl mx-auto">
                    <CardHeader>
                      <CardTitle>Immediate Impact Opportunities</CardTitle>
                      <CardDescription>Areas where you can contribute from day one</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-3">
                        {currentSlideData.content.opportunities.map((opportunity, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <Star className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                            <span className="text-sm">{opportunity}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </div>
              </div>
            )}

            {currentSlideData.type === "qa" && (
              <div className="space-y-12">
                <div className="text-center space-y-4">
                  <div className="flex justify-center mb-6">
                    {currentSlideData.content.icon}
                  </div>
                  <h1 className="text-4xl md:text-5xl font-bold">{currentSlideData.title}</h1>
                  <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                    {currentSlideData.subtitle}
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
                  {currentSlideData.content.sections.map((section, index) => (
                    <Card key={index} className="h-full">
                      <CardHeader>
                        <CardTitle className="text-lg">{section.title}</CardTitle>
                        <CardDescription>{section.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2">
                          {section.items.map((item, itemIndex) => (
                            <li key={itemIndex} className="flex items-start gap-2">
                              <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                              <span className="text-sm">{item}</span>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <Card className="max-w-4xl mx-auto bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
                  <CardHeader>
                    <CardTitle className="text-center">Welcome to the Koja Team! 🎉</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center">
                    <p className="text-muted-foreground mb-4">
                      We're excited to have you on board and look forward to building the future of African digital banking together.
                    </p>
                    <Button className="gap-2" onClick={() => navigate('/dashboard')}>
                      <ArrowRight className="h-4 w-4" />
                      Let's Get Started
                    </Button>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </main>

        {/* Navigation Controls */}
        <div className="border-t bg-background/95 backdrop-blur">
          <div className="container mx-auto px-4 py-6">
            <div className="flex items-center justify-between">
              <Button 
                variant="outline" 
                onClick={prevSlide}
                disabled={currentSlide === 0}
                className="gap-2"
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>

              <div className="flex items-center gap-2">
                {slides.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToSlide(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      index === currentSlide ? 'bg-primary' : 'bg-muted'
                    }`}
                    aria-label={`Go to slide ${index + 1}`}
                  />
                ))}
              </div>

              <Button 
                variant="outline" 
                onClick={nextSlide}
                disabled={currentSlide === slides.length - 1}
                className="gap-2"
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            <div className="text-center mt-6">
              <h2 className="text-sm font-medium text-muted-foreground">
                {currentSlideData.title}
              </h2>
              <div className="flex items-center justify-center gap-2 mt-1">
                <Clock className="h-3 w-3 text-muted-foreground" />
                <p className="text-xs text-muted-foreground">
                  {currentSlideData.duration}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KojaPresentation;