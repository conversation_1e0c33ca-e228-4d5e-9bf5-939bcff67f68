const mongoose = require('mongoose');
const crypto = require('crypto');

const CardSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  // Paystack authorization code (token)
  authorizationCode: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  // Last 4 digits of the card
  last4: {
    type: String,
    required: true,
    length: 4,
    match: /^\d{4}$/
  },
  // Card brand (visa, mastercard, etc.)
  brand: {
    type: String,
    required: true,
    enum: ['visa', 'mastercard', 'verve', 'american_express', 'discover'],
    lowercase: true
  },
  // Issuing bank
  bank: {
    type: String,
    required: true
  },
  // Card type
  cardType: {
    type: String,
    enum: ['debit', 'credit'],
    default: 'debit'
  },
  // Expiry month (MM)
  expiryMonth: {
    type: String,
    required: true,
    match: /^(0[1-9]|1[0-2])$/
  },
  // Expiry year (YYYY)
  expiryYear: {
    type: String,
    required: true,
    match: /^\d{4}$/
  },
  // Whether this is the default card for the user
  isDefault: {
    type: Boolean,
    default: false
  },
  // Whether the card is active
  isActive: {
    type: Boolean,
    default: true
  },
  // Whether the card has been verified
  isVerified: {
    type: Boolean,
    default: false
  },
  // Verification amount charged (to be reversed)
  verificationAmount: {
    type: Number,
    default: 100 // ₦1.00 for verification
  },
  // Verification reference
  verificationReference: {
    type: String,
    index: true
  },
  // Last successful transaction date
  lastUsed: {
    type: Date
  },
  // Usage statistics
  usageStats: {
    totalTransactions: {
      type: Number,
      default: 0
    },
    totalAmount: {
      type: Number,
      default: 0
    },
    lastTransactionAmount: {
      type: Number
    },
    failedTransactions: {
      type: Number,
      default: 0
    }
  },
  // Security flags
  security: {
    isBlocked: {
      type: Boolean,
      default: false
    },
    blockReason: String,
    blockedAt: Date,
    suspiciousActivity: {
      type: Boolean,
      default: false
    },
    riskScore: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    }
  },
  // Metadata
  metadata: {
    ipAddress: String,
    userAgent: String,
    deviceFingerprint: String,
    addedVia: {
      type: String,
      enum: ['web', 'mobile', 'api'],
      default: 'web'
    }
  }
}, {
  timestamps: true,
  toJSON: { 
    virtuals: true,
    transform: function(doc, ret) {
      // Remove sensitive information from JSON output
      delete ret.authorizationCode;
      delete ret.verificationReference;
      return ret;
    }
  },
  toObject: { virtuals: true }
});

// Virtual for masked card number
CardSchema.virtual('maskedNumber').get(function() {
  return `**** **** **** ${this.last4}`;
});

// Virtual for expiry date
CardSchema.virtual('expiryDate').get(function() {
  return `${this.expiryMonth}/${this.expiryYear}`;
});

// Virtual for card display name
CardSchema.virtual('displayName').get(function() {
  return `${this.brand.toUpperCase()} *${this.last4}`;
});

// Virtual to check if card is expired
CardSchema.virtual('isExpired').get(function() {
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1;
  
  const cardYear = parseInt(this.expiryYear);
  const cardMonth = parseInt(this.expiryMonth);
  
  return cardYear < currentYear || (cardYear === currentYear && cardMonth < currentMonth);
});

// Pre-save middleware
CardSchema.pre('save', async function(next) {
  // If this card is being set as default, unset other default cards for this user
  if (this.isDefault && this.isModified('isDefault')) {
    await this.constructor.updateMany(
      { userId: this.userId, _id: { $ne: this._id } },
      { isDefault: false }
    );
  }
  
  // Update risk score based on usage patterns
  if (this.isModified('usageStats.failedTransactions')) {
    const failureRate = this.usageStats.failedTransactions / Math.max(this.usageStats.totalTransactions, 1);
    this.security.riskScore = Math.min(failureRate * 100, 100);
  }
  
  next();
});

// Indexes for efficient queries
CardSchema.index({ userId: 1, isDefault: 1 });
CardSchema.index({ userId: 1, isActive: 1 });
CardSchema.index({ expiryYear: 1, expiryMonth: 1 });
CardSchema.index({ 'security.isBlocked': 1 });
CardSchema.index({ createdAt: -1 });

// Static methods
CardSchema.statics.getUserCards = function(userId, activeOnly = true) {
  const query = { userId };
  if (activeOnly) {
    query.isActive = true;
    query['security.isBlocked'] = false;
  }
  return this.find(query).sort({ isDefault: -1, createdAt: -1 });
};

CardSchema.statics.getDefaultCard = function(userId) {
  return this.findOne({ 
    userId, 
    isDefault: true, 
    isActive: true,
    'security.isBlocked': false
  });
};

CardSchema.statics.findByAuthCode = function(authorizationCode) {
  return this.findOne({ authorizationCode });
};

// Instance methods
CardSchema.methods.verify = function(reference) {
  this.isVerified = true;
  this.verificationReference = reference;
  return this.save();
};

CardSchema.methods.setAsDefault = function() {
  this.isDefault = true;
  return this.save();
};

CardSchema.methods.block = function(reason) {
  this.security.isBlocked = true;
  this.security.blockReason = reason;
  this.security.blockedAt = new Date();
  this.isActive = false;
  return this.save();
};

CardSchema.methods.unblock = function() {
  this.security.isBlocked = false;
  this.security.blockReason = undefined;
  this.security.blockedAt = undefined;
  this.isActive = true;
  return this.save();
};

CardSchema.methods.recordTransaction = function(amount, success = true) {
  this.usageStats.totalTransactions += 1;
  this.lastUsed = new Date();
  
  if (success) {
    this.usageStats.totalAmount += amount;
    this.usageStats.lastTransactionAmount = amount;
  } else {
    this.usageStats.failedTransactions += 1;
  }
  
  return this.save();
};

CardSchema.methods.updateRiskScore = function() {
  const failureRate = this.usageStats.failedTransactions / Math.max(this.usageStats.totalTransactions, 1);
  const ageInDays = (Date.now() - this.createdAt) / (1000 * 60 * 60 * 24);
  const usageFrequency = this.usageStats.totalTransactions / Math.max(ageInDays, 1);
  
  // Calculate risk score based on multiple factors
  let riskScore = 0;
  
  // Failure rate (0-40 points)
  riskScore += failureRate * 40;
  
  // Low usage frequency (0-20 points)
  if (usageFrequency < 0.1) riskScore += 20;
  
  // Card age (newer cards are riskier) (0-20 points)
  if (ageInDays < 7) riskScore += 20;
  else if (ageInDays < 30) riskScore += 10;
  
  // Suspicious activity flag (0-20 points)
  if (this.security.suspiciousActivity) riskScore += 20;
  
  this.security.riskScore = Math.min(riskScore, 100);
  return this.save();
};

// Generate a secure card token for frontend use
CardSchema.methods.generateSecureToken = function() {
  const payload = {
    cardId: this._id,
    userId: this.userId,
    last4: this.last4,
    brand: this.brand,
    timestamp: Date.now()
  };
  
  return crypto
    .createHash('sha256')
    .update(JSON.stringify(payload) + process.env.CARD_TOKEN_SECRET)
    .digest('hex')
    .substring(0, 32);
};

module.exports = mongoose.model('Card', CardSchema);
