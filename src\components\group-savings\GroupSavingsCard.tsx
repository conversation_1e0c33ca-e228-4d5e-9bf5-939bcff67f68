import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { GroupSavingsPlan } from '@/services/group-savings';
import { Users, Calendar, Target, AlertTriangle } from 'lucide-react';
import { format } from 'date-fns';

interface GroupSavingsCardProps {
  group?: GroupSavingsPlan;
  plan?: GroupSavingsPlan;
  showJoinButton?: boolean;
  showWarning?: boolean;
  onJoin?: (plan: GroupSavingsPlan) => void;
  onContribute?: () => void;
}

export const GroupSavingsCard: React.FC<GroupSavingsCardProps> = ({ group, plan, showJoinButton = false, showWarning = true, onJoin, onContribute }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500 hover:bg-green-600';
      case 'pending': return 'bg-yellow-500 hover:bg-yellow-600';
      case 'completed': return 'bg-blue-500 hover:bg-blue-600';
      case 'cancelled': return 'bg-red-500 hover:bg-red-600';
      default: return 'bg-gray-500 hover:bg-gray-600';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'electronics': return 'text-blue-600';
      case 'car': return 'text-green-600';
      case 'phone': return 'text-purple-600';
      case 'grocery': return 'text-orange-600';
      default: return 'text-gray-600';
    }
  };

  const activeGroup = plan || group;
  if (!activeGroup) return null;
  
  const isNearCapacity = activeGroup.currentMembers >= activeGroup.maxMembers * 0.8;
  const isFull = activeGroup.currentMembers >= activeGroup.maxMembers;

  return (
    <Card className="hover:shadow-lg transition-shadow duration-200 border-border/50">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div className="space-y-1">
            <CardTitle className="text-lg font-semibold text-foreground">
              {activeGroup.name}
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">
              {activeGroup.description}
            </CardDescription>
          </div>
          <Badge className={getStatusColor(activeGroup.status)}>
            {activeGroup.status.charAt(0).toUpperCase() + activeGroup.status.slice(1)}
          </Badge>
        </div>
        
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <span className={`font-medium ${getCategoryColor(activeGroup.category)}`}>
            {activeGroup.category.charAt(0).toUpperCase() + activeGroup.category.slice(1)}
          </span>
          <span>•</span>
          <span>Code: {activeGroup.inviteCode}</span>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Progress</span>
            <span className="font-medium">
              {activeGroup.metadata.completionPercentage.toFixed(1)}%
            </span>
          </div>
          <Progress 
            value={activeGroup.metadata.completionPercentage} 
            className="h-2"
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>₦{activeGroup.metadata.totalCollected.toLocaleString()}</span>
            <span>₦{activeGroup.targetAmount.toLocaleString()}</span>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <span>
              {activeGroup.currentMembers}/{activeGroup.maxMembers} members
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Target className="h-4 w-4 text-muted-foreground" />
            <span>₦{activeGroup.contributionAmount.toLocaleString()}</span>
          </div>
          <div className="flex items-center gap-2 col-span-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span>
              Ends {format(new Date(activeGroup.endDate), 'MMM dd, yyyy')}
            </span>
          </div>
        </div>

        {/* Warning for new users */}
        {showWarning && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-none p-3">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
              <div className="text-xs text-yellow-800 dark:text-yellow-200">
                <p className="font-medium mb-1">Important Considerations:</p>
                <ul className="space-y-0.5 text-xs">
                  <li>• Verify group members and admin credibility</li>
                  <li>• Understand contribution schedule and penalties</li>
                  <li>• Ensure group goals align with your needs</li>
                  <li>• Read terms and conditions carefully</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Capacity Warning */}
        {isNearCapacity && !isFull && (
          <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-none p-2">
            <p className="text-xs text-orange-800 dark:text-orange-200 text-center">
              Almost full! Only {activeGroup.maxMembers - activeGroup.currentMembers} spots remaining
            </p>
          </div>
        )}

        {isFull && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-none p-2">
            <p className="text-xs text-red-800 dark:text-red-200 text-center font-medium">
              Group is Full
            </p>
          </div>
        )}
      </CardContent>

      <CardFooter className="pt-3">
        <div className="flex gap-2 w-full">
          {onContribute && (
            <Button
              onClick={onContribute}
              className="flex-1 bg-primary hover:bg-primary/90"
            >
              Contribute ₦{activeGroup.contributionAmount.toLocaleString()}
            </Button>
          )}
          {showJoinButton && onJoin && !isFull && activeGroup.status === 'active' && (
            <Button
              onClick={() => onJoin(activeGroup)}
              className="flex-1 bg-secondary hover:bg-secondary/90"
            >
              Join Group
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
};