import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock the Interest Calculation Service
const mockInterestService = {
  calculateFixedDepositInterest: vi.fn(),
  calculateFlexSavingsInterest: vi.fn(),
  calculateTargetSavingsInterest: vi.fn(),
  runDailyInterestCalculation: vi.fn(),
  processMaturedDeposits: vi.fn(),
  getStatus: vi.fn(),
  triggerManualCalculation: vi.fn(),
};

describe('Interest Calculation Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Daily Interest Calculation', () => {
    it('should calculate interest for all active fixed deposits', async () => {
      const mockResults = {
        fixedDeposits: {
          processed: 5,
          totalInterest: 2500,
          count: 5
        },
        flexSavings: {
          processed: 10,
          totalInterest: 150,
          count: 10
        },
        targetSavings: {
          processed: 15,
          totalInterest: 300,
          count: 15
        },
        errors: []
      };

      mockInterestService.runDailyInterestCalculation.mockResolvedValue(mockResults);

      const result = await mockInterestService.runDailyInterestCalculation();

      expect(result).toEqual(mockResults);
      expect(result.fixedDeposits.processed).toBe(5);
      expect(result.fixedDeposits.totalInterest).toBe(2500);
    });

    it('should handle calculation errors gracefully', async () => {
      const mockResults = {
        fixedDeposits: {
          processed: 3,
          totalInterest: 1500,
          count: 5
        },
        flexSavings: {
          processed: 8,
          totalInterest: 120,
          count: 10
        },
        targetSavings: {
          processed: 12,
          totalInterest: 240,
          count: 15
        },
        errors: [
          'Failed to calculate interest for deposit fd_123',
          'Failed to calculate interest for savings plan sp_456'
        ]
      };

      mockInterestService.runDailyInterestCalculation.mockResolvedValue(mockResults);

      const result = await mockInterestService.runDailyInterestCalculation();

      expect(result.errors).toHaveLength(2);
      expect(result.fixedDeposits.processed).toBeLessThan(result.fixedDeposits.count);
    });

    it('should prevent concurrent calculation runs', async () => {
      mockInterestService.getStatus.mockReturnValue({
        isRunning: true,
        lastRun: new Date(),
        nextRun: new Date(Date.now() + 24 * 60 * 60 * 1000)
      });

      mockInterestService.runDailyInterestCalculation.mockResolvedValue(null);

      const result = await mockInterestService.runDailyInterestCalculation();

      expect(result).toBeNull();
    });
  });

  describe('Fixed Deposit Interest Calculation', () => {
    it('should calculate compound interest correctly', async () => {
      const mockDeposits = [
        {
          id: 'fd_1',
          amount: 100000,
          interestRate: 15.0,
          startDate: new Date('2024-01-01'),
          duration: 90,
          status: 'active'
        },
        {
          id: 'fd_2',
          amount: 200000,
          interestRate: 20.0,
          startDate: new Date('2024-01-15'),
          duration: 365,
          status: 'active'
        }
      ];

      const expectedResults = {
        processed: 2,
        totalInterest: 1250, // Approximate daily compound interest
        count: 2
      };

      mockInterestService.calculateFixedDepositInterest.mockResolvedValue(expectedResults);

      const result = await mockInterestService.calculateFixedDepositInterest();

      expect(result.processed).toBe(2);
      expect(result.totalInterest).toBeGreaterThan(0);
      expect(result.count).toBe(2);
    });

    it('should handle different interest rates correctly', async () => {
      const testCases = [
        { amount: 100000, rate: 12.0, duration: 30, expectedDaily: 32.88 },
        { amount: 100000, rate: 15.0, duration: 90, expectedDaily: 41.10 },
        { amount: 100000, rate: 20.0, duration: 365, expectedDaily: 54.79 }
      ];

      for (const testCase of testCases) {
        const dailyRate = testCase.rate / 100 / 365;
        const expectedDailyInterest = testCase.amount * dailyRate;

        expect(expectedDailyInterest).toBeCloseTo(testCase.expectedDaily, 2);
      }
    });
  });

  describe('Matured Deposits Processing', () => {
    it('should process matured deposits and credit user accounts', async () => {
      const mockMaturedDeposits = [
        {
          id: 'fd_matured_1',
          userId: 'user_1',
          amount: 100000,
          finalAmount: 115000,
          maturityDate: new Date('2024-01-01')
        },
        {
          id: 'fd_matured_2',
          userId: 'user_2',
          amount: 200000,
          finalAmount: 240000,
          maturityDate: new Date('2024-01-02')
        }
      ];

      const expectedResults = {
        processed: 2,
        totalMatured: 355000,
        count: 2
      };

      mockInterestService.processMaturedDeposits.mockResolvedValue(expectedResults);

      const result = await mockInterestService.processMaturedDeposits();

      expect(result.processed).toBe(2);
      expect(result.totalMatured).toBe(355000);
    });

    it('should handle user account crediting errors', async () => {
      const expectedResults = {
        processed: 1,
        totalMatured: 115000,
        count: 2,
        errors: ['Failed to credit user account for deposit fd_matured_2']
      };

      mockInterestService.processMaturedDeposits.mockResolvedValue(expectedResults);

      const result = await mockInterestService.processMaturedDeposits();

      expect(result.processed).toBeLessThan(result.count);
      expect(result.errors).toBeDefined();
    });
  });

  describe('Target Savings Interest', () => {
    it('should calculate interest for target savings plans', async () => {
      const mockSavingsPlans = [
        {
          id: 'sp_1',
          currentAmount: 50000,
          interestRate: 5.0,
          status: 'active'
        },
        {
          id: 'sp_2',
          currentAmount: 75000,
          interestRate: 7.0,
          status: 'active'
        }
      ];

      const expectedResults = {
        processed: 2,
        totalInterest: 24, // Daily interest for both plans
        count: 2
      };

      mockInterestService.calculateTargetSavingsInterest.mockResolvedValue(expectedResults);

      const result = await mockInterestService.calculateTargetSavingsInterest();

      expect(result.processed).toBe(2);
      expect(result.totalInterest).toBeGreaterThan(0);
    });

    it('should skip plans with zero balance', async () => {
      const expectedResults = {
        processed: 1,
        totalInterest: 12,
        count: 2 // One plan had zero balance
      };

      mockInterestService.calculateTargetSavingsInterest.mockResolvedValue(expectedResults);

      const result = await mockInterestService.calculateTargetSavingsInterest();

      expect(result.processed).toBeLessThan(result.count);
    });
  });

  describe('Manual Calculation Trigger', () => {
    it('should allow manual triggering of interest calculation', async () => {
      const mockResults = {
        fixedDeposits: { processed: 3, totalInterest: 1500, count: 3 },
        flexSavings: { processed: 5, totalInterest: 75, count: 5 },
        targetSavings: { processed: 8, totalInterest: 120, count: 8 },
        errors: []
      };

      mockInterestService.triggerManualCalculation.mockResolvedValue(mockResults);

      const result = await mockInterestService.triggerManualCalculation();

      expect(result).toEqual(mockResults);
      expect(mockInterestService.triggerManualCalculation).toHaveBeenCalledOnce();
    });

    it('should prevent manual calculation when auto calculation is running', async () => {
      mockInterestService.getStatus.mockReturnValue({
        isRunning: true,
        lastRun: new Date(),
        nextRun: new Date(Date.now() + 24 * 60 * 60 * 1000)
      });

      mockInterestService.triggerManualCalculation.mockRejectedValue(
        new Error('Interest calculation is already running')
      );

      await expect(mockInterestService.triggerManualCalculation()).rejects.toThrow(
        'Interest calculation is already running'
      );
    });
  });

  describe('Service Status', () => {
    it('should provide accurate service status', () => {
      const mockStatus = {
        isRunning: false,
        lastRun: new Date('2024-01-01T00:00:00Z'),
        nextRun: new Date('2024-01-02T00:00:00Z')
      };

      mockInterestService.getStatus.mockReturnValue(mockStatus);

      const status = mockInterestService.getStatus();

      expect(status.isRunning).toBe(false);
      expect(status.lastRun).toBeInstanceOf(Date);
      expect(status.nextRun).toBeInstanceOf(Date);
      expect(status.nextRun.getTime()).toBeGreaterThan(status.lastRun.getTime());
    });
  });

  describe('Interest Calculation Accuracy', () => {
    it('should calculate compound interest accurately', () => {
      // Test compound interest formula: A = P(1 + r/n)^(nt)
      const principal = 100000;
      const annualRate = 0.15; // 15%
      const compoundingFrequency = 365; // Daily
      const timeInYears = 90 / 365; // 90 days

      const expectedAmount = principal * Math.pow(
        1 + annualRate / compoundingFrequency,
        compoundingFrequency * timeInYears
      );

      const expectedInterest = expectedAmount - principal;

      // Should be approximately ₦3,750 for 90 days at 15% annual rate
      expect(expectedInterest).toBeCloseTo(3750, -1); // Within ₦10
    });

    it('should handle different compounding frequencies', () => {
      const principal = 100000;
      const annualRate = 0.15;
      const timeInYears = 1;

      const daily = principal * Math.pow(1 + annualRate / 365, 365 * timeInYears);
      const monthly = principal * Math.pow(1 + annualRate / 12, 12 * timeInYears);
      const annually = principal * Math.pow(1 + annualRate / 1, 1 * timeInYears);

      // Daily compounding should yield the highest return
      expect(daily).toBeGreaterThan(monthly);
      expect(monthly).toBeGreaterThan(annually);
    });
  });
});
