const mongoose = require('mongoose');
const EventEmitter = require('events');

class DatabaseHealthService extends EventEmitter {
  constructor() {
    super();
    this.healthMetrics = {
      connectionStatus: 'unknown',
      lastHealthCheck: null,
      responseTime: 0,
      activeConnections: 0,
      totalQueries: 0,
      slowQueries: 0,
      errors: 0,
      uptime: 0,
      memoryUsage: {},
      diskUsage: {},
      replicationLag: 0,
      indexStats: {},
      collectionStats: {}
    };
    
    this.thresholds = {
      responseTime: 1000, // 1 second
      slowQueryTime: 5000, // 5 seconds
      maxConnections: 80, // 80% of max pool size
      memoryUsage: 85, // 85% of available memory
      diskUsage: 90, // 90% of available disk
      replicationLag: 10000 // 10 seconds
    };
    
    this.alertHistory = [];
    this.isMonitoring = false;
    this.monitoringInterval = null;
    this.queryPerformanceLog = [];
    this.maxLogEntries = 1000;
  }

  /**
   * Start health monitoring
   * @param {number} interval - Monitoring interval in milliseconds (default: 30 seconds)
   */
  startMonitoring(interval = 30000) {
    if (this.isMonitoring) {
      console.log('🔍 Database health monitoring is already running');
      return;
    }

    console.log(`🔍 Starting database health monitoring (interval: ${interval / 1000}s)`);
    this.isMonitoring = true;
    
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        console.error('❌ Health monitoring error:', error);
        this.recordError('health_check_failed', error.message);
      }
    }, interval);

    // Initial health check
    this.performHealthCheck();
  }

  /**
   * Stop health monitoring
   */
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }

    console.log('🛑 Stopping database health monitoring');
    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  /**
   * Perform comprehensive health check
   */
  async performHealthCheck() {
    const startTime = Date.now();
    
    try {
      // Check basic connectivity
      await this.checkConnectivity();
      
      // Get server status
      await this.getServerStatus();
      
      // Check connection pool
      await this.checkConnectionPool();
      
      // Get collection statistics
      await this.getCollectionStats();
      
      // Check index performance
      await this.checkIndexPerformance();
      
      // Check replication status (if applicable)
      await this.checkReplicationStatus();
      
      // Update metrics
      this.healthMetrics.lastHealthCheck = new Date();
      this.healthMetrics.responseTime = Date.now() - startTime;
      this.healthMetrics.connectionStatus = 'healthy';
      
      // Evaluate health and trigger alerts if needed
      this.evaluateHealth();
      
      // Emit health update event
      this.emit('healthUpdate', this.healthMetrics);
      
    } catch (error) {
      this.healthMetrics.connectionStatus = 'unhealthy';
      this.healthMetrics.responseTime = Date.now() - startTime;
      this.recordError('health_check', error.message);
      
      this.emit('healthError', {
        error: error.message,
        metrics: this.healthMetrics
      });
    }
  }

  /**
   * Check basic database connectivity
   */
  async checkConnectivity() {
    const connection = mongoose.connection;
    
    if (connection.readyState !== 1) {
      throw new Error(`Database not connected. ReadyState: ${connection.readyState}`);
    }
    
    // Perform a simple ping
    await connection.db.admin().ping();
  }

  /**
   * Get comprehensive server status
   */
  async getServerStatus() {
    try {
      const db = mongoose.connection.db;
      const serverStatus = await db.admin().serverStatus();
      
      this.healthMetrics.uptime = serverStatus.uptime;
      this.healthMetrics.activeConnections = serverStatus.connections.current;
      this.healthMetrics.totalQueries = serverStatus.opcounters.query + serverStatus.opcounters.getmore;
      
      // Memory usage
      this.healthMetrics.memoryUsage = {
        resident: serverStatus.mem.resident,
        virtual: serverStatus.mem.virtual,
        mapped: serverStatus.mem.mapped || 0,
        mappedWithJournal: serverStatus.mem.mappedWithJournal || 0
      };
      
      // Network metrics
      this.healthMetrics.network = {
        bytesIn: serverStatus.network.bytesIn,
        bytesOut: serverStatus.network.bytesOut,
        numRequests: serverStatus.network.numRequests
      };
      
      // Operation counters
      this.healthMetrics.operations = {
        insert: serverStatus.opcounters.insert,
        query: serverStatus.opcounters.query,
        update: serverStatus.opcounters.update,
        delete: serverStatus.opcounters.delete,
        getmore: serverStatus.opcounters.getmore,
        command: serverStatus.opcounters.command
      };
      
    } catch (error) {
      console.warn('⚠️ Could not get server status:', error.message);
    }
  }

  /**
   * Check connection pool status
   */
  async checkConnectionPool() {
    try {
      const connection = mongoose.connection;
      const poolSize = connection.db.serverConfig?.poolSize || 0;
      const maxPoolSize = connection.db.serverConfig?.options?.maxPoolSize || 10;
      
      this.healthMetrics.connectionPool = {
        current: poolSize,
        max: maxPoolSize,
        utilization: (poolSize / maxPoolSize) * 100
      };
      
    } catch (error) {
      console.warn('⚠️ Could not get connection pool stats:', error.message);
    }
  }

  /**
   * Get collection statistics
   */
  async getCollectionStats() {
    try {
      const db = mongoose.connection.db;
      const collections = await db.listCollections().toArray();
      
      this.healthMetrics.collectionStats = {};
      
      for (const collection of collections.slice(0, 10)) { // Limit to first 10 collections
        try {
          const stats = await db.collection(collection.name).stats();
          this.healthMetrics.collectionStats[collection.name] = {
            count: stats.count,
            size: stats.size,
            avgObjSize: stats.avgObjSize,
            storageSize: stats.storageSize,
            indexes: stats.nindexes,
            indexSize: stats.totalIndexSize
          };
        } catch (error) {
          // Skip collections that can't be accessed
          continue;
        }
      }
      
    } catch (error) {
      console.warn('⚠️ Could not get collection stats:', error.message);
    }
  }

  /**
   * Check index performance
   */
  async checkIndexPerformance() {
    try {
      const db = mongoose.connection.db;
      
      // Get index stats for main collections
      const mainCollections = ['users', 'transactions', 'savingsplans'];
      this.healthMetrics.indexStats = {};
      
      for (const collectionName of mainCollections) {
        try {
          const collection = db.collection(collectionName);
          const indexStats = await collection.indexStats().toArray();
          
          this.healthMetrics.indexStats[collectionName] = indexStats.map(stat => ({
            name: stat.name,
            accesses: stat.accesses.ops,
            since: stat.accesses.since
          }));
        } catch (error) {
          // Collection might not exist
          continue;
        }
      }
      
    } catch (error) {
      console.warn('⚠️ Could not get index stats:', error.message);
    }
  }

  /**
   * Check replication status (for replica sets)
   */
  async checkReplicationStatus() {
    try {
      const db = mongoose.connection.db;
      const replStatus = await db.admin().replSetGetStatus();
      
      if (replStatus && replStatus.members) {
        const primary = replStatus.members.find(member => member.state === 1);
        const secondaries = replStatus.members.filter(member => member.state === 2);
        
        this.healthMetrics.replication = {
          isPrimary: primary ? primary.self : false,
          secondaryCount: secondaries.length,
          lag: this.calculateReplicationLag(replStatus.members)
        };
      }
      
    } catch (error) {
      // Not a replica set or no permission
      this.healthMetrics.replication = { status: 'not_applicable' };
    }
  }

  /**
   * Calculate replication lag
   */
  calculateReplicationLag(members) {
    const primary = members.find(member => member.state === 1);
    if (!primary) return 0;
    
    const secondaries = members.filter(member => member.state === 2);
    if (secondaries.length === 0) return 0;
    
    const maxLag = Math.max(...secondaries.map(secondary => {
      return primary.optimeDate - secondary.optimeDate;
    }));
    
    return maxLag;
  }

  /**
   * Evaluate overall health and trigger alerts
   */
  evaluateHealth() {
    const alerts = [];
    
    // Check response time
    if (this.healthMetrics.responseTime > this.thresholds.responseTime) {
      alerts.push({
        type: 'performance',
        severity: 'warning',
        message: `High response time: ${this.healthMetrics.responseTime}ms`,
        threshold: this.thresholds.responseTime
      });
    }
    
    // Check connection pool utilization
    if (this.healthMetrics.connectionPool?.utilization > this.thresholds.maxConnections) {
      alerts.push({
        type: 'connections',
        severity: 'warning',
        message: `High connection pool utilization: ${this.healthMetrics.connectionPool.utilization.toFixed(1)}%`,
        threshold: this.thresholds.maxConnections
      });
    }
    
    // Check memory usage
    if (this.healthMetrics.memoryUsage?.resident > this.thresholds.memoryUsage) {
      alerts.push({
        type: 'memory',
        severity: 'critical',
        message: `High memory usage: ${this.healthMetrics.memoryUsage.resident}MB`,
        threshold: this.thresholds.memoryUsage
      });
    }
    
    // Check replication lag
    if (this.healthMetrics.replication?.lag > this.thresholds.replicationLag) {
      alerts.push({
        type: 'replication',
        severity: 'warning',
        message: `High replication lag: ${this.healthMetrics.replication.lag}ms`,
        threshold: this.thresholds.replicationLag
      });
    }
    
    // Process alerts
    if (alerts.length > 0) {
      this.processAlerts(alerts);
    }
  }

  /**
   * Process and handle alerts
   */
  processAlerts(alerts) {
    for (const alert of alerts) {
      const alertKey = `${alert.type}_${alert.severity}`;
      const lastAlert = this.alertHistory.find(a => a.key === alertKey);
      
      // Avoid spam - only alert if last alert was more than 5 minutes ago
      if (!lastAlert || Date.now() - lastAlert.timestamp > 300000) {
        console.warn(`🚨 Database Alert [${alert.severity.toUpperCase()}]: ${alert.message}`);
        
        this.alertHistory.push({
          key: alertKey,
          timestamp: Date.now(),
          ...alert
        });
        
        // Keep only last 100 alerts
        if (this.alertHistory.length > 100) {
          this.alertHistory = this.alertHistory.slice(-100);
        }
        
        // Emit alert event
        this.emit('alert', alert);
      }
    }
  }

  /**
   * Record query performance
   */
  recordQueryPerformance(operation, duration, collection) {
    const entry = {
      timestamp: Date.now(),
      operation,
      duration,
      collection,
      isSlow: duration > this.thresholds.slowQueryTime
    };
    
    this.queryPerformanceLog.push(entry);
    
    // Keep only recent entries
    if (this.queryPerformanceLog.length > this.maxLogEntries) {
      this.queryPerformanceLog = this.queryPerformanceLog.slice(-this.maxLogEntries);
    }
    
    // Count slow queries
    if (entry.isSlow) {
      this.healthMetrics.slowQueries++;
      console.warn(`🐌 Slow query detected: ${operation} on ${collection} took ${duration}ms`);
    }
  }

  /**
   * Record error
   */
  recordError(type, message) {
    this.healthMetrics.errors++;
    console.error(`❌ Database error [${type}]: ${message}`);
    
    this.emit('error', { type, message, timestamp: Date.now() });
  }

  /**
   * Get current health status
   */
  getHealthStatus() {
    return {
      ...this.healthMetrics,
      isMonitoring: this.isMonitoring,
      alertHistory: this.alertHistory.slice(-10), // Last 10 alerts
      queryPerformance: {
        totalQueries: this.queryPerformanceLog.length,
        slowQueries: this.queryPerformanceLog.filter(q => q.isSlow).length,
        avgResponseTime: this.calculateAverageResponseTime()
      }
    };
  }

  /**
   * Calculate average response time from recent queries
   */
  calculateAverageResponseTime() {
    if (this.queryPerformanceLog.length === 0) return 0;
    
    const recentQueries = this.queryPerformanceLog.slice(-100); // Last 100 queries
    const totalTime = recentQueries.reduce((sum, query) => sum + query.duration, 0);
    
    return totalTime / recentQueries.length;
  }

  /**
   * Get performance report
   */
  getPerformanceReport(timeRange = 3600000) { // Default: last hour
    const cutoff = Date.now() - timeRange;
    const recentQueries = this.queryPerformanceLog.filter(q => q.timestamp > cutoff);
    
    const report = {
      timeRange: timeRange / 1000 / 60, // Convert to minutes
      totalQueries: recentQueries.length,
      slowQueries: recentQueries.filter(q => q.isSlow).length,
      avgResponseTime: 0,
      operationBreakdown: {},
      collectionBreakdown: {}
    };
    
    if (recentQueries.length > 0) {
      report.avgResponseTime = recentQueries.reduce((sum, q) => sum + q.duration, 0) / recentQueries.length;
      
      // Group by operation
      recentQueries.forEach(query => {
        report.operationBreakdown[query.operation] = (report.operationBreakdown[query.operation] || 0) + 1;
        report.collectionBreakdown[query.collection] = (report.collectionBreakdown[query.collection] || 0) + 1;
      });
    }
    
    return report;
  }

  /**
   * Reset metrics
   */
  resetMetrics() {
    this.healthMetrics.errors = 0;
    this.healthMetrics.slowQueries = 0;
    this.queryPerformanceLog = [];
    this.alertHistory = [];
    
    console.log('📊 Database health metrics reset');
  }
}

// Create singleton instance
const databaseHealthService = new DatabaseHealthService();

module.exports = databaseHealthService;
