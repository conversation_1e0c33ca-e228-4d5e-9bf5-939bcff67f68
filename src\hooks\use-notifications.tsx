import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';
import React, { createContext, useContext, useEffect, useState } from 'react';

console.log('use-notifications.tsx loading...');

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: Date;
  userId?: string;
  channel?: NotificationChannel;
  priority?: 'low' | 'medium' | 'high';
  timestamp?: number;
}

export type NotificationType = 'group_invite' | 'contribution_reminder' | 'group_update' | 'payment_success' | 'general' | 'success' | 'error' | 'warning' | 'info';

export type NotificationChannel = 'push' | 'email' | 'sms' | 'in_app';

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (notification: Omit<Notification, 'id' | 'read' | 'createdAt'>) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  removeNotification: (id: string) => void;
  clearAllNotifications: () => void;
  clearNotifications: () => void;
  deleteNotification: (id: string) => void;
  filterNotifications: (typeFilter?: string, channelFilter?: string) => Notification[];
  fetchNotifications: () => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const { toast } = useToast();
  const { user } = useAuth();

  const unreadCount = notifications.filter(n => !n.read).length;

  const addNotification = (notificationData: Omit<Notification, 'id' | 'read' | 'createdAt'>) => {
    const notification: Notification = {
      ...notificationData,
      id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      read: false,
      createdAt: new Date()
    };

    setNotifications(prev => [notification, ...prev]);

    // Show toast for important notifications
    if (notification.type === 'group_invite' || notification.type === 'payment_success') {
      toast({
        title: notification.title,
        description: notification.message,
      });
    }
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  const clearNotifications = clearAllNotifications;
  const deleteNotification = removeNotification;
  
  const filterNotifications = (typeFilter?: string, channelFilter?: string): Notification[] => {
    let filtered = notifications;
    
    if (typeFilter && typeFilter !== 'all') {
      filtered = filtered.filter(n => n.type === typeFilter);
    }
    
    if (channelFilter) {
      filtered = filtered.filter(n => n.channel === channelFilter);
    }
    
    return filtered;
  };

  const fetchNotifications = async (): Promise<void> => {
    try {
      const response = await fetch('/api/v1/notifications', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        const fetchedNotifications = data.data || [];
        setNotifications(fetchedNotifications);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    }
  };

  // Fetch notifications when user is available
  useEffect(() => {
    if (user) {
      fetchNotifications();
    }
  }, [user]);

  // Demo notifications fallback for development
  useEffect(() => {
    if (user && notifications.length === 0) {
      const demoNotifications: Notification[] = [
        {
          id: 'demo_1',
          type: 'group_invite',
          title: 'Group Invitation',
          message: 'You have been invited to join "iPhone 15 Savings Group"',
          data: { groupId: 'demo_group_1', inviteCode: 'ABC123' },
          read: false,
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1 day ago
        },
        {
          id: 'demo_2',
          type: 'contribution_reminder',
          title: 'Contribution Reminder',
          message: 'Your monthly contribution of ₦5,000 is due tomorrow',
          data: { groupId: 'demo_group_2', amount: 5000 },
          read: false,
          createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000) // 12 hours ago
        },
        {
          id: 'demo_3',
          type: 'group_update',
          title: 'Group Target Achieved!',
          message: 'Congratulations! Your group "Laptop Fund" has reached its target of ₦200,000',
          data: { groupId: 'demo_group_3', targetAmount: 200000 },
          read: true,
          createdAt: new Date(Date.now() - 48 * 60 * 60 * 1000) // 2 days ago
        }
      ];
      setNotifications(demoNotifications);
    }
  }, [user]);

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAllNotifications,
    clearNotifications,
    deleteNotification,
    filterNotifications,
    fetchNotifications
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
