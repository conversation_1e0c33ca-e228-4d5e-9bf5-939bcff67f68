
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 relative overflow-hidden transition-all duration-200 transform-gpu will-change-transform touch-manipulation select-none border border-[hsl(var(--button-border))]",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground border-0 rounded-none shadow-[inset_4px_4px_8px_rgba(0,0,0,0.15),inset_-4px_-4px_8px_rgba(255,255,255,0.1)] hover:shadow-[inset_6px_6px_12px_rgba(0,0,0,0.2),inset_-6px_-6px_12px_rgba(255,255,255,0.15)] active:shadow-[inset_8px_8px_16px_rgba(0,0,0,0.25),inset_-8px_-8px_16px_rgba(255,255,255,0.05)] active:scale-[0.98]",
        destructive: "bg-background text-destructive border-0 rounded-none shadow-[inset_4px_4px_8px_rgba(220,38,38,0.15),inset_-4px_-4px_8px_rgba(255,255,255,0.1)] hover:shadow-[inset_6px_6px_12px_rgba(220,38,38,0.2),inset_-6px_-6px_12px_rgba(255,255,255,0.15)] active:shadow-[inset_8px_8px_16px_rgba(220,38,38,0.25),inset_-8px_-8px_16px_rgba(255,255,255,0.05)] active:scale-[0.98]",
        outline: "bg-background text-primary border-2 border-primary/20 rounded-none shadow-[inset_2px_2px_4px_rgba(0,0,0,0.1),inset_-2px_-2px_4px_rgba(255,255,255,0.1)] hover:shadow-[inset_4px_4px_8px_rgba(0,0,0,0.15),inset_-4px_-4px_8px_rgba(255,255,255,0.15)] active:shadow-[inset_6px_6px_12px_rgba(0,0,0,0.2),inset_-6px_-6px_12px_rgba(255,255,255,0.05)] active:scale-[0.98]",
        secondary: "bg-background text-secondary-foreground border-0 rounded-none shadow-[inset_4px_4px_8px_rgba(0,0,0,0.15),inset_-4px_-4px_8px_rgba(255,255,255,0.1)] hover:shadow-[inset_6px_6px_12px_rgba(0,0,0,0.2),inset_-6px_-6px_12px_rgba(255,255,255,0.15)] active:shadow-[inset_8px_8px_16px_rgba(0,0,0,0.25),inset_-8px_-8px_16px_rgba(255,255,255,0.05)] active:scale-[0.98]",
        ghost: "bg-transparent text-foreground border-0 rounded-none hover:bg-background/50 hover:shadow-[inset_2px_2px_4px_rgba(0,0,0,0.1),inset_-2px_-2px_4px_rgba(255,255,255,0.1)] active:shadow-[inset_4px_4px_8px_rgba(0,0,0,0.15),inset_-4px_-4px_8px_rgba(255,255,255,0.05)] active:scale-[0.98]",
        link: "text-primary underline-offset-4 hover:underline p-0 h-auto font-normal border-0 rounded-none",
        premium: "bg-background text-amber-600 border-0 rounded-none shadow-[inset_4px_4px_8px_rgba(245,158,11,0.15),inset_-4px_-4px_8px_rgba(255,255,255,0.1)] hover:shadow-[inset_6px_6px_12px_rgba(245,158,11,0.2),inset_-6px_-6px_12px_rgba(255,255,255,0.15)] active:shadow-[inset_8px_8px_16px_rgba(245,158,11,0.25),inset_-8px_-8px_16px_rgba(255,255,255,0.05)] active:scale-[0.98]",
        success: "bg-background text-green-600 border-0 rounded-none shadow-[inset_4px_4px_8px_rgba(34,197,94,0.15),inset_-4px_-4px_8px_rgba(255,255,255,0.1)] hover:shadow-[inset_6px_6px_12px_rgba(34,197,94,0.2),inset_-6px_-6px_12px_rgba(255,255,255,0.15)] active:shadow-[inset_8px_8px_16px_rgba(34,197,94,0.25),inset_-8px_-8px_16px_rgba(255,255,255,0.05)] active:scale-[0.98]",
        glass: "bg-background/80 backdrop-blur-md border border-white/10 text-foreground rounded-none shadow-[inset_2px_2px_4px_rgba(0,0,0,0.1),inset_-2px_-2px_4px_rgba(255,255,255,0.1)] hover:shadow-[inset_4px_4px_8px_rgba(0,0,0,0.15),inset_-4px_-4px_8px_rgba(255,255,255,0.15)] active:shadow-[inset_6px_6px_12px_rgba(0,0,0,0.2),inset_-6px_-6px_12px_rgba(255,255,255,0.05)] active:scale-[0.98]",
        floating: "bg-background text-primary border-0 rounded-none shadow-[inset_4px_4px_8px_rgba(0,0,0,0.15),inset_-4px_-4px_8px_rgba(255,255,255,0.1)] hover:shadow-[inset_6px_6px_12px_rgba(0,0,0,0.2),inset_-6px_-6px_12px_rgba(255,255,255,0.15)] active:shadow-[inset_8px_8px_16px_rgba(0,0,0,0.25),inset_-8px_-8px_16px_rgba(255,255,255,0.05)] active:scale-[0.98]",
        brand: "bg-background text-primary border-0 rounded-none shadow-[inset_4px_4px_8px_rgba(0,0,0,0.15),inset_-4px_-4px_8px_rgba(255,255,255,0.1)] hover:shadow-[inset_6px_6px_12px_rgba(0,0,0,0.2),inset_-6px_-6px_12px_rgba(255,255,255,0.15)] active:shadow-[inset_8px_8px_16px_rgba(0,0,0,0.25),inset_-8px_-8px_16px_rgba(255,255,255,0.05)] active:scale-[0.98]",
        yellow: "bg-background text-amber-600 border-0 rounded-none shadow-[inset_4px_4px_8px_rgba(245,158,11,0.15),inset_-4px_-4px_8px_rgba(255,255,255,0.1)] hover:shadow-[inset_6px_6px_12px_rgba(245,158,11,0.2),inset_-6px_-6px_12px_rgba(255,255,255,0.15)] active:shadow-[inset_8px_8px_16px_rgba(245,158,11,0.25),inset_-8px_-8px_16px_rgba(255,255,255,0.05)] active:scale-[0.98]",
        admin: "bg-background text-secondary border-0 rounded-none shadow-[inset_4px_4px_8px_rgba(0,0,0,0.15),inset_-4px_-4px_8px_rgba(255,255,255,0.1)] hover:shadow-[inset_6px_6px_12px_rgba(0,0,0,0.2),inset_-6px_-6px_12px_rgba(255,255,255,0.15)] active:shadow-[inset_8px_8px_16px_rgba(0,0,0,0.25),inset_-8px_-8px_16px_rgba(255,255,255,0.05)] active:scale-[0.98]"
      },
      size: {
        default: "h-10 px-4 py-2 text-sm sm:h-11 sm:px-6 sm:text-base",
        sm: "h-8 px-3 text-xs sm:h-9 sm:px-4 sm:text-sm",
        lg: "h-12 px-6 text-base sm:h-14 sm:px-8 sm:text-lg",
        xl: "h-14 px-8 text-lg sm:h-16 sm:px-12 sm:text-xl",
        icon: "h-10 w-10 p-0 sm:h-11 sm:w-11",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, children, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    
    return (
      <Comp
        className={cn(buttonVariants({ variant, size }), className)}
        ref={ref}
        {...props}
      >
        {children}
      </Comp>
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
