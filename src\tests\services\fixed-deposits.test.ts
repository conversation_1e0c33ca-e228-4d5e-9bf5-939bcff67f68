import { describe, it, expect, beforeEach, vi } from 'vitest';
import { fixedDepositAPI } from '@/services/api-client';

// Mock the API client
vi.mock('@/services/api-client', () => ({
  fixedDepositAPI: {
    getRates: vi.fn(),
    create: vi.fn(),
    getDeposits: vi.fn(),
    breakDeposit: vi.fn(),
    calculateInterest: vi.fn(),
  }
}));

describe('Fixed Deposits API', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getRates', () => {
    it('should fetch fixed deposit rates successfully', async () => {
      const mockRates = {
        success: true,
        data: {
          rates: [
            { duration: 30, rate: 12.0, label: '1 Month' },
            { duration: 90, rate: 15.0, label: '3 Months' },
            { duration: 365, rate: 20.0, label: '12 Months' }
          ],
          minimumAmount: 10000,
          maximumAmount: 10000000,
          penaltyRate: 25
        }
      };

      vi.mocked(fixedDepositAPI.getRates).mockResolvedValue(mockRates);

      const result = await fixedDepositAPI.getRates();

      expect(result).toEqual(mockRates);
      expect(fixedDepositAPI.getRates).toHaveBeenCalledOnce();
    });

    it('should handle API errors when fetching rates', async () => {
      const mockError = new Error('Failed to fetch rates');
      vi.mocked(fixedDepositAPI.getRates).mockRejectedValue(mockError);

      await expect(fixedDepositAPI.getRates()).rejects.toThrow('Failed to fetch rates');
    });
  });

  describe('create', () => {
    it('should create fixed deposit successfully', async () => {
      const depositData = {
        amount: 100000,
        duration: 90,
        autoRenewal: false
      };

      const mockResponse = {
        success: true,
        data: {
          id: 'fd_123',
          amount: 100000,
          duration: 90,
          interestRate: 15.0,
          maturityDate: '2024-04-01T00:00:00.000Z',
          status: 'active'
        }
      };

      vi.mocked(fixedDepositAPI.create).mockResolvedValue(mockResponse);

      const result = await fixedDepositAPI.create(depositData);

      expect(result).toEqual(mockResponse);
      expect(fixedDepositAPI.create).toHaveBeenCalledWith(depositData);
    });

    it('should validate minimum amount', async () => {
      const invalidData = {
        amount: 5000, // Below minimum
        duration: 90
      };

      const mockError = new Error('Minimum fixed deposit amount is ₦10,000');
      vi.mocked(fixedDepositAPI.create).mockRejectedValue(mockError);

      await expect(fixedDepositAPI.create(invalidData)).rejects.toThrow(
        'Minimum fixed deposit amount is ₦10,000'
      );
    });

    it('should handle insufficient balance error', async () => {
      const depositData = {
        amount: 100000,
        duration: 90
      };

      const mockError = new Error('Insufficient balance');
      vi.mocked(fixedDepositAPI.create).mockRejectedValue(mockError);

      await expect(fixedDepositAPI.create(depositData)).rejects.toThrow('Insufficient balance');
    });
  });

  describe('getDeposits', () => {
    it('should fetch user deposits successfully', async () => {
      const mockDeposits = {
        success: true,
        data: {
          deposits: [
            {
              id: 'fd_123',
              amount: 100000,
              duration: 90,
              interestRate: 15.0,
              status: 'active',
              currentValue: 103750,
              daysRemaining: 45
            },
            {
              id: 'fd_124',
              amount: 200000,
              duration: 365,
              interestRate: 20.0,
              status: 'matured',
              finalAmount: 240000
            }
          ],
          pagination: {
            page: 1,
            limit: 10,
            total: 2,
            pages: 1
          }
        }
      };

      vi.mocked(fixedDepositAPI.getDeposits).mockResolvedValue(mockDeposits);

      const result = await fixedDepositAPI.getDeposits();

      expect(result).toEqual(mockDeposits);
      expect(fixedDepositAPI.getDeposits).toHaveBeenCalledWith(undefined);
    });

    it('should filter deposits by status', async () => {
      const mockActiveDeposits = {
        success: true,
        data: {
          deposits: [
            {
              id: 'fd_123',
              amount: 100000,
              status: 'active'
            }
          ]
        }
      };

      vi.mocked(fixedDepositAPI.getDeposits).mockResolvedValue(mockActiveDeposits);

      const result = await fixedDepositAPI.getDeposits('active');

      expect(result).toEqual(mockActiveDeposits);
      expect(fixedDepositAPI.getDeposits).toHaveBeenCalledWith('active');
    });
  });

  describe('breakDeposit', () => {
    it('should break deposit successfully', async () => {
      const depositId = 'fd_123';
      const mockResponse = {
        success: true,
        data: {
          originalAmount: 100000,
          interestEarned: 5000,
          penalty: 1250,
          netInterest: 3750,
          finalAmount: 103750,
          creditedAmount: 103750
        }
      };

      vi.mocked(fixedDepositAPI.breakDeposit).mockResolvedValue(mockResponse);

      const result = await fixedDepositAPI.breakDeposit(depositId);

      expect(result).toEqual(mockResponse);
      expect(fixedDepositAPI.breakDeposit).toHaveBeenCalledWith(depositId);
    });

    it('should handle deposit not found error', async () => {
      const depositId = 'invalid_id';
      const mockError = new Error('Fixed deposit not found');
      vi.mocked(fixedDepositAPI.breakDeposit).mockRejectedValue(mockError);

      await expect(fixedDepositAPI.breakDeposit(depositId)).rejects.toThrow(
        'Fixed deposit not found'
      );
    });

    it('should handle already matured deposit error', async () => {
      const depositId = 'fd_matured';
      const mockError = new Error('Only active deposits can be broken');
      vi.mocked(fixedDepositAPI.breakDeposit).mockRejectedValue(mockError);

      await expect(fixedDepositAPI.breakDeposit(depositId)).rejects.toThrow(
        'Only active deposits can be broken'
      );
    });
  });

  describe('calculateInterest', () => {
    it('should calculate interest correctly', async () => {
      const amount = 100000;
      const duration = 90;

      const mockCalculation = {
        success: true,
        data: {
          principal: 100000,
          duration: 90,
          interestRate: 15.0,
          maturityAmount: 103750,
          totalInterest: 3750,
          dailyInterest: 42,
          effectiveAnnualRate: 15.39
        }
      };

      vi.mocked(fixedDepositAPI.calculateInterest).mockResolvedValue(mockCalculation);

      const result = await fixedDepositAPI.calculateInterest(amount, duration);

      expect(result).toEqual(mockCalculation);
      expect(fixedDepositAPI.calculateInterest).toHaveBeenCalledWith(amount, duration);
    });

    it('should validate calculation parameters', async () => {
      const invalidAmount = 0;
      const duration = 90;

      const mockError = new Error('Amount and duration are required');
      vi.mocked(fixedDepositAPI.calculateInterest).mockRejectedValue(mockError);

      await expect(fixedDepositAPI.calculateInterest(invalidAmount, duration)).rejects.toThrow(
        'Amount and duration are required'
      );
    });

    it('should handle different duration tiers', async () => {
      const testCases = [
        { duration: 30, expectedRate: 12.0 },
        { duration: 90, expectedRate: 15.0 },
        { duration: 180, expectedRate: 17.5 },
        { duration: 365, expectedRate: 20.0 }
      ];

      for (const testCase of testCases) {
        const mockResponse = {
          success: true,
          data: {
            principal: 100000,
            duration: testCase.duration,
            interestRate: testCase.expectedRate,
            maturityAmount: 100000 * (1 + testCase.expectedRate / 100 * testCase.duration / 365),
            totalInterest: 100000 * testCase.expectedRate / 100 * testCase.duration / 365
          }
        };

        vi.mocked(fixedDepositAPI.calculateInterest).mockResolvedValue(mockResponse);

        const result = await fixedDepositAPI.calculateInterest(100000, testCase.duration);

        expect(result.data.interestRate).toBe(testCase.expectedRate);
      }
    });
  });
});
