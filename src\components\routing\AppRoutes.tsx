
import { LoadingScreen } from "@/components/ui/loading-screen";
import { OfflineIndicator } from "@/components/ui/offline-indicator";
import { useAOS } from "@/hooks/use-aos";
import { Suspense, lazy } from "react";
import { Navigate, Route, Routes } from "react-router-dom";

// Lazy load components
const Legal = lazy(() => import("@/pages/Legal"));
const Login = lazy(() => import("@/pages/Login"));
const Signup = lazy(() => import("@/pages/Signup"));
const NotFound = lazy(() => import("@/pages/NotFound"));
const Index = lazy(() => import("@/pages/Index"));
const EnhancedLanding = lazy(() => import("@/pages/EnhancedLanding"));
const FAQ = lazy(() => import("@/pages/FAQ"));
const KojaPresentation = lazy(() => import("@/pages/KojaPresentation"));
const UserDashboard = lazy(() => import("@/pages/user/Dashboard"));
const AdminDashboard = lazy(() => import("@/pages/admin/Dashboard"));
const AdminLogin = lazy(() => import("@/pages/AdminLogin"));
const UserManagement = lazy(() => import("@/pages/admin/UserManagement"));
const UserLayout = lazy(() => import("@/components/layout/user-layout"));
const SavingsPlans = lazy(() => import("@/pages/user/SavingsPlans"));
const Payments = lazy(() => import("@/pages/user/Payments"));
const KycVerification = lazy(() => import("@/pages/user/KycVerification"));
const HelpSupport = lazy(() => import("@/pages/user/HelpSupport"));
const Settings = lazy(() => import("@/pages/user/Settings"));
const Analytics = lazy(() => import("@/pages/user/Analytics"));
const AddCard = lazy(() => import("@/pages/user/AddCard"));
const Transactions = lazy(() => import("@/pages/user/Transactions"));

// Admin routes
const OtpVerificationManagement = lazy(() => import("@/pages/admin/OtpVerificationManagement"));
const AssignPlanToUser = lazy(() => import("@/pages/admin/AssignPlanToUser"));
const AdminRolesManagement = lazy(() => import("@/pages/admin/AdminRolesManagement"));
const AdminAnalytics = lazy(() => import("@/pages/admin/Analytics"));
const UserRequests = lazy(() => import("@/pages/admin/Requests"));
const UserProfileDetails = lazy(() => import("@/pages/admin/UserProfileDetails"));
const StaffManagement = lazy(() => import("@/pages/admin/StaffManagement"));
const GroupSavingsPlans = lazy(() => import("@/pages/admin/GroupSavingsPlans"));
const NotificationManagement = lazy(() => import("@/pages/admin/NotificationManagement"));
const StaffRoleManagement = lazy(() => import("@/pages/admin/StaffRoleManagement"));
const InterestManagement = lazy(() => import("@/pages/admin/InterestManagement"));
const KycManagement = lazy(() => import("@/pages/admin/KycManagement"));
const AdsManagement = lazy(() => import("@/pages/admin/AdsManagement"));
const BillPayments = lazy(() => import("@/pages/user/BillPayments"));
const BillManagement = lazy(() => import("@/pages/admin/BillManagement"));
const BillPaymentManagement = lazy(() => import("@/pages/admin/BillPaymentManagement"));
const Investments = lazy(() => import("@/pages/user/Investments"));
const AuthCallback = lazy(() => import("@/pages/AuthCallback"));

export const AppRoutes = () => {
  useAOS(); // Initialize AOS animations
  
  return (
    <>
      <OfflineIndicator />
      <Suspense fallback={<LoadingScreen />}>
        <Routes>
          {/* Public routes */}
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<Signup />} />
          <Route path="/legal" element={<Legal />} />
          <Route path="/admin/login" element={<AdminLogin />} />
          <Route path="/koja-presentation" element={<KojaPresentation />} />
          <Route path="/auth/callback" element={<AuthCallback />} />
          <Route path="/enhanced" element={<EnhancedLanding />} />
          <Route path="/faq" element={<FAQ />} />
          <Route path="/" element={<Index />} />

          {/* User routes with auth protection */}
          <Route element={<UserLayout isAdmin={false} />}>
            <Route path="/dashboard" element={<UserDashboard />} />
            <Route path="/savings" element={<SavingsPlans />} />
            <Route path="/payments" element={<Payments />} />
            <Route path="/kyc" element={<KycVerification />} />
            <Route path="/help-support" element={<HelpSupport />} />
            <Route path="/transactions" element={<Transactions />} />
            <Route path="/analytics" element={<Analytics />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="/profile" element={<Navigate to="/settings" replace />} />
            <Route path="/add-card" element={<AddCard />} />
            <Route path="/bill-payments" element={<BillPayments />} />
            <Route path="/investments" element={<Investments />} />
          </Route>

          {/* Admin routes with auth protection */}
          <Route element={<UserLayout isAdmin={true} />}>
            <Route path="/admin/dashboard" element={<AdminDashboard />} />
            <Route path="/admin/users" element={<UserManagement />} />
            <Route path="/admin/users/:userId" element={<UserProfileDetails />} />
            <Route path="/admin/settings" element={<Settings />} />
            <Route path="/admin/requests" element={<UserRequests />} />
            <Route path="/admin/analytics" element={<AdminAnalytics />} />
            <Route path="/admin/payment-management" element={<Navigate to="/admin/requests" replace />} />
            <Route path="/admin/verification" element={<OtpVerificationManagement />} />
            <Route path="/admin/assign-plan" element={<AssignPlanToUser />} />
            <Route path="/admin/roles" element={<AdminRolesManagement />} />
            <Route path="/admin/staff" element={<StaffManagement />} />
            <Route path="/admin/group-savings" element={<GroupSavingsPlans />} />
            <Route path="/admin/notifications" element={<NotificationManagement />} />
            <Route path="/admin/ads" element={<AdsManagement />} />
            <Route path="/admin/staff-roles" element={<StaffRoleManagement />} />
            <Route path="/admin/interest-management" element={<InterestManagement />} />
            <Route path="/admin/kyc-management" element={<KycManagement />} />
            <Route path="/admin/bill-management" element={<BillManagement />} />
            <Route path="/admin/bill-payment-management" element={<BillPaymentManagement />} />
          </Route>

          {/* Catch-all route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </Suspense>
    </>
  );
};
