const cron = require('node-cron');
const FixedDeposit = require('../models/FixedDeposit');
const SavingsPlan = require('../models/SavingsPlan');
const User = require('../models/User');
const mongoose = require('mongoose');

class InterestCalculationService {
  constructor() {
    this.isRunning = false;
    this.lastRun = null;
  }

  // Initialize the service and start cron jobs
  initialize() {
    console.log('🏦 Interest Calculation Service initialized');
    
    // Run daily at midnight
    cron.schedule('0 0 * * *', () => {
      this.runDailyInterestCalculation();
    });

    // Run every hour to check for matured deposits
    cron.schedule('0 * * * *', () => {
      this.processMaturedDeposits();
    });

    console.log('📅 Interest calculation cron jobs scheduled');
  }

  // Main daily interest calculation
  async runDailyInterestCalculation() {
    if (this.isRunning) {
      console.log('⏳ Interest calculation already running, skipping...');
      return;
    }

    this.isRunning = true;
    const startTime = new Date();
    
    try {
      console.log('🚀 Starting daily interest calculation...');
      
      const results = {
        fixedDeposits: await this.calculateFixedDepositInterest(),
        flexSavings: await this.calculateFlexSavingsInterest(),
        targetSavings: await this.calculateTargetSavingsInterest(),
        errors: []
      };

      this.lastRun = new Date();
      
      console.log('✅ Daily interest calculation completed:', {
        duration: `${Date.now() - startTime}ms`,
        results
      });

      return results;
    } catch (error) {
      console.error('❌ Error in daily interest calculation:', error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  // Calculate interest for fixed deposits
  async calculateFixedDepositInterest() {
    try {
      const activeDeposits = await FixedDeposit.find({ status: 'active' });
      let processed = 0;
      let totalInterest = 0;

      for (const deposit of activeDeposits) {
        try {
          const currentInterest = deposit.calculateCurrentInterest();
          deposit.accruedInterest = currentInterest;
          await deposit.save();
          
          processed++;
          totalInterest += currentInterest;
        } catch (error) {
          console.error(`Error calculating interest for deposit ${deposit._id}:`, error);
        }
      }

      return {
        processed,
        totalInterest: Math.round(totalInterest),
        count: activeDeposits.length
      };
    } catch (error) {
      console.error('Error in fixed deposit interest calculation:', error);
      throw error;
    }
  }

  // Calculate interest for flex savings accounts
  async calculateFlexSavingsInterest() {
    try {
      // This would be implemented when FlexSavings model is created
      // For now, return placeholder
      return {
        processed: 0,
        totalInterest: 0,
        count: 0
      };
    } catch (error) {
      console.error('Error in flex savings interest calculation:', error);
      throw error;
    }
  }

  // Calculate interest for target savings
  async calculateTargetSavingsInterest() {
    try {
      const activePlans = await SavingsPlan.find({ 
        status: 'active',
        interestRate: { $gt: 0 }
      });

      let processed = 0;
      let totalInterest = 0;

      for (const plan of activePlans) {
        try {
          if (plan.currentAmount > 0) {
            const dailyRate = (plan.interestRate || 5) / 100 / 365;
            const dailyInterest = plan.currentAmount * dailyRate;
            
            plan.currentAmount += dailyInterest;
            await plan.save();
            
            processed++;
            totalInterest += dailyInterest;
          }
        } catch (error) {
          console.error(`Error calculating interest for plan ${plan._id}:`, error);
        }
      }

      return {
        processed,
        totalInterest: Math.round(totalInterest),
        count: activePlans.length
      };
    } catch (error) {
      console.error('Error in target savings interest calculation:', error);
      throw error;
    }
  }

  // Process matured fixed deposits
  async processMaturedDeposits() {
    try {
      const maturedDeposits = await FixedDeposit.find({
        status: 'active',
        maturityDate: { $lte: new Date() }
      });

      let processed = 0;
      let totalMatured = 0;

      for (const deposit of maturedDeposits) {
        try {
          const session = await mongoose.startSession();
          await session.withTransaction(async () => {
            // Mature the deposit
            await deposit.mature();
            
            // Credit user account
            const user = await User.findById(deposit.userId);
            if (user) {
              user.balance += deposit.finalAmount;
              await user.save();
              
              // TODO: Send notification to user
              console.log(`💰 Matured deposit ${deposit._id} credited ₦${deposit.finalAmount} to user ${user.email}`);
            }
          });
          
          processed++;
          totalMatured += deposit.finalAmount;
        } catch (error) {
          console.error(`Error processing matured deposit ${deposit._id}:`, error);
        }
      }

      if (processed > 0) {
        console.log(`🎯 Processed ${processed} matured deposits, total: ₦${totalMatured}`);
      }

      return {
        processed,
        totalMatured,
        count: maturedDeposits.length
      };
    } catch (error) {
      console.error('Error processing matured deposits:', error);
      throw error;
    }
  }

  // Manual trigger for interest calculation (for testing/admin)
  async triggerManualCalculation() {
    console.log('🔧 Manual interest calculation triggered');
    return await this.runDailyInterestCalculation();
  }

  // Get calculation status
  getStatus() {
    return {
      isRunning: this.isRunning,
      lastRun: this.lastRun,
      nextRun: this.getNextScheduledRun()
    };
  }

  // Get next scheduled run time
  getNextScheduledRun() {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    return tomorrow;
  }

  // Calculate projected returns for a given amount and duration
  static calculateProjectedReturns(principal, annualRate, days, compoundingFrequency = 'daily') {
    const rate = annualRate / 100;
    let compoundingPeriods;
    
    switch (compoundingFrequency) {
      case 'daily':
        compoundingPeriods = 365;
        break;
      case 'monthly':
        compoundingPeriods = 12;
        break;
      case 'quarterly':
        compoundingPeriods = 4;
        break;
      case 'annually':
        compoundingPeriods = 1;
        break;
      default:
        compoundingPeriods = 365;
    }

    const periodicRate = rate / compoundingPeriods;
    const totalPeriods = (days / 365) * compoundingPeriods;
    
    const finalAmount = principal * Math.pow(1 + periodicRate, totalPeriods);
    const totalInterest = finalAmount - principal;
    
    return {
      principal,
      finalAmount: Math.round(finalAmount),
      totalInterest: Math.round(totalInterest),
      effectiveAnnualRate: ((finalAmount / principal) ** (365 / days) - 1) * 100,
      dailyInterest: Math.round(totalInterest / days)
    };
  }

  // Get interest calculation history
  async getCalculationHistory(days = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      // This would typically come from a calculation log collection
      // For now, return mock data
      return {
        period: `${days} days`,
        totalCalculations: days,
        totalInterestDistributed: 0,
        averageDailyInterest: 0,
        lastCalculation: this.lastRun
      };
    } catch (error) {
      console.error('Error fetching calculation history:', error);
      throw error;
    }
  }
}

// Export singleton instance
const interestCalculationService = new InterestCalculationService();

module.exports = {
  InterestCalculationService,
  interestCalculationService
};
