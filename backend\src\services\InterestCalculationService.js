const cron = require('node-cron');
const FixedDeposit = require('../models/FixedDeposit');
const SavingsPlan = require('../models/SavingsPlan');
const GroupSavingsPlan = require('../models/GroupSavingsPlan');
const Transaction = require('../models/Transaction');
const User = require('../models/User');
const mongoose = require('mongoose');
const { sendEmail } = require('./emailService');

class InterestCalculationService {
  constructor() {
    this.isRunning = false;
    this.lastRun = null;
  }

  // Initialize the service and start cron jobs
  initialize() {
    console.log('🏦 Interest Calculation Service initialized');

    // Run daily at midnight for interest calculation
    cron.schedule('0 0 * * *', () => {
      this.runDailyInterestCalculation();
    });

    // Run every hour to check for matured deposits
    cron.schedule('0 * * * *', () => {
      this.processMaturedDeposits();
    });

    // Run daily at 6 AM to process group savings
    cron.schedule('0 6 * * *', () => {
      this.processGroupSavingsContributions();
    });

    // Run weekly on Sundays at 8 AM for weekly reports
    cron.schedule('0 8 * * 0', () => {
      this.generateWeeklyReports();
    });

    // Run monthly on 1st at 9 AM for monthly processing
    cron.schedule('0 9 1 * *', () => {
      this.processMonthlyTasks();
    });

    console.log('📅 Interest calculation cron jobs scheduled');
  }

  // Main daily interest calculation
  async runDailyInterestCalculation() {
    if (this.isRunning) {
      console.log('⏳ Interest calculation already running, skipping...');
      return;
    }

    this.isRunning = true;
    const startTime = new Date();
    
    try {
      console.log('🚀 Starting daily interest calculation...');
      
      const results = {
        fixedDeposits: await this.calculateFixedDepositInterest(),
        flexSavings: await this.calculateFlexSavingsInterest(),
        targetSavings: await this.calculateTargetSavingsInterest(),
        errors: []
      };

      this.lastRun = new Date();
      
      console.log('✅ Daily interest calculation completed:', {
        duration: `${Date.now() - startTime}ms`,
        results
      });

      return results;
    } catch (error) {
      console.error('❌ Error in daily interest calculation:', error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  // Calculate interest for fixed deposits
  async calculateFixedDepositInterest() {
    try {
      const activeDeposits = await FixedDeposit.find({ status: 'active' });
      let processed = 0;
      let totalInterest = 0;

      for (const deposit of activeDeposits) {
        try {
          const currentInterest = deposit.calculateCurrentInterest();
          deposit.accruedInterest = currentInterest;
          await deposit.save();
          
          processed++;
          totalInterest += currentInterest;
        } catch (error) {
          console.error(`Error calculating interest for deposit ${deposit._id}:`, error);
        }
      }

      return {
        processed,
        totalInterest: Math.round(totalInterest),
        count: activeDeposits.length
      };
    } catch (error) {
      console.error('Error in fixed deposit interest calculation:', error);
      throw error;
    }
  }

  // Calculate interest for flex savings accounts
  async calculateFlexSavingsInterest() {
    try {
      // This would be implemented when FlexSavings model is created
      // For now, return placeholder
      return {
        processed: 0,
        totalInterest: 0,
        count: 0
      };
    } catch (error) {
      console.error('Error in flex savings interest calculation:', error);
      throw error;
    }
  }

  // Calculate interest for target savings
  async calculateTargetSavingsInterest() {
    try {
      const activePlans = await SavingsPlan.find({ 
        status: 'active',
        interestRate: { $gt: 0 }
      });

      let processed = 0;
      let totalInterest = 0;

      for (const plan of activePlans) {
        try {
          if (plan.currentAmount > 0) {
            const dailyRate = (plan.interestRate || 5) / 100 / 365;
            const dailyInterest = plan.currentAmount * dailyRate;
            
            plan.currentAmount += dailyInterest;
            await plan.save();
            
            processed++;
            totalInterest += dailyInterest;
          }
        } catch (error) {
          console.error(`Error calculating interest for plan ${plan._id}:`, error);
        }
      }

      return {
        processed,
        totalInterest: Math.round(totalInterest),
        count: activePlans.length
      };
    } catch (error) {
      console.error('Error in target savings interest calculation:', error);
      throw error;
    }
  }

  // Process matured fixed deposits
  async processMaturedDeposits() {
    try {
      const maturedDeposits = await FixedDeposit.find({
        status: 'active',
        maturityDate: { $lte: new Date() }
      });

      let processed = 0;
      let totalMatured = 0;

      for (const deposit of maturedDeposits) {
        try {
          const session = await mongoose.startSession();
          await session.withTransaction(async () => {
            // Mature the deposit
            await deposit.mature();
            
            // Credit user account
            const user = await User.findById(deposit.userId);
            if (user) {
              user.balance += deposit.finalAmount;
              await user.save();
              
              // TODO: Send notification to user
              console.log(`💰 Matured deposit ${deposit._id} credited ₦${deposit.finalAmount} to user ${user.email}`);
            }
          });
          
          processed++;
          totalMatured += deposit.finalAmount;
        } catch (error) {
          console.error(`Error processing matured deposit ${deposit._id}:`, error);
        }
      }

      if (processed > 0) {
        console.log(`🎯 Processed ${processed} matured deposits, total: ₦${totalMatured}`);
      }

      return {
        processed,
        totalMatured,
        count: maturedDeposits.length
      };
    } catch (error) {
      console.error('Error processing matured deposits:', error);
      throw error;
    }
  }

  // Manual trigger for interest calculation (for testing/admin)
  async triggerManualCalculation() {
    console.log('🔧 Manual interest calculation triggered');
    return await this.runDailyInterestCalculation();
  }

  // Get calculation status
  getStatus() {
    return {
      isRunning: this.isRunning,
      lastRun: this.lastRun,
      nextRun: this.getNextScheduledRun()
    };
  }

  // Get next scheduled run time
  getNextScheduledRun() {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    return tomorrow;
  }

  // Calculate projected returns for a given amount and duration
  static calculateProjectedReturns(principal, annualRate, days, compoundingFrequency = 'daily') {
    const rate = annualRate / 100;
    let compoundingPeriods;
    
    switch (compoundingFrequency) {
      case 'daily':
        compoundingPeriods = 365;
        break;
      case 'monthly':
        compoundingPeriods = 12;
        break;
      case 'quarterly':
        compoundingPeriods = 4;
        break;
      case 'annually':
        compoundingPeriods = 1;
        break;
      default:
        compoundingPeriods = 365;
    }

    const periodicRate = rate / compoundingPeriods;
    const totalPeriods = (days / 365) * compoundingPeriods;
    
    const finalAmount = principal * Math.pow(1 + periodicRate, totalPeriods);
    const totalInterest = finalAmount - principal;
    
    return {
      principal,
      finalAmount: Math.round(finalAmount),
      totalInterest: Math.round(totalInterest),
      effectiveAnnualRate: ((finalAmount / principal) ** (365 / days) - 1) * 100,
      dailyInterest: Math.round(totalInterest / days)
    };
  }

  // Get interest calculation history
  async getCalculationHistory(days = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      // Get actual calculation data from transactions
      const interestTransactions = await Transaction.find({
        type: 'interest',
        createdAt: { $gte: startDate },
        status: 'completed'
      });

      const totalInterestDistributed = interestTransactions.reduce((sum, txn) => sum + txn.amount, 0);
      const averageDailyInterest = totalInterestDistributed / days;

      return {
        period: `${days} days`,
        totalCalculations: interestTransactions.length,
        totalInterestDistributed,
        averageDailyInterest,
        lastCalculation: this.lastRun
      };
    } catch (error) {
      console.error('Error fetching calculation history:', error);
      throw error;
    }
  }

  // Process group savings contributions
  async processGroupSavingsContributions() {
    if (this.isRunning) return;

    this.isRunning = true;
    console.log('🏦 Processing group savings contributions...');

    try {
      const activeGroups = await GroupSavingsPlan.find({
        status: 'active',
        nextContributionDate: { $lte: new Date() }
      }).populate('members.userId');

      for (const group of activeGroups) {
        // Calculate interest for group savings
        const dailyRate = (group.interestRate || 0) / 365 / 100;
        const interest = group.currentAmount * dailyRate;

        if (interest > 0) {
          group.currentAmount += interest;
          await group.save();

          // Create interest transaction
          await Transaction.create({
            userId: group.adminId,
            type: 'interest',
            amount: interest,
            description: `Group savings interest for ${group.name}`,
            category: 'savings',
            relatedId: group._id,
            relatedModel: 'GroupSavingsPlan',
            status: 'completed'
          });
        }

        // Update next contribution date
        const nextDate = new Date(group.nextContributionDate);
        switch (group.frequency) {
          case 'daily':
            nextDate.setDate(nextDate.getDate() + 1);
            break;
          case 'weekly':
            nextDate.setDate(nextDate.getDate() + 7);
            break;
          case 'monthly':
            nextDate.setMonth(nextDate.getMonth() + 1);
            break;
        }

        group.nextContributionDate = nextDate;
        await group.save();
      }

      console.log(`✅ Processed ${activeGroups.length} group savings plans`);
    } catch (error) {
      console.error('❌ Error processing group savings:', error);
    } finally {
      this.isRunning = false;
      this.lastRun = new Date();
    }
  }

  // Generate weekly reports
  async generateWeeklyReports() {
    console.log('📊 Generating weekly reports...');

    try {
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);

      // Get weekly statistics
      const weeklyStats = await this.getWeeklyStatistics(weekAgo);

      // Send reports to admins
      const adminUsers = await User.find({ role: 'admin' });

      for (const admin of adminUsers) {
        try {
          await sendEmail({
            to: admin.email,
            subject: 'Better Interest - Weekly Report',
            template: 'weekly-report',
            data: {
              adminName: admin.firstName,
              stats: weeklyStats,
              period: {
                start: weekAgo.toDateString(),
                end: new Date().toDateString()
              }
            }
          });
        } catch (emailError) {
          console.error(`Failed to send weekly report to ${admin.email}:`, emailError);
        }
      }

      console.log('✅ Weekly reports generated and sent');
    } catch (error) {
      console.error('❌ Error generating weekly reports:', error);
    }
  }

  // Process monthly tasks
  async processMonthlyTasks() {
    console.log('📅 Processing monthly tasks...');

    try {
      // Archive completed savings plans
      await this.archiveCompletedPlans();

      // Generate monthly statements
      await this.generateMonthlyStatements();

      // Update user credit scores
      await this.updateUserCreditScores();

      console.log('✅ Monthly tasks completed');
    } catch (error) {
      console.error('❌ Error processing monthly tasks:', error);
    }
  }

  // Helper method to get weekly statistics
  async getWeeklyStatistics(startDate) {
    const endDate = new Date();

    try {
      const [
        newUsers,
        totalDeposits,
        totalWithdrawals,
        activeSavingsPlans,
        completedPlans
      ] = await Promise.all([
        User.countDocuments({ createdAt: { $gte: startDate, $lte: endDate } }),
        Transaction.aggregate([
          { $match: { type: 'deposit', createdAt: { $gte: startDate, $lte: endDate }, status: 'completed' } },
          { $group: { _id: null, total: { $sum: '$amount' } } }
        ]),
        Transaction.aggregate([
          { $match: { type: 'withdrawal', createdAt: { $gte: startDate, $lte: endDate }, status: 'completed' } },
          { $group: { _id: null, total: { $sum: '$amount' } } }
        ]),
        SavingsPlan.countDocuments({ status: 'active' }),
        SavingsPlan.countDocuments({
          status: 'completed',
          updatedAt: { $gte: startDate, $lte: endDate }
        })
      ]);

      return {
        newUsers,
        totalDeposits: totalDeposits[0]?.total || 0,
        totalWithdrawals: totalWithdrawals[0]?.total || 0,
        activeSavingsPlans,
        completedPlans
      };
    } catch (error) {
      console.error('Error getting weekly statistics:', error);
      return {
        newUsers: 0,
        totalDeposits: 0,
        totalWithdrawals: 0,
        activeSavingsPlans: 0,
        completedPlans: 0
      };
    }
  }
}

// Export singleton instance
const interestCalculationService = new InterestCalculationService();

module.exports = {
  InterestCalculationService,
  interestCalculationService
};
