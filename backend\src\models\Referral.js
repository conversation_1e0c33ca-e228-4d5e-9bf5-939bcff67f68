const mongoose = require('mongoose');

const referralSchema = new mongoose.Schema({
  referrerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  referredUserId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  referralCode: {
    type: String,
    required: true,
    uppercase: true
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'cancelled'],
    default: 'pending'
  },
  rewardAmount: {
    type: Number,
    default: 0
  },
  rewardCurrency: {
    type: String,
    default: 'NGN'
  },
  rewardPaid: {
    type: Boolean,
    default: false
  },
  rewardPaidAt: {
    type: Date
  },
  metadata: {
    referralSource: String, // 'link', 'code', 'social'
    conversionDate: Date,
    lifetime_value: Number,
    tier: {
      type: String,
      enum: ['bronze', 'silver', 'gold', 'platinum'],
      default: 'bronze'
    }
  },
  milestones: [{
    name: String,
    achieved: { type: Boolean, default: false },
    achievedAt: Date,
    rewardAmount: Number
  }]
}, {
  timestamps: true
});

// Compound index to prevent duplicate referrals
referralSchema.index({ referrerId: 1, referredUserId: 1 }, { unique: true });

// Pre-save middleware to calculate reward
referralSchema.pre('save', function(next) {
  if (this.status === 'completed' && !this.rewardAmount) {
    // Base reward calculation
    this.rewardAmount = this.calculateReward();
  }
  next();
});

// Instance method to calculate reward based on tier
referralSchema.methods.calculateReward = function() {
  const rewardTiers = {
    bronze: 1000,   // ₦1,000
    silver: 2500,   // ₦2,500  
    gold: 5000,     // ₦5,000
    platinum: 10000 // ₦10,000
  };
  
  return rewardTiers[this.metadata.tier] || rewardTiers.bronze;
};

// Instance method to complete referral
referralSchema.methods.complete = async function() {
  this.status = 'completed';
  this.metadata.conversionDate = new Date();
  this.rewardAmount = this.calculateReward();
  
  await this.save();
  
  // Update referrer's stats
  await mongoose.model('User').findByIdAndUpdate(this.referrerId, {
    $inc: { 
      totalReferrals: 1,
      referralEarnings: this.rewardAmount
    }
  });
  
  return this;
};

// Static method to get referral stats
referralSchema.statics.getReferralStats = function(userId) {
  return this.aggregate([
    { $match: { referrerId: mongoose.Types.ObjectId(userId) } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalReward: { $sum: '$rewardAmount' }
      }
    }
  ]);
};

// Static method to get top referrers
referralSchema.statics.getTopReferrers = function(limit = 10) {
  return this.aggregate([
    { $match: { status: 'completed' } },
    {
      $group: {
        _id: '$referrerId',
        totalReferrals: { $sum: 1 },
        totalEarnings: { $sum: '$rewardAmount' }
      }
    },
    { $sort: { totalReferrals: -1 } },
    { $limit: limit },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'user'
      }
    },
    { $unwind: '$user' }
  ]);
};

module.exports = mongoose.model('Referral', referralSchema);