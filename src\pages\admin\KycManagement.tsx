import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Search, 
  Filter, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Eye, 
  Users, 
  UserCheck, 
  UserX,
  Download,
  AlertTriangle
} from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { <PERSON>, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

const KycManagement = () => {
  const [users, setUsers] = useState([
    {
      id: 1,
      name: "<PERSON> Doe",
      email: "<EMAIL>",
      phone: "+234 ************",
      kycStatus: "verified",
      dateSubmitted: "2024-01-15",
      dateVerified: "2024-01-16",
      documents: {
        idCard: "uploaded",
        proofOfAddress: "uploaded",
        selfie: "uploaded"
      },
      verificationNotes: "All documents verified successfully"
    },
    {
      id: 2,
      name: "Jane Smith",
      email: "<EMAIL>",
      phone: "+234 ************",
      kycStatus: "pending",
      dateSubmitted: "2024-01-20",
      dateVerified: null,
      documents: {
        idCard: "uploaded",
        proofOfAddress: "uploaded",
        selfie: "pending"
      },
      verificationNotes: ""
    },
    {
      id: 3,
      name: "Mike Johnson",
      email: "<EMAIL>",
      phone: "+234 ************",
      kycStatus: "rejected",
      dateSubmitted: "2024-01-18",
      dateVerified: "2024-01-19",
      documents: {
        idCard: "uploaded",
        proofOfAddress: "rejected",
        selfie: "uploaded"
      },
      verificationNotes: "Proof of address document is unclear"
    },
    {
      id: 4,
      name: "Sarah Wilson",
      email: "<EMAIL>",
      phone: "+234 ************",
      kycStatus: "not_submitted",
      dateSubmitted: null,
      dateVerified: null,
      documents: {
        idCard: "not_uploaded",
        proofOfAddress: "not_uploaded",
        selfie: "not_uploaded"
      },
      verificationNotes: ""
    }
  ]);

  const [filteredUsers, setFilteredUsers] = useState(users);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedUser, setSelectedUser] = useState(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Filter users based on search term and status
  useEffect(() => {
    let filtered = users;

    if (searchTerm) {
      filtered = filtered.filter(user => 
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.phone.includes(searchTerm)
      );
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter(user => user.kycStatus === statusFilter);
    }

    setFilteredUsers(filtered);
  }, [searchTerm, statusFilter, users]);

  const getStatusBadge = (status) => {
    switch (status) {
      case "verified":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100"><CheckCircle className="h-3 w-3 mr-1" />Verified</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100"><Clock className="h-3 w-3 mr-1" />Pending</Badge>;
      case "rejected":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100"><XCircle className="h-3 w-3 mr-1" />Rejected</Badge>;
      case "not_submitted":
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100"><AlertTriangle className="h-3 w-3 mr-1" />Not Submitted</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getDocumentStatus = (status) => {
    switch (status) {
      case "uploaded":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Uploaded</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>;
      case "rejected":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Rejected</Badge>;
      case "not_uploaded":
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Not Uploaded</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const handleVerifyUser = (userId, status, notes = "") => {
    setUsers(users.map(user => 
      user.id === userId 
        ? { 
            ...user, 
            kycStatus: status, 
            dateVerified: new Date().toISOString().split('T')[0],
            verificationNotes: notes 
          }
        : user
    ));
    
    const statusText = status === "verified" ? "verified" : "rejected";
    toast.success(`User KYC ${statusText} successfully`);
    setIsDialogOpen(false);
  };

  const stats = {
    total: users.length,
    verified: users.filter(u => u.kycStatus === "verified").length,
    pending: users.filter(u => u.kycStatus === "pending").length,
    rejected: users.filter(u => u.kycStatus === "rejected").length,
    notSubmitted: users.filter(u => u.kycStatus === "not_submitted").length
  };

  const KycDetailsModal = ({ user }) => {
    const [verificationNotes, setVerificationNotes] = useState(user?.verificationNotes || "");

    if (!user) return null;

    return (
      <div className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label>Full Name</Label>
            <p className="font-medium">{user.name}</p>
          </div>
          <div>
            <Label>Email</Label>
            <p className="font-medium">{user.email}</p>
          </div>
          <div>
            <Label>Phone</Label>
            <p className="font-medium">{user.phone}</p>
          </div>
          <div>
            <Label>Current Status</Label>
            <div className="mt-1">{getStatusBadge(user.kycStatus)}</div>
          </div>
        </div>

        <div>
          <Label className="text-base font-semibold">Documents Status</Label>
          <div className="grid grid-cols-1 gap-3 mt-2">
            <div className="flex justify-between items-center p-3 border rounded-lg">
              <span>ID Card/Passport</span>
              {getDocumentStatus(user.documents.idCard)}
            </div>
            <div className="flex justify-between items-center p-3 border rounded-lg">
              <span>Proof of Address</span>
              {getDocumentStatus(user.documents.proofOfAddress)}
            </div>
            <div className="flex justify-between items-center p-3 border rounded-lg">
              <span>Selfie Verification</span>
              {getDocumentStatus(user.documents.selfie)}
            </div>
          </div>
        </div>

        <div>
          <Label htmlFor="notes">Verification Notes</Label>
          <Textarea
            id="notes"
            value={verificationNotes}
            onChange={(e) => setVerificationNotes(e.target.value)}
            placeholder="Add notes about the verification..."
            className="mt-1"
          />
        </div>

        {user.kycStatus === "pending" && (
          <div className="flex space-x-3">
            <Button 
              onClick={() => handleVerifyUser(user.id, "verified", verificationNotes)}
              className="flex-1 bg-green-600 hover:bg-green-700"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Approve KYC
            </Button>
            <Button 
              onClick={() => handleVerifyUser(user.id, "rejected", verificationNotes)}
              variant="destructive"
              className="flex-1"
            >
              <XCircle className="h-4 w-4 mr-2" />
              Reject KYC
            </Button>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">KYC Management</h1>
        <Button variant="outline" className="gap-2">
          <Download className="h-4 w-4" />
          Export Data
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="border-2 border-green-600/20 dark:border-green-400/20 rounded-none">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-2xl font-bold">{stats.total}</p>
                <p className="text-sm text-muted-foreground">Total Users</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="border-2 border-green-600/20 dark:border-green-400/20 rounded-none">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <UserCheck className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-2xl font-bold text-green-600">{stats.verified}</p>
                <p className="text-sm text-muted-foreground">Verified</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="border-2 border-green-600/20 dark:border-green-400/20 rounded-none">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
                <p className="text-sm text-muted-foreground">Pending</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="border-2 border-green-600/20 dark:border-green-400/20 rounded-none">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <UserX className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-2xl font-bold text-red-600">{stats.rejected}</p>
                <p className="text-sm text-muted-foreground">Rejected</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="border-2 border-green-600/20 dark:border-green-400/20 rounded-none">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-gray-600" />
              <div>
                <p className="text-2xl font-bold text-gray-600">{stats.notSubmitted}</p>
                <p className="text-sm text-muted-foreground">Not Submitted</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="border-2 border-green-600/20 dark:border-green-400/20 rounded-none">
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by name, email, or phone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-full md:w-48">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="verified">Verified</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="not_submitted">Not Submitted</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card className="border-2 border-green-600/20 dark:border-green-400/20 rounded-none">
        <CardHeader>
          <CardTitle>KYC Submissions</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>KYC Status</TableHead>
                <TableHead>Date Submitted</TableHead>
                <TableHead>Date Verified</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={`https://avatar.vercel.sh/${user.email}`} />
                        <AvatarFallback>{user.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{user.name}</p>
                        <p className="text-sm text-muted-foreground">{user.email}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{user.phone}</TableCell>
                  <TableCell>{getStatusBadge(user.kycStatus)}</TableCell>
                  <TableCell>{user.dateSubmitted || "—"}</TableCell>
                  <TableCell>{user.dateVerified || "—"}</TableCell>
                  <TableCell>
                    <Dialog open={isDialogOpen && selectedUser?.id === user.id} onOpenChange={setIsDialogOpen}>
                      <DialogTrigger asChild>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setSelectedUser(user)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>KYC Details - {user.name}</DialogTitle>
                        </DialogHeader>
                        <KycDetailsModal user={selectedUser} />
                      </DialogContent>
                    </Dialog>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default KycManagement;