import { useTheme } from '@/components/theme-provider';
import { Button } from '@/components/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
    NavigationMenu,
    NavigationMenuContent,
    NavigationMenuItem,
    NavigationMenuLink,
    NavigationMenuList,
    NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import { AnimatePresence, motion } from 'framer-motion';
import {
    BookOpen,
    Calculator,
    FileText,
    HelpCircle,
    Menu,
    MessageCircle,
    Monitor,
    Moon,
    Phone,
    PiggyBank,
    Shield,
    Sun,
    TrendingUp,
    X
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { theme, setTheme, actualTheme } = useTheme();
  const navigate = useNavigate();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const bankingOptions = [
    { 
      title: 'Dashboard', 
      description: 'Overview of your finances',
      icon: TrendingUp,
      href: '/dashboard'
    },
    { 
      title: 'Savings Plans', 
      description: 'Create and manage savings goals',
      icon: PiggyBank,
      href: '/savings'
    },
    { 
      title: 'Fixed Deposits', 
      description: 'High-yield fixed deposits',
      icon: TrendingUp,
      href: '/fixed-deposits'
    },
    { 
      title: 'Investments', 
      description: 'Grow your wealth with investments',
      icon: TrendingUp,
      href: '/investments'
    },
  ];

  const resources = [
    { 
      title: 'Blog', 
      description: 'Financial tips and insights',
      icon: BookOpen,
      href: '/blog'
    },
    { 
      title: 'Savings Calculator', 
      description: 'Plan your financial goals',
      icon: Calculator,
      href: '/calculator'
    },
    { 
      title: 'Financial Tips', 
      description: 'Expert advice and guides',
      icon: BookOpen,
      href: '/tips'
    },
  ];

  const support = [
    { 
      title: 'Help Center', 
      description: 'Find answers to common questions',
      icon: HelpCircle,
      href: '/help'
    },
    { 
      title: 'Contact Us', 
      description: 'Get in touch with our team',
      icon: Phone,
      href: '/contact'
    },
    { 
      title: 'Live Chat', 
      description: 'Chat with our support team',
      icon: MessageCircle,
      href: '/chat'
    },
  ];

  const legal = [
    { 
      title: 'Privacy Policy', 
      description: 'How we protect your data',
      icon: Shield,
      href: '/privacy'
    },
    { 
      title: 'Terms of Service', 
      description: 'Our terms and conditions',
      icon: FileText,
      href: '/terms'
    },
  ];

  const ThemeToggle = () => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="w-9 px-0">
          <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => setTheme("light")}>
          <Sun className="mr-2 h-4 w-4" />
          <span>Light</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("dark")}>
          <Moon className="mr-2 h-4 w-4" />
          <span>Dark</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("system")}>
          <Monitor className="mr-2 h-4 w-4" />
          <span>System</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  const NavDropdown = ({ title, items, trigger }: any) => (
    <NavigationMenuItem>
      <NavigationMenuTrigger className="h-9 px-4 py-2">
        {title}
      </NavigationMenuTrigger>
      <NavigationMenuContent>
        <div className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
          {items.map((item: any) => (
            <NavigationMenuLink key={item.href} asChild>
              <Link
                to={item.href}
                className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
              >
                <div className="flex items-center space-x-2">
                  <item.icon className="h-4 w-4" />
                  <div className="text-sm font-medium leading-none">{item.title}</div>
                </div>
                <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                  {item.description}
                </p>
              </Link>
            </NavigationMenuLink>
          ))}
        </div>
      </NavigationMenuContent>
    </NavigationMenuItem>
  );

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled 
          ? 'bg-background/80 backdrop-blur-md border-b shadow-sm' 
          : 'bg-transparent'
      }`}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-logo-green rounded-lg flex items-center justify-center border border-logo-yellow-orange">
              <PiggyBank className="h-5 w-5 text-white" />
            </div>
            <span className="text-xl font-bold text-foreground">Better Interest</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-1">
            <NavigationMenu>
              <NavigationMenuList>
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link to="/about" className="h-9 px-4 py-2 text-sm font-medium transition-colors hover:text-primary">
                      About
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>
                
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link to="/features" className="h-9 px-4 py-2 text-sm font-medium transition-colors hover:text-primary">
                      Features
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link to="/plans" className="h-9 px-4 py-2 text-sm font-medium transition-colors hover:text-primary">
                      Plans
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>

                <NavDropdown title="Banking" items={bankingOptions} />
                <NavDropdown title="Resources" items={resources} />
                <NavDropdown title="Support" items={support} />
                <NavDropdown title="Legal" items={legal} />
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-2">
            <ThemeToggle />
            
            <div className="hidden md:flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/login')}
                className="text-logo-yellow-orange hover:text-logo-yellow-orange-light hover:bg-logo-yellow-orange/10"
              >
                Login
              </Button>
              <button
                onClick={() => navigate('/signup')}
                className="btn-neumorphic-inward px-4 py-2 text-sm"
              >
                Get Started
              </button>
            </div>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="lg:hidden border-t bg-background/95 backdrop-blur-sm"
            >
              <div className="py-4 space-y-2">
                <Link to="/about" className="block px-4 py-2 text-sm hover:bg-accent rounded-md">
                  About
                </Link>
                <Link to="/features" className="block px-4 py-2 text-sm hover:bg-accent rounded-md">
                  Features
                </Link>
                <Link to="/plans" className="block px-4 py-2 text-sm hover:bg-accent rounded-md">
                  Plans
                </Link>
                
                <div className="px-4 py-2">
                  <div className="text-sm font-medium text-muted-foreground mb-2">Banking</div>
                  {bankingOptions.map((item) => (
                    <Link key={item.href} to={item.href} className="block py-1 text-sm hover:text-primary">
                      {item.title}
                    </Link>
                  ))}
                </div>

                <div className="px-4 py-2">
                  <div className="text-sm font-medium text-muted-foreground mb-2">Support</div>
                  {support.map((item) => (
                    <Link key={item.href} to={item.href} className="block py-1 text-sm hover:text-primary">
                      {item.title}
                    </Link>
                  ))}
                </div>

                <div className="flex flex-col space-y-2 px-4 pt-4 border-t">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigate('/login')}
                    className="text-logo-yellow-orange hover:text-logo-yellow-orange-light hover:bg-logo-yellow-orange/10"
                  >
                    Login
                  </Button>
                  <button
                    onClick={() => navigate('/signup')}
                    className="btn-neumorphic-inward px-4 py-2 text-sm w-full"
                  >
                    Get Started
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.header>
  );
};

export default Header;
