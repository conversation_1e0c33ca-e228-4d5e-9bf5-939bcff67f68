import Header from '@/components/landing/Header';
import HeroSection from '@/components/landing/HeroSection';
import FeaturesSection from '@/components/landing/FeaturesSection';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import AOS from 'aos';
import 'aos/dist/aos.css';
import { motion } from 'framer-motion';
import {
    ArrowRight,
    Award,
    Play,
    Shield,
    Star,
    TrendingUp,
    Users
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';

const EnhancedLanding = () => {
  const navigate = useNavigate();

  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: 'ease-out-cubic',
    });
  }, []);

  const [testimonials, setTestimonials] = useState<any[]>([]);
  const [stats, setStats] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchLandingData = async () => {
      try {
        // Fetch testimonials from API
        const testimonialsResponse = await fetch('/api/v1/testimonials/featured');
        if (testimonialsResponse.ok) {
          const testimonialsData = await testimonialsResponse.json();
          setTestimonials(testimonialsData.data || []);
        }

        // Fetch platform stats from API
        const statsResponse = await fetch('/api/v1/analytics/platform-stats');
        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          setStats([
            { value: statsData.data.activeUsers || '0', label: 'Active Users', icon: Users },
            { value: statsData.data.totalSavings || '₦0', label: 'Total Savings', icon: TrendingUp },
            { value: statsData.data.maxReturns || '0%', label: 'Max Returns', icon: Award },
            { value: statsData.data.uptime || '0%', label: 'Uptime', icon: Shield },
          ]);
        }
      } catch (error) {
        console.error('Error fetching landing page data:', error);
        // Set empty arrays on error
        setTestimonials([]);
        setStats([]);
      } finally {
        setLoading(false);
      }
    };

    fetchLandingData();
  }, []);

  return (
    <>
      <Helmet>
        <title>Better Interest - Smart Savings Platform | Grow Your Wealth in Nigeria</title>
        <meta name="description" content="Transform your financial future with Better Interest. Earn up to 20% annual returns on fixed deposits, automate your savings, and achieve your financial goals faster. Join 50,000+ Nigerians building wealth." />
        <meta name="keywords" content="savings, investment, fixed deposits, Nigeria, fintech, financial planning, wealth building, high yield savings, automated savings, group savings" />
        <meta property="og:title" content="Better Interest - Smart Savings Platform | Grow Your Wealth" />
        <meta property="og:description" content="Transform your financial future with our intelligent savings platform. Earn up to 20% annual returns on fixed deposits." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://betterinterest.com" />
        <meta property="og:image" content="https://betterinterest.com/og-image.jpg" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Better Interest - Smart Savings Platform" />
        <meta name="twitter:description" content="Transform your financial future with our intelligent savings platform." />
        <meta name="twitter:image" content="https://betterinterest.com/twitter-image.jpg" />
        <link rel="canonical" href="https://betterinterest.com" />
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FinancialService",
            "name": "Better Interest",
            "description": "Smart savings platform for Nigerians",
            "url": "https://betterinterest.com",
            "logo": "https://betterinterest.com/logo.png",
            "sameAs": [
              "https://twitter.com/betterinterest",
              "https://facebook.com/betterinterest",
              "https://linkedin.com/company/betterinterest"
            ]
          })}
        </script>
      </Helmet>

      <div className="min-h-screen">
        <Header />
        <HeroSection />
        <FeaturesSection />

        {/* Stats Section */}
        <section className="py-16 bg-primary text-primary-foreground">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="space-y-2"
                >
                  <stat.icon className="h-8 w-8 mx-auto mb-2" />
                  <div className="text-3xl md:text-4xl font-bold">{stat.value}</div>
                  <div className="text-primary-foreground/80">{stat.label}</div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="py-24 bg-secondary/20">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Loved by thousands of
                <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  {" "}Nigerians
                </span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Join the community of smart savers who are building wealth with Better Interest
              </p>
            </motion.div>

            <div className="grid md:grid-cols-3 gap-8">
              {loading ? (
                // Loading skeleton
                Array.from({ length: 3 }).map((_, index) => (
                  <Card key={index} className="h-full p-6">
                    <CardContent className="space-y-4">
                      <div className="flex items-center space-x-1">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <div key={i} className="w-4 h-4 bg-muted rounded animate-pulse" />
                        ))}
                      </div>
                      <div className="space-y-2">
                        <div className="h-4 bg-muted rounded animate-pulse" />
                        <div className="h-4 bg-muted rounded animate-pulse w-3/4" />
                        <div className="h-4 bg-muted rounded animate-pulse w-1/2" />
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-muted rounded-full animate-pulse" />
                        <div className="space-y-2">
                          <div className="h-4 bg-muted rounded animate-pulse w-24" />
                          <div className="h-3 bg-muted rounded animate-pulse w-20" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : testimonials.length > 0 ? (
                testimonials.map((testimonial, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  data-aos="fade-up"
                  data-aos-delay={index * 100}
                >
                  <Card className="h-full p-6 hover:shadow-lg transition-shadow">
                    <CardContent className="space-y-4">
                      <div className="flex items-center space-x-1">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                      <p className="text-muted-foreground italic">"{testimonial.content}"</p>
                      <div className="flex items-center space-x-3">
                        <img
                          src={testimonial.image}
                          alt={testimonial.name}
                          className="w-12 h-12 rounded-full"
                        />
                        <div>
                          <div className="font-semibold">{testimonial.name}</div>
                          <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                          <div className="text-sm font-semibold text-primary">Saved: {testimonial.amount}</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))
              ) : (
                <div className="col-span-full text-center py-12">
                  <p className="text-muted-foreground">No testimonials available at the moment.</p>
                </div>
              )}
            </div>
          </div>
        </section>



        {/* CTA Section */}
        <section className="py-24 bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10">
          <div className="container mx-auto px-4 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="max-w-4xl mx-auto space-y-8"
            >
              <h2 className="text-4xl md:text-6xl font-bold">
                Start building wealth
                <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  {" "}today
                </span>
              </h2>
              <p className="text-xl text-muted-foreground">
                Join thousands of Nigerians who are already building wealth with Better Interest. 
                Start with as little as ₦1,000 and watch your money grow.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  size="lg" 
                  className="text-lg px-8 py-6 rounded-xl"
                  onClick={() => navigate('/signup')}
                >
                  Open Free Account
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                <Button 
                  variant="outline" 
                  size="lg" 
                  className="text-lg px-8 py-6 rounded-xl"
                  onClick={() => navigate('/demo')}
                >
                  <Play className="mr-2 h-5 w-5" />
                  Watch Demo
                </Button>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-background border-t py-12">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-4 gap-8">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                    <TrendingUp className="h-5 w-5 text-primary-foreground" />
                  </div>
                  <span className="text-xl font-bold">Better Interest</span>
                </div>
                <p className="text-muted-foreground">
                  Building wealth for the next generation of Nigerians through smart savings and investments.
                </p>
              </div>
              
              <div>
                <h3 className="font-semibold mb-4">Product</h3>
                <ul className="space-y-2 text-muted-foreground">
                  <li><a href="/features" className="hover:text-primary">Features</a></li>
                  <li><a href="/plans" className="hover:text-primary">Pricing</a></li>
                  <li><a href="/security" className="hover:text-primary">Security</a></li>
                  <li><a href="/api" className="hover:text-primary">API</a></li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-semibold mb-4">Support</h3>
                <ul className="space-y-2 text-muted-foreground">
                  <li><a href="/help" className="hover:text-primary">Help Center</a></li>
                  <li><a href="/contact" className="hover:text-primary">Contact</a></li>
                  <li><a href="/faq" className="hover:text-primary">FAQ</a></li>
                  <li><a href="/status" className="hover:text-primary">Status</a></li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-semibold mb-4">Legal</h3>
                <ul className="space-y-2 text-muted-foreground">
                  <li><a href="/privacy" className="hover:text-primary">Privacy</a></li>
                  <li><a href="/terms" className="hover:text-primary">Terms</a></li>
                  <li><a href="/compliance" className="hover:text-primary">Compliance</a></li>
                </ul>
              </div>
            </div>
            
            <div className="border-t mt-8 pt-8 text-center text-muted-foreground">
              <p>&copy; 2024 Better Interest. All rights reserved. Licensed by CBN.</p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
};

export default EnhancedLanding;
