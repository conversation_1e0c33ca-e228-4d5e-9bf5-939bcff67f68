const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const { auth, admin } = require('../middleware/auth');
const User = require('../models/User');
const SavingsPlan = require('../models/SavingsPlan');
const Transaction = require('../models/Transaction');
const Kyc = require('../models/Kyc');
const Withdrawal = require('../models/Withdrawal');

// Get user profile
router.get('/profile', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.id)
      .select('-password -pin')
      .populate('kycStatus');
    
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        message: 'User not found' 
      });
    }

    res.json({ 
      success: true, 
      data: user 
    });
  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch user profile' 
    });
  }
});

// Update user profile
router.put('/profile', auth, async (req, res) => {
  try {
    const allowedUpdates = [
      'firstName', 'lastName', 'phone', 'profile.dateOfBirth', 
      'profile.address', 'savingsGoal', 'roundUpSettings', 
      'investmentProfile', 'socialPreferences'
    ];
    
    const updates = {};
    Object.keys(req.body).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updates[key] = req.body[key];
      }
    });

    const user = await User.findByIdAndUpdate(
      req.user.id,
      { $set: updates },
      { new: true, runValidators: true }
    ).select('-password -pin');

    if (!user) {
      return res.status(404).json({ 
        success: false, 
        message: 'User not found' 
      });
    }

    res.json({ 
      success: true, 
      data: user,
      message: 'Profile updated successfully' 
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to update profile' 
    });
  }
});

// Change password
router.put('/change-password', auth, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({ 
        success: false, 
        message: 'Current password and new password are required' 
      });
    }

    const user = await User.findById(req.user.id).select('+password');
    
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        message: 'User not found' 
      });
    }

    // Verify current password
    const isValidPassword = await user.comparePassword(currentPassword);
    if (!isValidPassword) {
      return res.status(400).json({ 
        success: false, 
        message: 'Current password is incorrect' 
      });
    }

    // Update password
    user.password = newPassword;
    await user.save();

    res.json({ 
      success: true, 
      message: 'Password changed successfully' 
    });
  } catch (error) {
    console.error('Error changing password:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to change password' 
    });
  }
});

// Change PIN
router.put('/change-pin', auth, async (req, res) => {
  try {
    const { currentPin, newPin } = req.body;

    if (!currentPin || !newPin) {
      return res.status(400).json({ 
        success: false, 
        message: 'Current PIN and new PIN are required' 
      });
    }

    const user = await User.findById(req.user.id).select('+pin');
    
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        message: 'User not found' 
      });
    }

    // Verify current PIN
    const isValidPin = await user.comparePin(currentPin);
    if (!isValidPin) {
      return res.status(400).json({ 
        success: false, 
        message: 'Current PIN is incorrect' 
      });
    }

    // Update PIN
    user.pin = newPin;
    await user.save();

    res.json({ 
      success: true, 
      message: 'PIN changed successfully' 
    });
  } catch (error) {
    console.error('Error changing PIN:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to change PIN' 
    });
  }
});

// Get user dashboard data
router.get('/dashboard', auth, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user basic info
    const user = await User.findById(userId).select('-password -pin');
    
    // Get savings plans summary
    const savingsPlans = await SavingsPlan.find({ userId, status: 'active' });
    const totalSavings = savingsPlans.reduce((sum, plan) => sum + plan.currentAmount, 0);
    const totalTarget = savingsPlans.reduce((sum, plan) => sum + plan.targetAmount, 0);
    
    // Get recent transactions from Transaction model
    const recentTransactions = await Transaction.findByUser(userId, {
      limit: 10
    });

    // Calculate monthly stats
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    const monthlyTransactions = await Transaction.find({
      userId,
      createdAt: { $gte: currentMonth },
      status: 'completed'
    });

    const monthlyStats = {
      totalSavings: monthlyTransactions
        .filter(t => ['deposit', 'interest'].includes(t.type))
        .reduce((sum, t) => sum + t.amount, 0),
      interestEarned: monthlyTransactions
        .filter(t => t.type === 'interest')
        .reduce((sum, t) => sum + t.amount, 0),
      growthRate: 8.2 // Calculate based on previous month comparison
    };

    // Calculate savings progress
    const savingsProgress = {
      percentage: totalTarget > 0 ? Math.min((totalSavings / totalTarget) * 100, 100) : 0,
      monthlyGrowth: 5.1 // Calculate based on monthly growth
    };

    // Get KYC status
    const kyc = await Kyc.findOne({ userId });
    
    // Get pending withdrawals
    const pendingWithdrawals = await Withdrawal.find({ 
      userId, 
      status: { $in: ['pending', 'processing'] } 
    }).select('amount status createdAt');

    const dashboardData = {
      user: {
        name: `${user.firstName} ${user.lastName}`,
        email: user.email,
        balance: user.balance,
        memberSince: user.createdAt,
        kycStatus: kyc?.status || 'pending',
        reputation: user.reputation
      },
      savings: {
        totalSavings,
        totalTarget,
        activePlans: savingsPlans.length,
        progressPercentage: totalTarget > 0 ? (totalSavings / totalTarget) * 100 : 0,
        plans: savingsPlans.map(plan => ({
          id: plan._id,
          name: plan.name,
          type: plan.planType,
          currentAmount: plan.currentAmount,
          targetAmount: plan.targetAmount,
          progressPercentage: plan.progressPercentage,
          daysRemaining: plan.daysRemaining
        }))
      },
      monthlyStats,
      savingsProgress,
      transactions: recentTransactions,
      withdrawals: pendingWithdrawals,
      notifications: [], // TODO: Implement notifications
      quickActions: [
        { label: 'Add Money', action: 'add_money', enabled: true },
        { label: 'Create Plan', action: 'create_plan', enabled: true },
        { label: 'Withdraw', action: 'withdraw', enabled: kyc?.status === 'approved' },
        { label: 'Complete KYC', action: 'kyc', enabled: kyc?.status !== 'approved' }
      ]
    };

    res.json({ 
      success: true, 
      data: dashboardData 
    });
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch dashboard data' 
    });
  }
});

// Get user analytics
router.get('/analytics', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { period = '6months' } = req.query;

    // Calculate date range
    const now = new Date();
    let startDate;
    switch (period) {
      case '1month':
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
        break;
      case '3months':
        startDate = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
        break;
      case '1year':
        startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
        break;
      default: // 6months
        startDate = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());
    }

    const savingsPlans = await SavingsPlan.find({ userId });
    
    // Calculate savings growth
    const savingsGrowth = [];
    const monthlyData = {};
    
    savingsPlans.forEach(plan => {
      plan.transactions.forEach(txn => {
        const txnDate = new Date(txn.date);
        if (txnDate >= startDate) {
          const monthKey = `${txnDate.getFullYear()}-${txnDate.getMonth()}`;
          if (!monthlyData[monthKey]) {
            monthlyData[monthKey] = { deposits: 0, withdrawals: 0, interest: 0 };
          }
          
          if (txn.type === 'deposit') {
            monthlyData[monthKey].deposits += txn.amount;
          } else if (txn.type === 'withdrawal') {
            monthlyData[monthKey].withdrawals += txn.amount;
          } else if (txn.type === 'interest') {
            monthlyData[monthKey].interest += txn.amount;
          }
        }
      });
    });

    // Format data for charts
    Object.keys(monthlyData).sort().forEach(monthKey => {
      const [year, month] = monthKey.split('-');
      savingsGrowth.push({
        month: new Date(year, month).toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        deposits: monthlyData[monthKey].deposits,
        withdrawals: monthlyData[monthKey].withdrawals,
        interest: monthlyData[monthKey].interest
      });
    });

    // Calculate totals
    const totalDeposits = savingsPlans.reduce((sum, plan) => sum + plan.analytics.totalDeposits, 0);
    const totalWithdrawals = savingsPlans.reduce((sum, plan) => sum + plan.analytics.totalWithdrawals, 0);
    const totalInterest = savingsPlans.reduce((sum, plan) => sum + plan.analytics.totalInterest, 0);
    const currentBalance = savingsPlans.reduce((sum, plan) => sum + plan.currentAmount, 0);

    const analytics = {
      overview: {
        totalDeposits,
        totalWithdrawals,
        totalInterest,
        currentBalance,
        netGain: totalDeposits - totalWithdrawals + totalInterest
      },
      savingsGrowth,
      planBreakdown: savingsPlans.map(plan => ({
        name: plan.name,
        type: plan.planType,
        currentAmount: plan.currentAmount,
        targetAmount: plan.targetAmount,
        progressPercentage: plan.progressPercentage
      })),
      achievements: [
        { title: 'Consistent Saver', achieved: totalDeposits > 50000 },
        { title: 'Goal Achiever', achieved: savingsPlans.some(p => p.status === 'completed') },
        { title: 'Interest Earner', achieved: totalInterest > 1000 }
      ]
    };

    res.json({ 
      success: true, 
      data: analytics 
    });
  } catch (error) {
    console.error('Error fetching analytics:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch analytics' 
    });
  }
});

// Delete user account (soft delete)
router.delete('/account', auth, async (req, res) => {
  try {
    const { password, reason } = req.body;

    if (!password) {
      return res.status(400).json({ 
        success: false, 
        message: 'Password is required to delete account' 
      });
    }

    const user = await User.findById(req.user.id).select('+password');
    
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        message: 'User not found' 
      });
    }

    // Verify password
    const isValidPassword = await user.comparePassword(password);
    if (!isValidPassword) {
      return res.status(400).json({ 
        success: false, 
        message: 'Incorrect password' 
      });
    }

    // Check for active savings plans
    const activePlans = await SavingsPlan.find({ 
      userId: req.user.id, 
      status: 'active' 
    });

    if (activePlans.length > 0) {
      return res.status(400).json({ 
        success: false, 
        message: 'Cannot delete account with active savings plans. Please withdraw or transfer your funds first.' 
      });
    }

    // Soft delete user
    user.isActive = false;
    user.deletedAt = new Date();
    user.deletionReason = reason;
    await user.save();

    res.json({ 
      success: true, 
      message: 'Account deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting account:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to delete account' 
    });
  }
});

module.exports = router;
