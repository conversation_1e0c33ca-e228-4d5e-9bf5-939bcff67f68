const mongoose = require('mongoose');

const groupMilestoneSchema = new mongoose.Schema({
  groupId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'GroupSavingsPlan',
    required: true
  },
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: ['savings_target', 'member_count', 'consistency', 'completion', 'custom'],
    required: true
  },
  criteria: {
    targetAmount: Number,
    targetMembers: Number,
    targetDays: Number,
    customCondition: String
  },
  achievedBy: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    achievedAt: {
      type: Date,
      default: Date.now
    },
    contribution: {
      type: String,
      description: String
    }
  }],
  isAchieved: {
    type: Boolean,
    default: false
  },
  achievedAt: {
    type: Date
  },
  reward: {
    type: {
      type: String,
      enum: ['badge', 'bonus', 'discount', 'feature_unlock'],
      default: 'badge'
    },
    value: {
      type: mongoose.Schema.Types.Mixed
    },
    description: String
  },
  visibility: {
    type: String,
    enum: ['public', 'group_only', 'achievers_only'],
    default: 'group_only'
  },
  autoGenerated: {
    type: Boolean,
    default: false
  },
  priority: {
    type: Number,
    default: 1,
    min: 1,
    max: 5
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for achievement percentage
groupMilestoneSchema.virtual('achievementPercentage').get(function() {
  if (!this.criteria) return 0;
  
  // Calculate based on milestone type
  switch (this.type) {
    case 'member_count':
      return Math.min(100, (this.achievedBy.length / this.criteria.targetMembers) * 100);
    default:
      return this.isAchieved ? 100 : 0;
  }
});

// Indexes
groupMilestoneSchema.index({ groupId: 1, isAchieved: 1 });
groupMilestoneSchema.index({ type: 1, isAchieved: 1 });
groupMilestoneSchema.index({ achievedAt: -1 });
groupMilestoneSchema.index({ priority: -1, createdAt: -1 });

// Check if milestone is achieved
groupMilestoneSchema.methods.checkAchievement = async function() {
  const GroupSavingsPlan = mongoose.model('GroupSavingsPlan');
  const group = await GroupSavingsPlan.findById(this.groupId);
  
  if (!group) return false;
  
  let achieved = false;
  
  switch (this.type) {
    case 'savings_target':
      achieved = group.currentAmount >= this.criteria.targetAmount;
      break;
    case 'member_count':
      achieved = group.members.length >= this.criteria.targetMembers;
      break;
    case 'completion':
      achieved = group.status === 'completed';
      break;
    case 'consistency':
      // Check if all members have contributed in target days
      const daysSinceCreation = Math.floor((Date.now() - group.createdAt) / (1000 * 60 * 60 * 24));
      achieved = daysSinceCreation >= this.criteria.targetDays;
      break;
  }
  
  if (achieved && !this.isAchieved) {
    this.isAchieved = true;
    this.achievedAt = new Date();
    
    // Add all group members as achievers
    this.achievedBy = group.members.map(member => ({
      userId: member.userId,
      achievedAt: new Date(),
      contribution: 'Group member'
    }));
    
    await this.save();
  }
  
  return achieved;
};

// Award achievement to specific user
groupMilestoneSchema.methods.awardToUser = function(userId, contribution = '') {
  const existingAchievement = this.achievedBy.find(
    a => a.userId.toString() === userId.toString()
  );
  
  if (!existingAchievement) {
    this.achievedBy.push({
      userId,
      achievedAt: new Date(),
      contribution
    });
    return this.save();
  }
  
  return Promise.resolve(this);
};

// Create default milestones for a group
groupMilestoneSchema.statics.createDefaultMilestones = async function(groupId) {
  const GroupSavingsPlan = mongoose.model('GroupSavingsPlan');
  const group = await GroupSavingsPlan.findById(groupId);
  
  if (!group) return;
  
  const defaultMilestones = [
    {
      groupId,
      title: 'First Million Saved',
      description: 'Group collectively saved ₦1,000,000',
      type: 'savings_target',
      criteria: { targetAmount: 1000000 },
      autoGenerated: true,
      priority: 5
    },
    {
      groupId,
      title: 'Full House',
      description: 'Reached maximum member capacity',
      type: 'member_count',
      criteria: { targetMembers: group.maxMembers },
      autoGenerated: true,
      priority: 4
    },
    {
      groupId,
      title: 'Goal Achieved',
      description: 'Successfully completed the savings goal',
      type: 'completion',
      autoGenerated: true,
      priority: 5
    },
    {
      groupId,
      title: 'One Month Strong',
      description: 'Group has been active for 30 days',
      type: 'consistency',
      criteria: { targetDays: 30 },
      autoGenerated: true,
      priority: 3
    }
  ];
  
  return this.insertMany(defaultMilestones);
};

module.exports = mongoose.model('GroupMilestone', groupMilestoneSchema);