export interface PaystackConfig {
  publicKey: string;
  secretKey: string;
  baseUrl: string;
}

export interface PaystackPaymentData {
  email: string;
  amount: number; // in kobo
  reference: string;
  callback_url?: string;
  metadata?: any;
  plan?: string; // For recurring payments
  channels?: string[];
}

export interface PaystackResponse {
  status: boolean;
  success?: boolean;
  message: string;
  data: any;
}

export interface RecurringPlanData {
  name: string;
  amount: number; // in kobo
  interval: 'daily' | 'weekly' | 'monthly' | 'yearly';
  description?: string;
  currency?: string;
}

export class PaystackService {
  private config: PaystackConfig;

  constructor() {
    this.config = {
      publicKey: import.meta.env.VITE_PAYSTACK_PUBLIC_KEY || '',
      secretKey: import.meta.env.VITE_PAYSTACK_SECRET_KEY || '',
      baseUrl: 'https://api.paystack.co'
    };
  }

  // Initialize Paystack payment
  async initializePayment(paymentData: PaystackPaymentData): Promise<PaystackResponse> {
    try {
      const response = await fetch(`${this.config.baseUrl}/transaction/initialize`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.secretKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...paymentData,
          currency: 'NGN'
        })
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Paystack initialization error:', error);
      throw new Error('Failed to initialize payment');
    }
  }

  // Verify payment
  async verifyPayment(reference: string): Promise<PaystackResponse> {
    try {
      const response = await fetch(`${this.config.baseUrl}/transaction/verify/${reference}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.config.secretKey}`,
          'Content-Type': 'application/json',
        }
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Paystack verification error:', error);
      throw new Error('Failed to verify payment');
    }
  }

  // Create recurring payment plan
  async createPlan(planData: RecurringPlanData): Promise<PaystackResponse> {
    try {
      const response = await fetch(`${this.config.baseUrl}/plan`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.secretKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...planData,
          currency: 'NGN'
        })
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Paystack plan creation error:', error);
      throw new Error('Failed to create payment plan');
    }
  }

  // Create subscription
  async createSubscription(customerCode: string, planCode: string): Promise<PaystackResponse> {
    try {
      const response = await fetch(`${this.config.baseUrl}/subscription`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.secretKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customer: customerCode,
          plan: planCode,
          authorization: customerCode
        })
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Paystack subscription error:', error);
      throw new Error('Failed to create subscription');
    }
  }

  // Create customer
  async createCustomer(email: string, firstName: string, lastName: string, phone?: string): Promise<PaystackResponse> {
    try {
      const response = await fetch(`${this.config.baseUrl}/customer`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.secretKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          first_name: firstName,
          last_name: lastName,
          phone
        })
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Paystack customer creation error:', error);
      throw new Error('Failed to create customer');
    }
  }

  // Open Paystack popup
  openPaystackPopup(paymentData: PaystackPaymentData, onSuccess: (reference: any) => void, onClose: () => void) {
    if (typeof window !== 'undefined' && (window as any).PaystackPop) {
      const handler = (window as any).PaystackPop.setup({
        key: this.config.publicKey,
        email: paymentData.email,
        amount: paymentData.amount,
        ref: paymentData.reference,
        metadata: paymentData.metadata,
        callback: onSuccess,
        onClose: onClose
      });
      handler.openIframe();
    }
  }

  // Withdraw funds
  async initiateWithdrawal(withdrawalData: {
    amount: number;
    bankCode: string;
    accountNumber: string;
    accountName: string;
    reason?: string;
  }): Promise<PaystackResponse> {
    try {
      const response = await fetch('/api/v1/payments/withdraw', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(withdrawalData)
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Withdrawal error:', error);
      throw new Error('Failed to initiate withdrawal');
    }
  }

  // Get list of banks
  async getBanks(): Promise<PaystackResponse> {
    try {
      const response = await fetch('/api/v1/payments/banks', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Banks fetch error:', error);
      throw new Error('Failed to fetch banks');
    }
  }

  // Verify account number
  async verifyAccount(accountNumber: string, bankCode: string): Promise<PaystackResponse> {
    try {
      const response = await fetch('/api/v1/payments/verify-account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ accountNumber, bankCode })
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Account verification error:', error);
      throw new Error('Failed to verify account');
    }
  }

  // Check withdrawal status
  async checkWithdrawalStatus(reference: string): Promise<PaystackResponse> {
    try {
      const response = await fetch(`/api/v1/payments/withdrawal-status/${reference}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Withdrawal status check error:', error);
      throw new Error('Failed to check withdrawal status');
    }
  }

  // Generate payment reference
  generateReference(): string {
    return `pstk_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }
}

export const paystackService = new PaystackService();