import { Button } from '@/components/ui/button';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { GraduationCap, Loader2, Phone, Tv, Wifi, Zap } from 'lucide-react';
import { useEffect, useState } from 'react';

interface BillProvider {
  _id: string;
  name: string;
  category: string;
  fee: number;
  feeType: 'fixed' | 'percentage';
  minAmount: number;
  maxAmount: number;
}

interface CustomerInfo {
  identifier: string;
  name: string;
  address?: string;
  isValid: boolean;
}

interface BillPaymentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const categoryIcons = {
  airtime: Phone,
  data: Wifi,
  electricity: Zap,
  cable_tv: Tv,
  internet: Wifi,
  education: GraduationCap,
  betting: Phone
};

const categoryDescriptions = {
  airtime: 'Mobile airtime top-up for all networks',
  data: 'Mobile data bundles for internet access',
  electricity: 'Electricity bill payments for all DISCOs',
  cable_tv: 'Cable TV subscriptions and renewals',
  internet: 'Internet service provider payments',
  education: 'Educational service payments and result checkers',
  betting: 'Sports betting and gaming platform top-ups'
};

export const BillPaymentModal = ({ open, onOpenChange }: BillPaymentModalProps) => {
  const [providers, setProviders] = useState<BillProvider[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedProvider, setSelectedProvider] = useState<BillProvider | null>(null);
  const [amount, setAmount] = useState('');
  const [customerIdentifier, setCustomerIdentifier] = useState('');
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo | null>(null);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (open) {
      fetchProviders();
    }
  }, [open]);

  const fetchProviders = async () => {
    try {
      const response = await fetch('/api/v1/bills/providers', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch bill providers');
      }

      const data = await response.json();
      setProviders(data.data || []);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch bill providers',
        variant: 'destructive'
      });
    }
  };

  const verifyCustomer = async () => {
    if (!selectedProvider || !customerIdentifier.trim()) return;

    setIsVerifying(true);
    try {
      // Verify customer with actual API call
      const response = await fetch('/api/v1/bills/verify-customer', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          providerId: selectedProvider._id,
          customerIdentifier,
          category: selectedCategory
        })
      });

      if (!response.ok) {
        throw new Error('Customer verification failed');
      }

      const data = await response.json();
      const customerInfo: CustomerInfo = {
        identifier: customerIdentifier,
        name: data.data.customerName,
        address: data.data.address,
        isValid: true
      };

      setCustomerInfo(customerInfo);
      toast({
        title: 'Customer Verified',
        description: `Customer: ${customerInfo.name}`,
      });
    } catch (error: any) {
      toast({
        title: 'Verification Failed',
        description: 'Failed to verify customer',
        variant: 'destructive'
      });
      setCustomerInfo(null);
    } finally {
      setIsVerifying(false);
    }
  };

  const calculateFee = () => {
    if (!selectedProvider || !amount) return 0;
    return selectedProvider.feeType === 'percentage'
      ? (parseFloat(amount) * selectedProvider.fee) / 100
      : selectedProvider.fee;
  };

  const getTotalAmount = () => {
    const amountValue = parseFloat(amount) || 0;
    return amountValue + calculateFee();
  };

  const handlePayment = async () => {
    if (!selectedProvider || !amount || !customerInfo) return;

    setIsProcessing(true);
    try {
      // Process payment with actual API call
      const response = await fetch('/api/v1/bills/pay', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          providerId: selectedProvider._id,
          customerIdentifier,
          amount: parseFloat(amount),
          category: selectedCategory
        })
      });

      if (!response.ok) {
        throw new Error('Payment processing failed');
      }

      const data = await response.json();
      toast({
        title: 'Payment Successful',
        description: `Bill payment completed. Reference: ${data.data.reference}`,
      });

      // Reset form and close modal
      resetForm();
      onOpenChange(false);
    } catch (error: any) {
      toast({
        title: 'Payment Failed',
        description: 'Failed to process payment',
        variant: 'destructive'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const resetForm = () => {
    setAmount('');
    setCustomerIdentifier('');
    setCustomerInfo(null);
    setSelectedProvider(null);
    setSelectedCategory('');
  };

  const handleClose = () => {
    resetForm();
    onOpenChange(false);
  };

  const filteredProviders = providers.filter(p => p.category === selectedCategory);
  const categories = [...new Set(providers.map(p => p.category))];

  const getPlaceholderText = () => {
    switch (selectedCategory) {
      case 'airtime':
      case 'data':
        return 'Enter phone number (e.g., ***********)';
      case 'electricity':
        return 'Enter meter number (e.g., ***********)';
      case 'cable_tv':
        return 'Enter decoder/smartcard number';
      case 'internet':
        return 'Enter account number or username';
      case 'education':
        return 'Enter registration number';
      case 'betting':
        return 'Enter username or customer ID';
      default:
        return 'Enter customer identifier';
    }
  };

  const getAmountSuggestions = () => {
    switch (selectedCategory) {
      case 'airtime':
        return [100, 200, 500, 1000, 2000, 5000];
      case 'data':
        return [500, 1000, 2000, 3000, 5000, 10000];
      case 'electricity':
        return [1000, 2000, 5000, 10000, 20000, 50000];
      case 'cable_tv':
        return [2000, 4000, 6000, 8000, 12000, 18000];
      case 'internet':
        return [2500, 5000, 10000, 15000, 25000];
      case 'education':
        return [500, 1000, 2500];
      case 'betting':
        return [500, 1000, 2000, 5000, 10000];
      default:
        return [];
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="flex items-center gap-2 text-xl font-bold">
            <Zap className="h-6 w-6 text-primary" />
            Bill Payment
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6 pt-4">
          {/* Category Selection */}
          <div className="space-y-2">
            <Label htmlFor="category" className="text-sm font-medium">Service Category</Label>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select service category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => {
                  const Icon = categoryIcons[category as keyof typeof categoryIcons];
                  const description = categoryDescriptions[category as keyof typeof categoryDescriptions];
                  return (
                    <SelectItem key={category} value={category}>
                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <Icon className="h-4 w-4" />
                          <span className="capitalize font-medium">{category.replace('_', ' ')}</span>
                        </div>
                        <span className="text-xs text-muted-foreground">{description}</span>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Provider Selection */}
          {selectedCategory && (
            <div className="space-y-2">
              <Label htmlFor="provider" className="text-sm font-medium">Service Provider</Label>
              <Select 
                value={selectedProvider?._id || ''} 
                onValueChange={(id) => {
                  const provider = filteredProviders.find(p => p._id === id);
                  setSelectedProvider(provider || null);
                  setCustomerInfo(null);
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select service provider" />
                </SelectTrigger>
                <SelectContent>
                  {filteredProviders.map((provider) => (
                    <SelectItem key={provider._id} value={provider._id}>
                      {provider.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Customer Identifier */}
          {selectedProvider && (
            <div className="space-y-2">
              <Label htmlFor="customer" className="text-sm font-medium">Customer Details</Label>
              <div className="flex gap-2">
                <Input
                  id="customer"
                  placeholder={getPlaceholderText()}
                  value={customerIdentifier}
                  onChange={(e) => setCustomerIdentifier(e.target.value)}
                  className="flex-1"
                />
                <Button 
                  onClick={verifyCustomer}
                  disabled={!customerIdentifier.trim() || isVerifying}
                  size="sm"
                  variant="outline"
                >
                  {isVerifying && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                  Verify
                </Button>
              </div>
              {customerInfo && (
                <div className="p-4 bg-muted/50 rounded-lg border">
                  <p className="text-sm font-medium text-foreground">{customerInfo.name}</p>
                  {customerInfo.address && (
                    <p className="text-xs text-muted-foreground mt-1">{customerInfo.address}</p>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Amount */}
          {customerInfo && (
            <div className="space-y-2">
              <Label htmlFor="amount" className="text-sm font-medium">Amount (₦)</Label>
              <Input
                id="amount"
                type="number"
                placeholder="Enter amount"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                min={selectedProvider?.minAmount}
                max={selectedProvider?.maxAmount}
                className="w-full"
              />
              
              {/* Amount suggestions */}
              {getAmountSuggestions().length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  <span className="text-xs text-muted-foreground">Quick amounts:</span>
                  {getAmountSuggestions().map((suggestedAmount) => (
                    <Button
                      key={suggestedAmount}
                      type="button"
                      variant="outline"
                      size="sm"
                      className="h-7 px-2 text-xs"
                      onClick={() => setAmount(suggestedAmount.toString())}
                    >
                      ₦{suggestedAmount.toLocaleString()}
                    </Button>
                  ))}
                </div>
              )}
              
              {selectedProvider && amount && (
                <div className="p-4 bg-muted/30 rounded-lg border text-sm space-y-2">
                  <div className="flex justify-between">
                    <span>Amount:</span>
                    <span className="font-medium">₦{parseFloat(amount).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Fee:</span>
                    <span className="font-medium">₦{calculateFee().toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between font-semibold border-t border-muted-foreground/20 pt-2">
                    <span>Total:</span>
                    <span className="text-primary">₦{getTotalAmount().toLocaleString()}</span>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Payment Button */}
          {customerInfo && amount && (
            <div className="flex gap-3 pt-4">
              <Button 
                variant="outline" 
                onClick={handleClose}
                className="flex-1"
                disabled={isProcessing}
              >
                Cancel
              </Button>
              <Button 
                onClick={handlePayment}
                disabled={isProcessing || !amount || parseFloat(amount) < (selectedProvider?.minAmount || 0)}
                className="flex-1"
              >
                {isProcessing && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                Pay ₦{getTotalAmount().toLocaleString()}
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
