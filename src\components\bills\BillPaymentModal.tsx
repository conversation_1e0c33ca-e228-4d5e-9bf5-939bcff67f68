import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Phone, Zap, Tv, Wifi, Plane, GraduationCap, X } from 'lucide-react';
import axios from 'axios';

interface BillProvider {
  _id: string;
  name: string;
  category: string;
  fee: number;
  feeType: 'fixed' | 'percentage';
  minAmount: number;
  maxAmount: number;
}

interface CustomerInfo {
  identifier: string;
  name: string;
  address?: string;
  isValid: boolean;
}

interface BillPaymentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const categoryIcons = {
  airtime: Phone,
  data: Wifi,
  electricity: Zap,
  cable_tv: Tv,
  internet: Wifi,
  education: GraduationCap,
  betting: Phone
};

const categoryDescriptions = {
  airtime: 'Mobile airtime top-up for all networks',
  data: 'Mobile data bundles for internet access',
  electricity: 'Electricity bill payments for all DISCOs',
  cable_tv: 'Cable TV subscriptions and renewals',
  internet: 'Internet service provider payments',
  education: 'Educational service payments and result checkers',
  betting: 'Sports betting and gaming platform top-ups'
};

export const BillPaymentModal = ({ open, onOpenChange }: BillPaymentModalProps) => {
  const [providers, setProviders] = useState<BillProvider[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedProvider, setSelectedProvider] = useState<BillProvider | null>(null);
  const [amount, setAmount] = useState('');
  const [customerIdentifier, setCustomerIdentifier] = useState('');
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo | null>(null);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (open) {
      fetchProviders();
    }
  }, [open]);

  const fetchProviders = async () => {
    try {
      // Comprehensive mock data for Nigerian bill payments
      const mockProviders: BillProvider[] = [
        // Airtime providers
        { _id: '1', name: 'MTN Nigeria', category: 'airtime', fee: 0, feeType: 'fixed', minAmount: 50, maxAmount: 50000 },
        { _id: '2', name: 'Airtel Nigeria', category: 'airtime', fee: 0, feeType: 'fixed', minAmount: 50, maxAmount: 50000 },
        { _id: '3', name: 'Glo Nigeria', category: 'airtime', fee: 0, feeType: 'fixed', minAmount: 50, maxAmount: 50000 },
        { _id: '4', name: '9mobile', category: 'airtime', fee: 0, feeType: 'fixed', minAmount: 50, maxAmount: 50000 },
        
        // Data providers
        { _id: '5', name: 'MTN Data', category: 'data', fee: 25, feeType: 'fixed', minAmount: 100, maxAmount: 50000 },
        { _id: '6', name: 'Airtel Data', category: 'data', fee: 25, feeType: 'fixed', minAmount: 100, maxAmount: 50000 },
        { _id: '7', name: 'Glo Data', category: 'data', fee: 25, feeType: 'fixed', minAmount: 100, maxAmount: 50000 },
        { _id: '8', name: '9mobile Data', category: 'data', fee: 25, feeType: 'fixed', minAmount: 100, maxAmount: 50000 },
        
        // Electricity providers
        { _id: '9', name: 'Eko Electricity (EKEDC)', category: 'electricity', fee: 100, feeType: 'fixed', minAmount: 1000, maxAmount: 100000 },
        { _id: '10', name: 'Ikeja Electric (IE)', category: 'electricity', fee: 100, feeType: 'fixed', minAmount: 1000, maxAmount: 100000 },
        { _id: '11', name: 'Abuja Electricity (AEDC)', category: 'electricity', fee: 100, feeType: 'fixed', minAmount: 1000, maxAmount: 100000 },
        { _id: '12', name: 'Port Harcourt Electric (PHED)', category: 'electricity', fee: 100, feeType: 'fixed', minAmount: 1000, maxAmount: 100000 },
        { _id: '13', name: 'Kano Electricity (KEDCO)', category: 'electricity', fee: 100, feeType: 'fixed', minAmount: 1000, maxAmount: 100000 },
        { _id: '14', name: 'Enugu Electricity (EEDC)', category: 'electricity', fee: 100, feeType: 'fixed', minAmount: 1000, maxAmount: 100000 },
        
        // Cable TV providers
        { _id: '15', name: 'DSTV', category: 'cable_tv', fee: 100, feeType: 'fixed', minAmount: 1500, maxAmount: 50000 },
        { _id: '16', name: 'GOtv', category: 'cable_tv', fee: 50, feeType: 'fixed', minAmount: 500, maxAmount: 20000 },
        { _id: '17', name: 'StarTimes', category: 'cable_tv', fee: 50, feeType: 'fixed', minAmount: 300, maxAmount: 15000 },
        { _id: '18', name: 'Strong Decoder', category: 'cable_tv', fee: 30, feeType: 'fixed', minAmount: 200, maxAmount: 10000 },
        
        // Internet providers
        { _id: '19', name: 'Spectranet', category: 'internet', fee: 150, feeType: 'fixed', minAmount: 2000, maxAmount: 100000 },
        { _id: '20', name: 'Smile Communications', category: 'internet', fee: 100, feeType: 'fixed', minAmount: 1000, maxAmount: 50000 },
        { _id: '21', name: 'Swift Networks', category: 'internet', fee: 100, feeType: 'fixed', minAmount: 1500, maxAmount: 75000 },
        { _id: '22', name: 'Coollink', category: 'internet', fee: 75, feeType: 'fixed', minAmount: 1000, maxAmount: 40000 },
        
        // Education providers
        { _id: '23', name: 'WAEC Result Checker', category: 'education', fee: 50, feeType: 'fixed', minAmount: 500, maxAmount: 2500 },
        { _id: '24', name: 'NECO Result Checker', category: 'education', fee: 50, feeType: 'fixed', minAmount: 500, maxAmount: 2500 },
        { _id: '25', name: 'JAMB Result Checker', category: 'education', fee: 50, feeType: 'fixed', minAmount: 1000, maxAmount: 5000 },
        
        // Betting platforms
        { _id: '26', name: 'Bet9ja', category: 'betting', fee: 0, feeType: 'fixed', minAmount: 100, maxAmount: 1000000 },
        { _id: '27', name: 'SportyBet', category: 'betting', fee: 0, feeType: 'fixed', minAmount: 100, maxAmount: 1000000 },
        { _id: '28', name: 'NairaBet', category: 'betting', fee: 0, feeType: 'fixed', minAmount: 100, maxAmount: 500000 },
        { _id: '29', name: 'Merrybet', category: 'betting', fee: 0, feeType: 'fixed', minAmount: 100, maxAmount: 500000 },
      ];
      setProviders(mockProviders);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch bill providers',
        variant: 'destructive'
      });
    }
  };

  const verifyCustomer = async () => {
    if (!selectedProvider || !customerIdentifier.trim()) return;

    setIsVerifying(true);
    try {
      // Mock verification - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const mockCustomerInfo: CustomerInfo = {
        identifier: customerIdentifier,
        name: `Customer ${customerIdentifier.slice(-4)}`,
        address: selectedCategory === 'electricity' ? 'Lagos, Nigeria' : undefined,
        isValid: true
      };

      setCustomerInfo(mockCustomerInfo);
      toast({
        title: 'Customer Verified',
        description: `Customer: ${mockCustomerInfo.name}`,
      });
    } catch (error: any) {
      toast({
        title: 'Verification Failed',
        description: 'Failed to verify customer',
        variant: 'destructive'
      });
      setCustomerInfo(null);
    } finally {
      setIsVerifying(false);
    }
  };

  const calculateFee = () => {
    if (!selectedProvider || !amount) return 0;
    return selectedProvider.feeType === 'percentage'
      ? (parseFloat(amount) * selectedProvider.fee) / 100
      : selectedProvider.fee;
  };

  const getTotalAmount = () => {
    const amountValue = parseFloat(amount) || 0;
    return amountValue + calculateFee();
  };

  const handlePayment = async () => {
    if (!selectedProvider || !amount || !customerInfo) return;

    setIsProcessing(true);
    try {
      // Mock payment - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      toast({
        title: 'Payment Successful',
        description: `Bill payment completed. Reference: BP${Date.now()}`,
      });

      // Reset form and close modal
      resetForm();
      onOpenChange(false);
    } catch (error: any) {
      toast({
        title: 'Payment Failed',
        description: 'Failed to process payment',
        variant: 'destructive'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const resetForm = () => {
    setAmount('');
    setCustomerIdentifier('');
    setCustomerInfo(null);
    setSelectedProvider(null);
    setSelectedCategory('');
  };

  const handleClose = () => {
    resetForm();
    onOpenChange(false);
  };

  const filteredProviders = providers.filter(p => p.category === selectedCategory);
  const categories = [...new Set(providers.map(p => p.category))];

  const getPlaceholderText = () => {
    switch (selectedCategory) {
      case 'airtime':
      case 'data':
        return 'Enter phone number (e.g., ***********)';
      case 'electricity':
        return 'Enter meter number (e.g., ***********)';
      case 'cable_tv':
        return 'Enter decoder/smartcard number';
      case 'internet':
        return 'Enter account number or username';
      case 'education':
        return 'Enter registration number';
      case 'betting':
        return 'Enter username or customer ID';
      default:
        return 'Enter customer identifier';
    }
  };

  const getAmountSuggestions = () => {
    switch (selectedCategory) {
      case 'airtime':
        return [100, 200, 500, 1000, 2000, 5000];
      case 'data':
        return [500, 1000, 2000, 3000, 5000, 10000];
      case 'electricity':
        return [1000, 2000, 5000, 10000, 20000, 50000];
      case 'cable_tv':
        return [2000, 4000, 6000, 8000, 12000, 18000];
      case 'internet':
        return [2500, 5000, 10000, 15000, 25000];
      case 'education':
        return [500, 1000, 2500];
      case 'betting':
        return [500, 1000, 2000, 5000, 10000];
      default:
        return [];
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="flex items-center gap-2 text-xl font-bold">
            <Zap className="h-6 w-6 text-primary" />
            Bill Payment
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6 pt-4">
          {/* Category Selection */}
          <div className="space-y-2">
            <Label htmlFor="category" className="text-sm font-medium">Service Category</Label>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select service category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => {
                  const Icon = categoryIcons[category as keyof typeof categoryIcons];
                  const description = categoryDescriptions[category as keyof typeof categoryDescriptions];
                  return (
                    <SelectItem key={category} value={category}>
                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <Icon className="h-4 w-4" />
                          <span className="capitalize font-medium">{category.replace('_', ' ')}</span>
                        </div>
                        <span className="text-xs text-muted-foreground">{description}</span>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Provider Selection */}
          {selectedCategory && (
            <div className="space-y-2">
              <Label htmlFor="provider" className="text-sm font-medium">Service Provider</Label>
              <Select 
                value={selectedProvider?._id || ''} 
                onValueChange={(id) => {
                  const provider = filteredProviders.find(p => p._id === id);
                  setSelectedProvider(provider || null);
                  setCustomerInfo(null);
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select service provider" />
                </SelectTrigger>
                <SelectContent>
                  {filteredProviders.map((provider) => (
                    <SelectItem key={provider._id} value={provider._id}>
                      {provider.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Customer Identifier */}
          {selectedProvider && (
            <div className="space-y-2">
              <Label htmlFor="customer" className="text-sm font-medium">Customer Details</Label>
              <div className="flex gap-2">
                <Input
                  id="customer"
                  placeholder={getPlaceholderText()}
                  value={customerIdentifier}
                  onChange={(e) => setCustomerIdentifier(e.target.value)}
                  className="flex-1"
                />
                <Button 
                  onClick={verifyCustomer}
                  disabled={!customerIdentifier.trim() || isVerifying}
                  size="sm"
                  variant="outline"
                >
                  {isVerifying && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                  Verify
                </Button>
              </div>
              {customerInfo && (
                <div className="p-4 bg-muted/50 rounded-lg border">
                  <p className="text-sm font-medium text-foreground">{customerInfo.name}</p>
                  {customerInfo.address && (
                    <p className="text-xs text-muted-foreground mt-1">{customerInfo.address}</p>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Amount */}
          {customerInfo && (
            <div className="space-y-2">
              <Label htmlFor="amount" className="text-sm font-medium">Amount (₦)</Label>
              <Input
                id="amount"
                type="number"
                placeholder="Enter amount"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                min={selectedProvider?.minAmount}
                max={selectedProvider?.maxAmount}
                className="w-full"
              />
              
              {/* Amount suggestions */}
              {getAmountSuggestions().length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  <span className="text-xs text-muted-foreground">Quick amounts:</span>
                  {getAmountSuggestions().map((suggestedAmount) => (
                    <Button
                      key={suggestedAmount}
                      type="button"
                      variant="outline"
                      size="sm"
                      className="h-7 px-2 text-xs"
                      onClick={() => setAmount(suggestedAmount.toString())}
                    >
                      ₦{suggestedAmount.toLocaleString()}
                    </Button>
                  ))}
                </div>
              )}
              
              {selectedProvider && amount && (
                <div className="p-4 bg-muted/30 rounded-lg border text-sm space-y-2">
                  <div className="flex justify-between">
                    <span>Amount:</span>
                    <span className="font-medium">₦{parseFloat(amount).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Fee:</span>
                    <span className="font-medium">₦{calculateFee().toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between font-semibold border-t border-muted-foreground/20 pt-2">
                    <span>Total:</span>
                    <span className="text-primary">₦{getTotalAmount().toLocaleString()}</span>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Payment Button */}
          {customerInfo && amount && (
            <div className="flex gap-3 pt-4">
              <Button 
                variant="outline" 
                onClick={handleClose}
                className="flex-1"
                disabled={isProcessing}
              >
                Cancel
              </Button>
              <Button 
                onClick={handlePayment}
                disabled={isProcessing || !amount || parseFloat(amount) < (selectedProvider?.minAmount || 0)}
                className="flex-1"
              >
                {isProcessing && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                Pay ₦{getTotalAmount().toLocaleString()}
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};