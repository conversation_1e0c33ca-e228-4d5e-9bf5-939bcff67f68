
import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  <PERSON>Chart, Line, BarChart, Bar, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from "recharts";
import { toast } from 'sonner';

export default function AdminAnalytics() {
  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch analytics data from API
  useEffect(() => {
    const fetchAnalyticsData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/v1/analytics/admin-dashboard', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to fetch analytics data');
        }

        const result = await response.json();
        setAnalyticsData(result.data);
      } catch (error) {
        console.error('Error fetching analytics data:', error);
        toast.error('Failed to load analytics data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnalyticsData();
  }, []);
  // Sample data for various analytics charts
  const userGrowthData = [
    { name: 'Jan', total: 123 },
    { name: 'Feb', total: 156 },
    { name: 'Mar', total: 210 },
    { name: 'Apr', total: 243 },
    { name: 'May', total: 289 },
    { name: 'Jun', total: 340 },
    { name: 'Jul', total: 412 },
    { name: 'Aug', total: 456 },
  ];

  const savingsData = [
    { name: 'Jan', total: 450000 },
    { name: 'Feb', total: 620000 },
    { name: 'Mar', total: 830000 },
    { name: 'Apr', total: 970000 },
    { name: 'May', total: 1250000 },
    { name: 'Jun', total: 1580000 },
    { name: 'Jul', total: 1920000 },
    { name: 'Aug', total: 2350000 },
  ];

  const planDistributionData = [
    { name: 'Daily Saver', value: 45 },
    { name: 'Weekly Target', value: 25 },
    { name: 'Premium Savings', value: 30 },
  ];

  const COLORS = ['#1231B8', '#FDE314', '#36B37E', '#FF5630'];

  const userVerificationData = [
    { name: 'Verified', value: 68 },
    { name: 'Pending', value: 22 },
    { name: 'Rejected', value: 10 },
  ];

  const transactionData = [
    { name: 'Deposits', total: 246 },
    { name: 'Withdrawals', total: 98 },
    { name: 'Interest Paid', total: 180 },
  ];

  const overviewData = {
    totalUsers: 456,
    activeUsers: 378,
    totalSavings: "₦2,350,000",
    verifiedUsers: "68%",
    totalPlans: 3,
    avgSavingsPerUser: "₦5,153.5"
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Admin Analytics</h1>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        <Card className="blue-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-white">Total Users</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-yellow-300">{overviewData.totalUsers}</p>
            <p className="text-white/80">{overviewData.activeUsers} active users</p>
          </CardContent>
        </Card>
        
        <Card className="blue-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-white">Total Savings</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-yellow-300">{overviewData.totalSavings}</p>
            <p className="text-white/80">Avg per user: {overviewData.avgSavingsPerUser}</p>
          </CardContent>
        </Card>
        
        <Card className="blue-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-white">Verified Users</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-yellow-300">{overviewData.verifiedUsers}</p>
            <p className="text-white/80">Total plans: {overviewData.totalPlans}</p>
          </CardContent>
        </Card>
      </div>
      
      <Tabs defaultValue="users" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="savings">Savings</TabsTrigger>
          <TabsTrigger value="plans">Plans</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
        </TabsList>
        
        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Growth</CardTitle>
              <CardDescription>Monthly user acquisition trends</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={350}>
                <LineChart data={userGrowthData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="total" 
                    stroke="#1231B8" 
                    strokeWidth={2} 
                    activeDot={{ r: 8 }} 
                    name="Users"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>User Verification Status</CardTitle>
              <CardDescription>Distribution of user verification status</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center">
              <ResponsiveContainer width="100%" height={350}>
                <PieChart>
                  <Pie
                    data={userVerificationData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {userVerificationData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => `${value}%`} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="savings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Total Savings Growth</CardTitle>
              <CardDescription>Monthly savings accumulation</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={350}>
                <BarChart data={savingsData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => `₦${value.toLocaleString()}`} />
                  <Legend />
                  <Bar 
                    dataKey="total" 
                    name="Savings (₦)" 
                    fill="#1231B8" 
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="plans" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Plan Distribution</CardTitle>
              <CardDescription>User distribution across savings plans</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center">
              <ResponsiveContainer width="100%" height={350}>
                <PieChart>
                  <Pie
                    data={planDistributionData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {planDistributionData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => `${value}%`} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Transaction Types</CardTitle>
              <CardDescription>Distribution of transaction types</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={350}>
                <BarChart data={transactionData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="total" name="Transactions" fill="#FDE314" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
