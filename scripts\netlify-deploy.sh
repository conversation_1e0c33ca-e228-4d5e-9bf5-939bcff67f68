#!/bin/bash

# ===========================================
# BETTER INTEREST - NETLIFY DEPLOYMENT SCRIPT
# ===========================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SITE_NAME="better-interest-demo"
PRODUCTION_DOMAIN="demo.kojaonline.store"
API_DOMAIN="api.kojaonline.store"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    log_info "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install Node.js 16 or higher."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    if ! command -v netlify &> /dev/null; then
        log_warning "Netlify CLI is not installed. Installing..."
        npm install -g netlify-cli
    fi
    
    log_success "All dependencies are available."
}

# Validate environment variables
validate_environment() {
    log_info "Validating environment variables..."
    
    if [ ! -f ".env" ]; then
        log_error ".env file not found. Please create it from .env.example"
        exit 1
    fi
    
    # Check if required environment variables are set
    source .env
    
    if [ -z "$VITE_API_URL" ]; then
        log_error "VITE_API_URL is not set in .env file"
        exit 1
    fi
    
    if [ -z "$VITE_PAYSTACK_PUBLIC_KEY" ]; then
        log_warning "VITE_PAYSTACK_PUBLIC_KEY is not set in .env file"
    fi
    
    log_success "Environment validation completed."
}

# Install dependencies
install_dependencies() {
    log_info "Installing dependencies..."
    npm ci --silent
    log_success "Dependencies installed successfully."
}

# Run tests
run_tests() {
    log_info "Running tests..."
    
    # Type checking
    if command -v tsc &> /dev/null; then
        log_info "Running TypeScript type checking..."
        npx tsc --noEmit
        log_success "TypeScript type checking passed."
    fi
    
    # Linting
    if [ -f "package.json" ] && grep -q "lint" package.json; then
        log_info "Running ESLint..."
        npm run lint
        log_success "Linting passed."
    fi
    
    log_success "All tests completed successfully."
}

# Build the application
build_application() {
    log_info "Building application for production..."
    
    # Clean previous build
    if [ -d "dist" ]; then
        rm -rf dist
        log_info "Cleaned previous build directory."
    fi
    
    # Build
    npm run build
    
    # Verify build output
    if [ ! -d "dist" ]; then
        log_error "Build failed - dist directory not found."
        exit 1
    fi
    
    if [ ! -f "dist/index.html" ]; then
        log_error "Build failed - index.html not found in dist directory."
        exit 1
    fi
    
    # Check build size
    BUILD_SIZE=$(du -sh dist | cut -f1)
    log_info "Build size: $BUILD_SIZE"
    
    log_success "Application built successfully."
}

# Deploy to Netlify
deploy_to_netlify() {
    local deploy_type=$1
    
    log_info "Deploying to Netlify ($deploy_type)..."
    
    # Login check
    if ! netlify status &> /dev/null; then
        log_info "Please login to Netlify..."
        netlify login
    fi
    
    # Deploy
    if [ "$deploy_type" = "production" ]; then
        log_info "Deploying to production..."
        netlify deploy --prod --dir=dist --site=$SITE_NAME
        
        # Verify deployment
        log_info "Verifying production deployment..."
        sleep 10
        
        if curl -f -s "https://$PRODUCTION_DOMAIN" > /dev/null; then
            log_success "Production deployment verified successfully!"
            log_info "Site URL: https://$PRODUCTION_DOMAIN"
        else
            log_error "Production deployment verification failed."
            exit 1
        fi
    else
        log_info "Deploying preview..."
        DEPLOY_URL=$(netlify deploy --dir=dist --site=$SITE_NAME --json | jq -r '.deploy_url')
        
        if [ "$DEPLOY_URL" != "null" ] && [ -n "$DEPLOY_URL" ]; then
            log_success "Preview deployment completed!"
            log_info "Preview URL: $DEPLOY_URL"
        else
            log_error "Preview deployment failed."
            exit 1
        fi
    fi
}

# Post-deployment tasks
post_deployment() {
    log_info "Running post-deployment tasks..."
    
    # Warm up cache
    log_info "Warming up cache..."
    curl -s "https://$PRODUCTION_DOMAIN" > /dev/null || true
    curl -s "https://$PRODUCTION_DOMAIN/enhanced" > /dev/null || true
    curl -s "https://$PRODUCTION_DOMAIN/faq" > /dev/null || true
    
    # Test API connectivity
    log_info "Testing API connectivity..."
    if curl -f -s "https://$API_DOMAIN/health" > /dev/null; then
        log_success "API is accessible."
    else
        log_warning "API health check failed. Please verify backend deployment."
    fi
    
    log_success "Post-deployment tasks completed."
}

# Main deployment function
main() {
    local deploy_type=${1:-preview}
    local skip_tests=${2:-false}
    
    log_info "Starting Better Interest deployment..."
    log_info "Deploy type: $deploy_type"
    log_info "Skip tests: $skip_tests"
    
    # Pre-deployment checks
    check_dependencies
    validate_environment
    install_dependencies
    
    # Testing (unless skipped)
    if [ "$skip_tests" != "true" ]; then
        run_tests
    else
        log_warning "Skipping tests as requested."
    fi
    
    # Build and deploy
    build_application
    deploy_to_netlify "$deploy_type"
    
    # Post-deployment (only for production)
    if [ "$deploy_type" = "production" ]; then
        post_deployment
    fi
    
    log_success "🎉 Deployment completed successfully!"
    
    if [ "$deploy_type" = "production" ]; then
        echo ""
        echo "🌐 Production URL: https://$PRODUCTION_DOMAIN"
        echo "📊 Netlify Dashboard: https://app.netlify.com/sites/$SITE_NAME"
        echo "🔧 API URL: https://$API_DOMAIN"
        echo ""
    fi
}

# Handle script arguments
case "${1:-}" in
    "production"|"prod")
        main "production" "${2:-}"
        ;;
    "preview"|"staging")
        main "preview" "${2:-}"
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [production|preview] [skip-tests]"
        echo ""
        echo "Commands:"
        echo "  production  Deploy to production (demo.kojaonline.store)"
        echo "  preview     Deploy preview (default)"
        echo "  help        Show this help message"
        echo ""
        echo "Options:"
        echo "  skip-tests  Skip running tests before deployment"
        echo ""
        echo "Examples:"
        echo "  $0 production           # Deploy to production"
        echo "  $0 preview              # Deploy preview"
        echo "  $0 production skip-tests # Deploy to production without tests"
        ;;
    *)
        main "preview" "${2:-}"
        ;;
esac
