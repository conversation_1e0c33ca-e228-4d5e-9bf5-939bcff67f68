import React from 'react';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { BillPaymentForm } from '@/components/bills/BillPaymentForm';
import { BillPaymentHistory } from '@/components/bills/BillPaymentHistory';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Zap, History, Phone, Wifi, Tv, Plane } from 'lucide-react';

const quickServices = [
  { name: 'Airtime', icon: Phone, category: 'airtime', color: 'bg-blue-100 text-blue-600' },
  { name: 'Data', icon: Wifi, category: 'data', color: 'bg-green-100 text-green-600' },
  { name: 'Electricity', icon: Zap, category: 'electricity', color: 'bg-yellow-100 text-yellow-600' },
  { name: 'Cable TV', icon: Tv, category: 'cable_tv', color: 'bg-purple-100 text-purple-600' },
  { name: 'Flight', icon: Plane, category: 'flight', color: 'bg-red-100 text-red-600' },
];

export default function BillPayments() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Bill Payments</h1>
          <p className="text-muted-foreground">Pay your bills quickly and securely</p>
        </div>
      </div>

      {/* Quick Services */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Services</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {quickServices.map((service) => {
              const Icon = service.icon;
              return (
                <div
                  key={service.name}
                  className={`p-4 rounded-lg text-center cursor-pointer hover:scale-105 transition-transform ${service.color}`}
                >
                  <Icon className="h-8 w-8 mx-auto mb-2" />
                  <p className="font-medium">{service.name}</p>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <Tabs defaultValue="pay" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="pay" className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Pay Bills
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <History className="h-4 w-4" />
            Payment History
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pay" className="space-y-6">
          <div className="flex justify-center">
            <BillPaymentForm />
          </div>
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <BillPaymentHistory />
        </TabsContent>
      </Tabs>
    </div>
  );
}