import React, { useState } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AlertTriangle, Shield, FileText, Users } from 'lucide-react';

interface GroupSavingsDisclaimerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAccept: () => void;
  groupName?: string;
}

export const GroupSavingsDisclaimer: React.FC<GroupSavingsDisclaimerProps> = ({
  open,
  onOpenChange,
  onAccept,
  groupName = "this group"
}) => {
  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const [acceptedRisks, setAcceptedRisks] = useState(false);
  const [acceptedResponsibility, setAcceptedResponsibility] = useState(false);

  const canProceed = acceptedTerms && acceptedRisks && acceptedResponsibility;

  const handleAccept = () => {
    if (canProceed) {
      onAccept();
      onOpenChange(false);
      // Reset checkboxes for next time
      setAcceptedTerms(false);
      setAcceptedRisks(false);
      setAcceptedResponsibility(false);
    }
  };

  const disclaimerPoints = [
    {
      icon: <AlertTriangle className="h-5 w-5 text-amber-500" />,
      title: "Financial Risk",
      content: "Group savings involve financial commitments. There's a risk of losing money if group members default on contributions or if the group dissolves prematurely."
    },
    {
      icon: <Users className="h-5 w-5 text-blue-500" />,
      title: "Member Dependency",
      content: "Your success depends on other members' participation. We cannot guarantee that all members will fulfill their contribution obligations."
    },
    {
      icon: <Shield className="h-5 w-5 text-green-500" />,
      title: "No FDIC Insurance",
      content: "Group savings are not insured by FDIC or any government agency. Your contributions are not protected by deposit insurance."
    },
    {
      icon: <FileText className="h-5 w-5 text-purple-500" />,
      title: "Legal Obligations",
      content: "By joining this group, you enter into a binding agreement with other members. You are legally responsible for your contribution commitments."
    }
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <AlertTriangle className="h-6 w-6 text-amber-500" />
            Group Savings Disclaimer
          </DialogTitle>
          <DialogDescription>
            Please read and acknowledge the following important information before joining {groupName}.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Main Alert */}
          <Alert className="border-amber-200 bg-amber-50">
            <AlertTriangle className="h-4 w-4 text-amber-600" />
            <AlertDescription className="text-amber-800">
              <strong>Important:</strong> Group savings involve financial risks and commitments. 
              Please read all terms carefully before proceeding.
            </AlertDescription>
          </Alert>

          {/* Risk Disclosure */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Risk Disclosure & Terms</h3>
            
            {disclaimerPoints.map((point, index) => (
              <div key={index} className="flex gap-3 p-4 border border-border rounded-lg bg-muted/30">
                {point.icon}
                <div>
                  <h4 className="font-medium text-sm mb-1">{point.title}</h4>
                  <p className="text-sm text-muted-foreground">{point.content}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Additional Terms */}
          <div className="space-y-3 p-4 bg-muted/50 rounded-lg">
            <h4 className="font-medium">Additional Terms & Conditions</h4>
            <ul className="text-sm text-muted-foreground space-y-2">
              <li>• Contribution schedules are binding and penalties may apply for late payments</li>
              <li>• Group dissolution requires majority member consent</li>
              <li>• Dispute resolution follows our standard arbitration process</li>
              <li>• Early withdrawal may incur penalties and affect other members</li>
              <li>• Better Interest acts as a facilitator, not a guarantor of outcomes</li>
            </ul>
          </div>

          {/* Acknowledgment Checkboxes */}
          <div className="space-y-4 border-t pt-4">
            <h4 className="font-medium">Required Acknowledgments</h4>
            
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <Checkbox 
                  id="terms" 
                  checked={acceptedTerms}
                  onCheckedChange={(checked) => setAcceptedTerms(checked === true)}
                />
                <label htmlFor="terms" className="text-sm leading-relaxed cursor-pointer">
                  I have read and agree to the <strong>Terms of Service</strong> and understand 
                  the legal obligations of joining this group savings plan.
                </label>
              </div>

              <div className="flex items-start space-x-3">
                <Checkbox 
                  id="risks" 
                  checked={acceptedRisks}
                  onCheckedChange={(checked) => setAcceptedRisks(checked === true)}
                />
                <label htmlFor="risks" className="text-sm leading-relaxed cursor-pointer">
                  I understand and acknowledge all <strong>financial risks</strong> associated 
                  with group savings, including potential loss of contributions.
                </label>
              </div>

              <div className="flex items-start space-x-3">
                <Checkbox 
                  id="responsibility" 
                  checked={acceptedResponsibility}
                  onCheckedChange={(checked) => setAcceptedResponsibility(checked === true)}
                />
                <label htmlFor="responsibility" className="text-sm leading-relaxed cursor-pointer">
                  I accept <strong>full responsibility</strong> for my participation and 
                  understand that Better Interest is not liable for group member defaults.
                </label>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <Button
            onClick={handleAccept}
            disabled={!canProceed}
            className={`${canProceed ? 'bg-primary hover:bg-primary/90' : 'bg-muted text-muted-foreground'}`}
          >
            I Acknowledge & Proceed
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};