import React from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { FileText, Eye, Download } from 'lucide-react';

interface TermsOfServiceModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const TermsOfServiceModal: React.FC<TermsOfServiceModalProps> = ({
  open,
  onOpenChange
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-green-500" />
            Terms of Service
          </DialogTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline">Last Updated: January 10, 2024</Badge>
            <Badge variant="secondary">Version 3.0</Badge>
          </div>
          <DialogDescription>
            These Terms of Service govern your use of Better Interest services and platform.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 text-sm">
          {/* Acceptance of Terms */}
          <section>
            <h3 className="font-semibold text-base mb-3">1. Acceptance of Terms</h3>
            <p className="text-muted-foreground leading-relaxed">
              By accessing or using Better Interest services, you agree to be bound by these Terms of Service 
              and all applicable laws and regulations. If you do not agree with any of these terms, you are 
              prohibited from using or accessing our services.
            </p>
          </section>

          <Separator />

          {/* Service Description */}
          <section>
            <h3 className="font-semibold text-base mb-3">2. Service Description</h3>
            <p className="text-muted-foreground leading-relaxed mb-3">
              Better Interest provides digital savings and financial services including:
            </p>
            <ul className="list-disc list-inside text-muted-foreground space-y-1">
              <li>Individual and group savings plans</li>
              <li>Interest-bearing deposit accounts</li>
              <li>Financial goal tracking and achievement tools</li>
              <li>Referral and rewards programs</li>
              <li>KYC verification and identity services</li>
            </ul>
          </section>

          <Separator />

          {/* User Responsibilities */}
          <section>
            <h3 className="font-semibold text-base mb-3">3. User Responsibilities</h3>
            <div className="space-y-3">
              <div>
                <h4 className="font-medium mb-2">Account Security</h4>
                <ul className="list-disc list-inside text-muted-foreground space-y-1">
                  <li>Maintain confidentiality of your account credentials</li>
                  <li>Notify us immediately of any unauthorized access</li>
                  <li>Use strong passwords and enable two-factor authentication</li>
                  <li>Keep your contact information up to date</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Prohibited Activities</h4>
                <ul className="list-disc list-inside text-muted-foreground space-y-1">
                  <li>Fraudulent activities or money laundering</li>
                  <li>Providing false or misleading information</li>
                  <li>Violating applicable laws and regulations</li>
                  <li>Attempting to circumvent our security measures</li>
                </ul>
              </div>
            </div>
          </section>

          <Separator />

          {/* Financial Terms */}
          <section>
            <h3 className="font-semibold text-base mb-3">4. Financial Terms</h3>
            <div className="space-y-3">
              <div>
                <h4 className="font-medium mb-2">Savings Plans</h4>
                <ul className="list-disc list-inside text-muted-foreground space-y-1">
                  <li>Interest rates are subject to change with notice</li>
                  <li>Early withdrawal may incur penalties</li>
                  <li>Minimum balance requirements may apply</li>
                  <li>Tax obligations are the responsibility of the user</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Group Savings</h4>
                <ul className="list-disc list-inside text-muted-foreground space-y-1">
                  <li>Participation in group savings creates binding obligations</li>
                  <li>Members are responsible for their contribution commitments</li>
                  <li>Dispute resolution follows our standard procedures</li>
                  <li>Group dissolution requires majority consent</li>
                </ul>
              </div>
            </div>
          </section>

          <Separator />

          {/* Service Availability */}
          <section>
            <h3 className="font-semibold text-base mb-3">5. Service Availability</h3>
            <p className="text-muted-foreground leading-relaxed mb-3">
              We strive to maintain high service availability, but we do not guarantee uninterrupted access:
            </p>
            <ul className="list-disc list-inside text-muted-foreground space-y-1">
              <li>Scheduled maintenance may temporarily interrupt services</li>
              <li>Technical issues may cause occasional downtime</li>
              <li>We reserve the right to modify or discontinue features</li>
              <li>Emergency situations may require service suspension</li>
            </ul>
          </section>

          <Separator />

          {/* Limitation of Liability */}
          <section>
            <h3 className="font-semibold text-base mb-3">6. Limitation of Liability</h3>
            <p className="text-muted-foreground leading-relaxed">
              Better Interest shall not be liable for any indirect, incidental, special, consequential, or 
              punitive damages, including but not limited to loss of profits, data, or business opportunities. 
              Our total liability shall not exceed the amount of fees paid by you in the twelve months 
              preceding the claim.
            </p>
          </section>

          <Separator />

          {/* Dispute Resolution */}
          <section>
            <h3 className="font-semibold text-base mb-3">7. Dispute Resolution</h3>
            <p className="text-muted-foreground leading-relaxed">
              Any disputes arising from these terms shall first be addressed through our internal complaint 
              procedure. If unresolved, disputes shall be settled through binding arbitration under Nigerian 
              law in Lagos, Nigeria.
            </p>
          </section>

          <Separator />

          {/* Contact Information */}
          <section>
            <h3 className="font-semibold text-base mb-3">8. Contact Information</h3>
            <p className="text-muted-foreground leading-relaxed">
              For questions about these Terms of Service, please contact us:
            </p>
            <div className="mt-2 p-3 bg-muted rounded-lg">
              <p className="text-sm"><strong>Email:</strong> <EMAIL></p>
              <p className="text-sm"><strong>Phone:</strong> +234 (0) 800 BETTER</p>
              <p className="text-sm"><strong>Address:</strong> Better Interest Legal Team, Lagos, Nigeria</p>
            </div>
          </section>
        </div>

        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Download PDF
            </Button>
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-2" />
              Print
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};