import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle, Info } from 'lucide-react';
import { GroupSavingsPlan, groupSavingsService } from '@/services/group-savings';
import { GroupSavingsDisclaimer } from '@/components/legal/GroupSavingsDisclaimer';
import { toast } from 'sonner';

interface CreateGroupDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateGroup?: (groupData: any) => void;
  onGroupCreated?: (group: GroupSavingsPlan) => void;
  currentUserId?: string;
}

interface FormData {
  name: string;
  description: string;
  category: string;
  targetAmount: number;
  contributionAmount: number;
  startDate: string;
  endDate: string;
  maxMembers: number;
  frequency: 'daily' | 'weekly' | 'monthly';
  minContribution: number;
  maxContribution: number;
  penaltyRate: number;
}

export const CreateGroupDialog: React.FC<CreateGroupDialogProps> = ({
  open,
  onOpenChange,
  onCreateGroup,
  onGroupCreated,
  currentUserId
}) => {
  const [loading, setLoading] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [showDisclaimer, setShowDisclaimer] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    description: '',
    category: 'electronics',
    targetAmount: 0,
    contributionAmount: 0,
    startDate: '',
    endDate: '',
    maxMembers: 10,
    frequency: 'monthly',
    minContribution: 0,
    maxContribution: 0,
    penaltyRate: 5
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const calculateTargetAmount = (): number => {
    if (!formData.startDate || !formData.endDate || !formData.contributionAmount || !formData.maxMembers) {
      return 0;
    }

    const startDate = new Date(formData.startDate);
    const endDate = new Date(formData.endDate);
    const timeDiff = endDate.getTime() - startDate.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

    let periodsCount = 0;
    
    switch (formData.frequency) {
      case 'daily':
        periodsCount = daysDiff;
        break;
      case 'weekly':
        periodsCount = Math.floor(daysDiff / 7);
        break;
      case 'monthly':
        periodsCount = Math.floor(daysDiff / 30);
        break;
      default:
        periodsCount = 0;
    }

    const totalContributions = formData.contributionAmount * formData.maxMembers * periodsCount;
    return Math.round(totalContributions);
  };

  const validateForm = (): string | null => {
    if (!formData.name.trim()) return 'Group name is required';
    if (!formData.description.trim()) return 'Description is required';
    if (formData.targetAmount < 1000) return 'Target amount must be at least ₦1,000';
    if (formData.contributionAmount < 100) return 'Contribution amount must be at least ₦100';
    if (formData.maxMembers < 2) return 'Group must have at least 2 members';
    if (formData.maxMembers > 50) return 'Group cannot have more than 50 members';
    if (!formData.startDate) return 'Start date is required';
    if (!formData.endDate) return 'End date is required';
    if (new Date(formData.endDate) <= new Date(formData.startDate)) {
      return 'End date must be after start date';
    }
    
    // Calculate if target is achievable
    const totalContributions = formData.contributionAmount * formData.maxMembers;
    const timeframe = new Date(formData.endDate).getTime() - new Date(formData.startDate).getTime();
    const months = timeframe / (1000 * 60 * 60 * 24 * 30);
    
    if (formData.frequency === 'monthly') {
      const maxPossible = totalContributions * months;
      if (maxPossible < formData.targetAmount) {
        return 'Target amount is not achievable with current settings';
      }
    }

    return null;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      toast.error(validationError);
      return;
    }

    if (!onCreateGroup) {
      toast.error('Create function not available');
      return;
    }

    setShowDisclaimer(true);
  };

  const handleDisclaimerAccept = () => {
    setShowDisclaimer(false);
    
    const groupData = {
      name: formData.name.trim(),
      description: formData.description.trim(),
      category: formData.category,
      targetAmount: calculateTargetAmount(),
      contributionAmount: formData.contributionAmount,
      startDate: new Date(formData.startDate),
      endDate: new Date(formData.endDate),
      maxMembers: formData.maxMembers,
      rules: {
        minContribution: formData.minContribution || formData.contributionAmount * 0.5,
        maxContribution: formData.maxContribution || formData.contributionAmount * 2,
        frequency: formData.frequency,
        penaltyRate: formData.penaltyRate
      }
    };

    onCreateGroup(groupData);
    onOpenChange(false);
    
    // Reset form
    setFormData({
      name: '',
      description: '',
      category: 'electronics',
      targetAmount: 0,
      contributionAmount: 0,
      startDate: '',
      endDate: '',
      maxMembers: 10,
      frequency: 'monthly',
      minContribution: 0,
      maxContribution: 0,
      penaltyRate: 5
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Group Savings Plan</DialogTitle>
          <DialogDescription>
            Set up a new group savings plan for collective purchasing or savings goals
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Group Name *</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="e.g., iPhone 15 Savings Group"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Describe the purpose and benefits of this group"
                rows={3}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select 
                value={formData.category} 
                onValueChange={(value) => handleSelectChange('category', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="electronics">Electronics & Appliances</SelectItem>
                  <SelectItem value="car">Cars & Vehicles</SelectItem>
                  <SelectItem value="phone">Phones & Gadgets</SelectItem>
                  <SelectItem value="grocery">Groceries & Food</SelectItem>
                  <SelectItem value="education">Education & Training</SelectItem>
                  <SelectItem value="health">Health & Medical</SelectItem>
                  <SelectItem value="travel">Travel & Tourism</SelectItem>
                  <SelectItem value="business">Business & Investment</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Timeline */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date *</Label>
              <Input
                id="startDate"
                name="startDate"
                type="date"
                value={formData.startDate}
                onChange={handleInputChange}
                min={new Date().toISOString().split('T')[0]}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate">End Date *</Label>
              <Input
                id="endDate"
                name="endDate"
                type="date"
                value={formData.endDate}
                onChange={handleInputChange}
                min={formData.startDate || new Date().toISOString().split('T')[0]}
                required
              />
            </div>
          </div>

          {/* Group Settings */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="maxMembers">Maximum Members *</Label>
              <Input
                id="maxMembers"
                name="maxMembers"
                type="number"
                value={formData.maxMembers}
                onChange={handleInputChange}
                min={2}
                max={50}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="frequency">Contribution Frequency *</Label>
              <Select 
                value={formData.frequency} 
                onValueChange={(value) => handleSelectChange('frequency', value as 'daily' | 'weekly' | 'monthly')}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Financial Details */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="contributionAmount">Contribution Amount (₦) *</Label>
              <Input
                id="contributionAmount"
                name="contributionAmount"
                type="number"
                value={formData.contributionAmount || ''}
                onChange={handleInputChange}
                min={100}
                placeholder="5000"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="targetAmount">Auto-Calculated Target (₦)</Label>
              <div className="relative">
                <Input
                  id="targetAmount"
                  name="targetAmount"
                  type="number"
                  value={calculateTargetAmount()}
                  className="bg-muted/50 cursor-not-allowed"
                  readOnly
                  disabled
                />
                <div className="absolute inset-y-0 right-3 flex items-center pointer-events-none">
                  <span className="text-xs text-muted-foreground">Auto</span>
                </div>
              </div>
              <p className="text-xs text-muted-foreground">
                Calculated based on contribution amount, frequency, duration, and member count
              </p>
            </div>
          </div>

          {/* Advanced Settings */}
          <div className="space-y-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="text-sm"
            >
              {showAdvanced ? 'Hide' : 'Show'} Advanced Settings
            </Button>

            {showAdvanced && (
              <div className="space-y-4 border border-border rounded-lg p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="minContribution">Min Contribution (₦)</Label>
                    <Input
                      id="minContribution"
                      name="minContribution"
                      type="number"
                      value={formData.minContribution || ''}
                      onChange={handleInputChange}
                      placeholder="Auto-calculated"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="maxContribution">Max Contribution (₦)</Label>
                    <Input
                      id="maxContribution"
                      name="maxContribution"
                      type="number"
                      value={formData.maxContribution || ''}
                      onChange={handleInputChange}
                      placeholder="Auto-calculated"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="penaltyRate">Late Payment Penalty (%)</Label>
                  <Input
                    id="penaltyRate"
                    name="penaltyRate"
                    type="number"
                    value={formData.penaltyRate}
                    onChange={handleInputChange}
                    min={0}
                    max={20}
                    step={0.5}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Warnings */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-sm">
              <strong>Important:</strong> As group admin, you'll be responsible for managing members, 
              approving contributions, and ensuring fair distribution of benefits. Make sure you understand 
              the commitment before creating this group.
            </AlertDescription>
          </Alert>

          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription className="text-sm">
              <strong>Group Safety Tips:</strong> Only invite people you trust, set clear rules for 
              contributions and withdrawals, and maintain transparent communication with all members.
            </AlertDescription>
          </Alert>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-primary hover:bg-primary/90"
            >
              {loading ? 'Creating...' : 'Create Group'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>

      {/* Group Savings Disclaimer Modal */}
      <GroupSavingsDisclaimer
        open={showDisclaimer}
        onOpenChange={setShowDisclaimer}
        onAccept={handleDisclaimerAccept}
        groupName={formData.name || "this group"}
      />
    </Dialog>
  );
};