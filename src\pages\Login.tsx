import { SocialLogin } from '@/components/auth/SocialLogin';
import { FloatingLabelInput } from "@/components/ui/floating-label-input";
import { useAuth } from '@/hooks/use-auth';
import { CheckCircle, Fingerprint, Shield } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const Login = () => {
  const navigate = useNavigate();
  const { signIn } = useAuth();
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      navigate('/dashboard');
    }
  }, [navigate]);

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      await signIn(email, password);
      navigate('/dashboard');
    } catch (error: any) {
      console.error("Login error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAccount = () => {
    navigate('/signup');
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 sm:p-6 lg:p-8" style={{ background: 'var(--gradient-hero)' }}>
      <div className="w-full max-w-sm sm:max-w-md lg:max-w-lg xl:max-w-xl">        
        <div className="w-full p-6 sm:p-8 lg:p-10 animate-fade-in">
          <div className="text-center mb-6 sm:mb-8 lg:mb-10">
            <div className="flex justify-center mb-4">
              <div className="h-12 sm:h-16 lg:h-20 w-auto">
                <img
                  src="/lovable-uploads/371e0f57-a280-42c0-8e1b-50aee7ebddc6.png"
                  alt="Better Interest Logo"
                  className="h-full w-auto object-contain"
                />
              </div>
            </div>
            <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-2 text-white">
              Welcome Back
            </h2>
            <p className="text-sm sm:text-base lg:text-lg text-gray-300 leading-relaxed">
              Sign in to continue building your wealth
            </p>
          </div>
          
          {/* Social Login Section */}
          <SocialLogin mode="login" className="mb-6 sm:mb-8" />
          
          <form onSubmit={handleEmailLogin} className="space-y-4 sm:space-y-6 lg:space-y-8">
            <FloatingLabelInput 
              id="email" 
              type="email" 
              label="Email Address" 
              value={email} 
              onChange={e => setEmail(e.target.value)} 
              required 
              className="h-12 sm:h-14 lg:h-16 text-sm sm:text-base lg:text-lg rounded-xl border-2 bg-white/10 backdrop-blur-sm transition-all duration-300 focus:border-emerald-400/60 focus:shadow-[0_0_0_4px_rgba(34,197,94,0.1)] hover:bg-white/15 text-white placeholder:text-gray-400" 
            />
            
            <FloatingLabelInput 
              id="password" 
              type="password" 
              label="Password" 
              value={password} 
              onChange={e => setPassword(e.target.value)} 
              required 
              className="h-12 sm:h-14 lg:h-16 text-sm sm:text-base lg:text-lg rounded-xl border-2 bg-white/10 backdrop-blur-sm transition-all duration-300 focus:border-emerald-400/60 focus:shadow-[0_0_0_4px_rgba(34,197,94,0.1)] hover:bg-white/15 text-white placeholder:text-gray-400" 
            />
            
            <div className="text-right">
              <button 
                type="button" 
                className="text-sm sm:text-base text-emerald-400 hover:text-emerald-300 transition-colors duration-200 hover:underline"
              >
                Forgot Password?
              </button>
            </div>
            
            <button
              type="submit"
              className="w-full h-12 sm:h-14 lg:h-16 text-base sm:text-lg lg:text-xl font-semibold btn-neumorphic-inward disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  Signing in...
                </div>
              ) : (
                "Sign In"
              )}
            </button>
          </form>
          
          <div className="mt-6 sm:mt-8 text-center">
            <p className="text-sm sm:text-base lg:text-lg text-gray-300">
              Don't have an account?{" "}
              <button
                onClick={handleCreateAccount}
                className="text-logo-yellow-orange hover:text-logo-yellow-orange-light font-semibold transition-colors duration-200 hover:underline"
              >
                Create Account
              </button>
            </p>
          </div>
        </div>
        
        {/* Trust indicators moved to bottom */}
        <div className="flex items-center justify-center gap-3 sm:gap-4 lg:gap-6 mt-4 sm:mt-6 text-xs sm:text-sm lg:text-base text-gray-400">
          <div className="flex items-center gap-1 sm:gap-2">
            <Shield className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 text-emerald-400" />
            <span>Secure</span>
          </div>
          <div className="flex items-center gap-1 sm:gap-2">
            <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 text-emerald-400" />
            <span>Verified</span>
          </div>
          <div className="flex items-center gap-1 sm:gap-2">
            <Fingerprint className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 text-emerald-400" />
            <span>Fast</span>
          </div>
        </div>
        
        {/* Regulatory Badge */}
        <div className="mt-4 sm:mt-6 lg:mt-8 pt-4 sm:pt-6 border-t border-white/20">
          <div className="flex justify-center mb-3 sm:mb-4">
            <img 
              src="/lovable-uploads/ba52c32b-b343-4d80-ae98-51c43a63c65c.png" 
              alt="NDIC & AMAC MFB Regulatory Badge" 
              className="h-10 sm:h-12 lg:h-14 xl:h-16 w-auto object-contain opacity-90 hover:opacity-100 transition-opacity duration-300" 
            />
          </div>
          <p className="text-xs sm:text-sm lg:text-base text-gray-400 text-center leading-relaxed">
            NDIC Insured • AMAC MFB Licensed • Your deposits are protected
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
