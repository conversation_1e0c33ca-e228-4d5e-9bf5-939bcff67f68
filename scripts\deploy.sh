#!/bin/bash

# Better Interest Deployment Script
# This script handles the complete deployment process

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="betterinterest"
DOCKER_COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"
BACKUP_DIR="./backups"
LOG_FILE="./logs/deployment.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    mkdir -p logs backups nginx/ssl monitoring/grafana/{dashboards,datasources} logging
    success "Directories created successfully"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check if .env file exists
    if [ ! -f "$ENV_FILE" ]; then
        warning ".env file not found. Creating from .env.production template..."
        cp .env.production .env
        warning "Please update .env file with your production values before continuing."
        read -p "Press Enter to continue after updating .env file..."
    fi
    
    success "Prerequisites check completed"
}

# Backup existing data
backup_data() {
    log "Creating backup of existing data..."
    
    BACKUP_TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    BACKUP_PATH="$BACKUP_DIR/backup_$BACKUP_TIMESTAMP"
    
    mkdir -p "$BACKUP_PATH"
    
    # Backup MongoDB data if container exists
    if docker ps -a --format 'table {{.Names}}' | grep -q "${PROJECT_NAME}_mongodb"; then
        log "Backing up MongoDB data..."
        docker exec "${PROJECT_NAME}_mongodb_1" mongodump --out /tmp/backup
        docker cp "${PROJECT_NAME}_mongodb_1:/tmp/backup" "$BACKUP_PATH/mongodb"
    fi
    
    # Backup uploaded files if they exist
    if [ -d "./backend/uploads" ]; then
        log "Backing up uploaded files..."
        cp -r ./backend/uploads "$BACKUP_PATH/"
    fi
    
    success "Backup created at $BACKUP_PATH"
}

# Build and deploy
deploy() {
    log "Starting deployment process..."
    
    # Pull latest images
    log "Pulling latest Docker images..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" pull
    
    # Build custom images
    log "Building custom images..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" build --no-cache
    
    # Stop existing containers
    log "Stopping existing containers..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # Start new containers
    log "Starting new containers..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    success "Deployment completed successfully"
}

# Health check
health_check() {
    log "Performing health checks..."
    
    # Wait for services to start
    sleep 30
    
    # Check frontend
    if curl -f http://localhost:3000/health > /dev/null 2>&1; then
        success "Frontend is healthy"
    else
        error "Frontend health check failed"
    fi
    
    # Check backend
    if curl -f http://localhost:5000/api/v1/health > /dev/null 2>&1; then
        success "Backend is healthy"
    else
        error "Backend health check failed"
    fi
    
    # Check MongoDB
    if docker exec "${PROJECT_NAME}_mongodb_1" mongo --eval "db.adminCommand('ismaster')" > /dev/null 2>&1; then
        success "MongoDB is healthy"
    else
        error "MongoDB health check failed"
    fi
    
    # Check Redis
    if docker exec "${PROJECT_NAME}_redis_1" redis-cli ping | grep -q PONG; then
        success "Redis is healthy"
    else
        error "Redis health check failed"
    fi
    
    success "All health checks passed"
}

# Setup monitoring
setup_monitoring() {
    log "Setting up monitoring..."
    
    # Create Grafana dashboards
    cat > monitoring/grafana/dashboards/dashboard.yml << EOF
apiVersion: 1

providers:
  - name: 'default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards
EOF

    # Create Prometheus datasource
    cat > monitoring/grafana/datasources/prometheus.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF

    success "Monitoring setup completed"
}

# Setup SSL certificates
setup_ssl() {
    log "Setting up SSL certificates..."
    
    if [ ! -f "nginx/ssl/cert.pem" ] || [ ! -f "nginx/ssl/private.key" ]; then
        warning "SSL certificates not found. Generating self-signed certificates for development..."
        
        mkdir -p nginx/ssl
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/private.key \
            -out nginx/ssl/cert.pem \
            -subj "/C=NG/ST=Lagos/L=Lagos/O=Better Interest/CN=localhost"
        
        warning "Self-signed certificates generated. Replace with proper certificates for production."
    fi
    
    success "SSL setup completed"
}

# Cleanup old images and containers
cleanup() {
    log "Cleaning up old Docker images and containers..."
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused containers
    docker container prune -f
    
    # Remove unused volumes (be careful with this in production)
    # docker volume prune -f
    
    success "Cleanup completed"
}

# Main deployment process
main() {
    log "Starting Better Interest deployment..."
    
    create_directories
    check_prerequisites
    setup_ssl
    setup_monitoring
    backup_data
    deploy
    health_check
    cleanup
    
    success "🎉 Deployment completed successfully!"
    log "Application is now running at:"
    log "  Frontend: http://localhost:3000"
    log "  Backend API: http://localhost:5000"
    log "  Grafana: http://localhost:3001"
    log "  Kibana: http://localhost:5601"
    log ""
    log "To view logs: docker-compose logs -f"
    log "To stop services: docker-compose down"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "backup")
        backup_data
        ;;
    "health")
        health_check
        ;;
    "cleanup")
        cleanup
        ;;
    "logs")
        docker-compose logs -f
        ;;
    "stop")
        docker-compose down
        ;;
    "restart")
        docker-compose restart
        ;;
    *)
        echo "Usage: $0 {deploy|backup|health|cleanup|logs|stop|restart}"
        echo ""
        echo "Commands:"
        echo "  deploy  - Full deployment process (default)"
        echo "  backup  - Create backup of existing data"
        echo "  health  - Run health checks"
        echo "  cleanup - Clean up old Docker images"
        echo "  logs    - View application logs"
        echo "  stop    - Stop all services"
        echo "  restart - Restart all services"
        exit 1
        ;;
esac
