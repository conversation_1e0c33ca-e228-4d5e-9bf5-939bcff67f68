const express = require('express');
const router = express.Router();
const Ticket = require('../models/Ticket');
const FAQ = require('../models/FAQ');
const User = require('../models/User');
const { authenticateToken } = require('../middleware/auth');
const { sendEmail } = require('../services/emailService');
const { v4: uuidv4 } = require('uuid');

// Create new ticket
router.post('/tickets', authenticateToken, async (req, res) => {
  try {
    const { subject, category, priority, message, metadata } = req.body;
    
    const ticket = new Ticket({
      userId: req.user.id,
      subject,
      category: category || 'general',
      priority: priority || 'medium',
      messages: [{
        id: uuidv4(),
        senderId: req.user.id,
        senderType: 'user',
        content: message,
        timestamp: new Date()
      }],
      metadata: {
        ...metadata,
        source: 'chat'
      }
    });

    await ticket.save();
    await ticket.populate(['userId', 'assignedTo']);

    // Send notification to support team
    await sendEmail({
      to: process.env.SUPPORT_EMAIL || '<EMAIL>',
      subject: `New Support Ticket: ${ticket.ticketId}`,
      template: 'new-ticket',
      data: {
        ticketId: ticket.ticketId,
        subject: ticket.subject,
        category: ticket.category,
        priority: ticket.priority,
        userEmail: req.user.email,
        message: message
      }
    });

    res.status(201).json({
      success: true,
      data: ticket
    });
  } catch (error) {
    console.error('Create ticket error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create ticket'
    });
  }
});

// Get user tickets
router.get('/tickets', authenticateToken, async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;
    const filter = { userId: req.user.id };
    
    if (status) filter.status = status;

    const tickets = await Ticket.find(filter)
      .populate('assignedTo', 'name email')
      .sort({ lastActivity: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Ticket.countDocuments(filter);

    res.json({
      success: true,
      data: tickets,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get tickets error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get tickets'
    });
  }
});

// Get single ticket
router.get('/tickets/:ticketId', authenticateToken, async (req, res) => {
  try {
    const ticket = await Ticket.findOne({ 
      ticketId: req.params.ticketId,
      userId: req.user.id 
    }).populate(['userId', 'assignedTo']);

    if (!ticket) {
      return res.status(404).json({
        success: false,
        message: 'Ticket not found'
      });
    }

    res.json({
      success: true,
      data: ticket
    });
  } catch (error) {
    console.error('Get ticket error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get ticket'
    });
  }
});

// Add message to ticket
router.post('/tickets/:ticketId/messages', authenticateToken, async (req, res) => {
  try {
    const { content } = req.body;
    const ticket = await Ticket.findOne({ 
      ticketId: req.params.ticketId,
      userId: req.user.id 
    });

    if (!ticket) {
      return res.status(404).json({
        success: false,
        message: 'Ticket not found'
      });
    }

    const message = {
      id: uuidv4(),
      senderId: req.user.id,
      senderType: 'user',
      content,
      timestamp: new Date()
    };

    ticket.messages.push(message);
    ticket.status = 'open'; // Reopen if needed
    await ticket.save();

    // Notify assigned staff
    if (ticket.assignedTo) {
      const staff = await User.findById(ticket.assignedTo);
      if (staff) {
        await sendEmail({
          to: staff.email,
          subject: `New message in ticket: ${ticket.ticketId}`,
          template: 'ticket-reply',
          data: {
            ticketId: ticket.ticketId,
            subject: ticket.subject,
            message: content,
            userEmail: req.user.email
          }
        });
      }
    }

    res.json({
      success: true,
      data: message
    });
  } catch (error) {
    console.error('Add message error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add message'
    });
  }
});

// Get FAQs
router.get('/faqs', async (req, res) => {
  try {
    const { category, search, page = 1, limit = 20 } = req.query;
    const filter = { isActive: true };
    
    if (category) filter.category = category;
    
    let query = FAQ.find(filter);
    
    if (search) {
      query = query.find({ $text: { $search: search } });
    }
    
    const faqs = await query
      .sort({ order: 1, helpful: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await FAQ.countDocuments(filter);

    res.json({
      success: true,
      data: faqs,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get FAQs error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get FAQs'
    });
  }
});

// Rate FAQ helpfulness
router.post('/faqs/:id/rate', async (req, res) => {
  try {
    const { helpful } = req.body;
    const faq = await FAQ.findById(req.params.id);
    
    if (!faq) {
      return res.status(404).json({
        success: false,
        message: 'FAQ not found'
      });
    }

    if (helpful) {
      faq.helpful += 1;
    } else {
      faq.notHelpful += 1;
    }

    await faq.save();

    res.json({
      success: true,
      message: 'Rating recorded'
    });
  } catch (error) {
    console.error('Rate FAQ error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to rate FAQ'
    });
  }
});

// Admin routes
router.use('/admin', authenticateToken, async (req, res, next) => {
  const user = await User.findById(req.user.id);
  if (!user || !['admin', 'staff'].includes(user.role)) {
    return res.status(403).json({
      success: false,
      message: 'Access denied'
    });
  }
  next();
});

// Get all tickets (admin)
router.get('/admin/tickets', async (req, res) => {
  try {
    const { 
      status, 
      priority, 
      category, 
      assignedTo, 
      page = 1, 
      limit = 20,
      search 
    } = req.query;
    
    const filter = {};
    if (status) filter.status = status;
    if (priority) filter.priority = priority;
    if (category) filter.category = category;
    if (assignedTo) filter.assignedTo = assignedTo;
    
    let query = Ticket.find(filter);
    
    if (search) {
      query = query.find({
        $or: [
          { ticketId: { $regex: search, $options: 'i' } },
          { subject: { $regex: search, $options: 'i' } }
        ]
      });
    }

    const tickets = await query
      .populate('userId', 'name email')
      .populate('assignedTo', 'name email')
      .sort({ lastActivity: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Ticket.countDocuments(filter);

    res.json({
      success: true,
      data: tickets,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get admin tickets error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get tickets'
    });
  }
});

// Assign ticket (admin)
router.patch('/admin/tickets/:ticketId/assign', async (req, res) => {
  try {
    const { assignedTo } = req.body;
    const ticket = await Ticket.findOne({ ticketId: req.params.ticketId });
    
    if (!ticket) {
      return res.status(404).json({
        success: false,
        message: 'Ticket not found'
      });
    }

    ticket.assignedTo = assignedTo;
    ticket.status = 'in_progress';
    await ticket.save();

    // Notify assigned staff
    if (assignedTo) {
      const staff = await User.findById(assignedTo);
      if (staff) {
        await sendEmail({
          to: staff.email,
          subject: `Ticket assigned: ${ticket.ticketId}`,
          template: 'ticket-assigned',
          data: {
            ticketId: ticket.ticketId,
            subject: ticket.subject,
            priority: ticket.priority
          }
        });
      }
    }

    res.json({
      success: true,
      message: 'Ticket assigned successfully'
    });
  } catch (error) {
    console.error('Assign ticket error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assign ticket'
    });
  }
});

// Update ticket status (admin)
router.patch('/admin/tickets/:ticketId/status', async (req, res) => {
  try {
    const { status } = req.body;
    const ticket = await Ticket.findOne({ ticketId: req.params.ticketId });
    
    if (!ticket) {
      return res.status(404).json({
        success: false,
        message: 'Ticket not found'
      });
    }

    ticket.status = status;
    if (status === 'resolved') {
      ticket.resolvedAt = new Date();
      ticket.resolvedBy = req.user.id;
    }
    
    await ticket.save();

    res.json({
      success: true,
      message: 'Ticket status updated'
    });
  } catch (error) {
    console.error('Update ticket status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update ticket status'
    });
  }
});

module.exports = router;