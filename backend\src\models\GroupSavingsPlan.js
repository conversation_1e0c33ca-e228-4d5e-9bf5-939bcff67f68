const mongoose = require('mongoose');

const groupSavingsPlanSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    required: true,
    maxlength: 500
  },
  category: {
    type: String,
    required: true,
    enum: ['electronics', 'car', 'phone', 'grocery', 'education', 'health', 'travel', 'business', 'other']
  },
  targetAmount: {
    type: Number,
    required: true,
    min: 1000
  },
  contributionAmount: {
    type: Number,
    required: true,
    min: 100
  },
  currentAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  maxMembers: {
    type: Number,
    required: true,
    min: 2,
    max: 50
  },
  currentMembers: {
    type: Number,
    default: 1,
    min: 1
  },
  inviteCode: {
    type: String,
    unique: true,
    required: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'completed', 'cancelled', 'paused'],
    default: 'active'
  },
  rules: {
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly'],
      default: 'monthly'
    },
    minContribution: {
      type: Number,
      default: function() { return this.contributionAmount * 0.5; }
    },
    maxContribution: {
      type: Number,
      default: function() { return this.contributionAmount * 2; }
    },
    penaltyRate: {
      type: Number,
      default: 5,
      min: 0,
      max: 20
    },
    autoContribution: {
      type: Boolean,
      default: false
    }
  },
  metadata: {
    totalContributions: {
      type: Number,
      default: 0
    },
    averageContribution: {
      type: Number,
      default: 0
    },
    completionPercentage: {
      type: Number,
      default: 0
    },
    lastContributionDate: Date,
    nextContributionDue: Date
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for progress percentage
groupSavingsPlanSchema.virtual('progressPercentage').get(function() {
  return Math.round((this.currentAmount / this.targetAmount) * 100);
});

// Virtual for remaining amount
groupSavingsPlanSchema.virtual('remainingAmount').get(function() {
  return Math.max(0, this.targetAmount - this.currentAmount);
});

// Virtual for days remaining
groupSavingsPlanSchema.virtual('daysRemaining').get(function() {
  const now = new Date();
  const end = new Date(this.endDate);
  const diffTime = end - now;
  return Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
});

// Pre-save middleware to generate invite code
groupSavingsPlanSchema.pre('save', async function(next) {
  if (this.isNew && !this.inviteCode) {
    this.inviteCode = await this.generateInviteCode();
  }
  
  // Update completion percentage
  this.metadata.completionPercentage = this.progressPercentage;
  
  next();
});

// Instance method to generate invite code
groupSavingsPlanSchema.methods.generateInviteCode = async function() {
  let code;
  let isUnique = false;
  
  while (!isUnique) {
    code = Math.random().toString(36).substring(2, 8).toUpperCase();
    const existing = await this.constructor.findOne({ inviteCode: code });
    if (!existing) isUnique = true;
  }
  
  return code;
};

// Instance method to check if group is full
groupSavingsPlanSchema.methods.isFull = function() {
  return this.currentMembers >= this.maxMembers;
};

// Instance method to check if group is active
groupSavingsPlanSchema.methods.isActive = function() {
  return this.status === 'active' && new Date() <= this.endDate;
};

// Static method to find by invite code
groupSavingsPlanSchema.statics.findByInviteCode = function(code) {
  return this.findOne({ inviteCode: code.toUpperCase() });
};

module.exports = mongoose.model('GroupSavingsPlan', groupSavingsPlanSchema);