const mongoose = require('mongoose');

const groupPostSchema = new mongoose.Schema({
  groupId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'GroupSavingsPlan',
    required: true
  },
  authorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  content: {
    type: String,
    required: true,
    maxlength: 1000
  },
  type: {
    type: String,
    enum: ['text', 'achievement', 'milestone', 'reminder'],
    default: 'text'
  },
  likes: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    likedAt: {
      type: Date,
      default: Date.now
    }
  }],
  comments: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    content: {
      type: String,
      required: true,
      maxlength: 500
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  isEdited: {
    type: Boolean,
    default: false
  },
  editedAt: {
    type: Date
  },
  isPinned: {
    type: Boolean,
    default: false
  },
  metadata: {
    mentionedUsers: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }],
    hashtags: [String],
    attachments: [{
      type: String,
      url: String,
      filename: String
    }]
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for like count
groupPostSchema.virtual('likeCount').get(function() {
  return this.likes.length;
});

// Virtual for comment count
groupPostSchema.virtual('commentCount').get(function() {
  return this.comments.length;
});

// Indexes
groupPostSchema.index({ groupId: 1, createdAt: -1 });
groupPostSchema.index({ authorId: 1, createdAt: -1 });
groupPostSchema.index({ isPinned: -1, createdAt: -1 });

// Check if user liked the post
groupPostSchema.methods.isLikedBy = function(userId) {
  return this.likes.some(like => like.userId.toString() === userId.toString());
};

// Toggle like
groupPostSchema.methods.toggleLike = function(userId) {
  const likeIndex = this.likes.findIndex(like => like.userId.toString() === userId.toString());
  
  if (likeIndex > -1) {
    this.likes.splice(likeIndex, 1);
  } else {
    this.likes.push({ userId });
  }
  
  return this.save();
};

// Add comment
groupPostSchema.methods.addComment = function(userId, content) {
  this.comments.push({ userId, content });
  return this.save();
};

// Edit post
groupPostSchema.methods.editContent = function(newContent) {
  this.content = newContent;
  this.isEdited = true;
  this.editedAt = new Date();
  return this.save();
};

module.exports = mongoose.model('GroupPost', groupPostSchema);