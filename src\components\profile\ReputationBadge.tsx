import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Trophy, Star, TrendingUp, Award, Target } from 'lucide-react';

interface ReputationData {
  score: number;
  totalContributions: number;
  onTimePayments: number;
  completedGroups: number;
  averageRating: number;
  achievements: string[];
}

interface ReputationBadgeProps {
  score: number;
  data?: ReputationData;
  showDetails?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export const ReputationBadge: React.FC<ReputationBadgeProps> = ({
  score,
  data,
  showDetails = false,
  size = 'md'
}) => {
  const getReputationLevel = (score: number) => {
    if (score >= 95) return {
      level: 'Diamond Elite',
      color: 'bg-gradient-to-r from-purple-500 to-pink-500',
      textColor: 'text-white',
      icon: '💎',
      description: 'Exceptional reliability and commitment'
    };
    if (score >= 90) return {
      level: 'Platinum',
      color: 'bg-gradient-to-r from-gray-400 to-gray-600',
      textColor: 'text-white',
      icon: '🏆',
      description: 'Outstanding track record'
    };
    if (score >= 80) return {
      level: 'Gold',
      color: 'bg-gradient-to-r from-yellow-400 to-yellow-600',
      textColor: 'text-white',
      icon: '🥇',
      description: 'Highly reliable member'
    };
    if (score >= 70) return {
      level: 'Silver',
      color: 'bg-gradient-to-r from-gray-300 to-gray-500',
      textColor: 'text-white',
      icon: '🥈',
      description: 'Reliable and trustworthy'
    };
    if (score >= 60) return {
      level: 'Bronze',
      color: 'bg-gradient-to-r from-orange-400 to-orange-600',
      textColor: 'text-white',
      icon: '🥉',
      description: 'Good standing member'
    };
    if (score >= 40) return {
      level: 'Iron',
      color: 'bg-gradient-to-r from-gray-500 to-gray-700',
      textColor: 'text-white',
      icon: '⚡',
      description: 'Building reputation'
    };
    return {
      level: 'Newcomer',
      color: 'bg-gradient-to-r from-blue-400 to-blue-600',
      textColor: 'text-white',
      icon: '⭐',
      description: 'Welcome to Better Interest!'
    };
  };

  const reputation = getReputationLevel(score);
  
  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1',
    lg: 'text-base px-4 py-2'
  };

  if (!showDetails) {
    return (
      <Badge 
        className={`${reputation.color} ${reputation.textColor} ${sizeClasses[size]} font-medium border-0`}
        title={`Reputation Score: ${score}% - ${reputation.description}`}
      >
        <span className="mr-1">{reputation.icon}</span>
        {reputation.level}
      </Badge>
    );
  }

  if (!data) return null;

  const nextLevel = getReputationLevel(score + 10);
  const pointsToNext = Math.ceil((nextLevel.level !== reputation.level ? 
    (Math.ceil(score / 10) * 10 + 10) - score : 0));

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Trophy className="h-5 w-5 text-yellow-500" />
            Reputation Profile
          </CardTitle>
          <Badge 
            className={`${reputation.color} ${reputation.textColor} font-medium border-0`}
          >
            <span className="mr-1">{reputation.icon}</span>
            {reputation.level}
          </Badge>
        </div>
        <CardDescription>{reputation.description}</CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Score Progress */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Reputation Score</span>
            <span className="text-lg font-bold text-primary">{score}%</span>
          </div>
          <Progress value={score} className="h-3" />
          {pointsToNext > 0 && (
            <p className="text-xs text-muted-foreground">
              {pointsToNext} points to {nextLevel.level}
            </p>
          )}
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-1">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Target className="h-4 w-4" />
              <span>Total Contributions</span>
            </div>
            <p className="font-semibold">{data.totalContributions}</p>
          </div>
          
          <div className="space-y-1">
            <div className="flex items-center gap-2 text-muted-foreground">
              <TrendingUp className="h-4 w-4" />
              <span>On-time Payments</span>
            </div>
            <p className="font-semibold">
              {data.onTimePayments}/{data.totalContributions}
            </p>
          </div>

          <div className="space-y-1">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Award className="h-4 w-4" />
              <span>Completed Groups</span>
            </div>
            <p className="font-semibold">{data.completedGroups}</p>
          </div>

          <div className="space-y-1">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Star className="h-4 w-4" />
              <span>Average Rating</span>
            </div>
            <p className="font-semibold">{data.averageRating.toFixed(1)}/5.0</p>
          </div>
        </div>

        {/* Achievements */}
        {data.achievements.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium flex items-center gap-2">
              <Award className="h-4 w-4" />
              Recent Achievements
            </h4>
            <div className="space-y-1">
              {data.achievements.slice(0, 3).map((achievement, index) => (
                <div key={index} className="flex items-center gap-2 text-xs">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>{achievement}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Reputation Factors */}
        <div className="space-y-2 pt-2 border-t">
          <h4 className="text-sm font-medium">How we calculate your score:</h4>
          <div className="space-y-1 text-xs text-muted-foreground">
            <div className="flex justify-between">
              <span>• Payment punctuality</span>
              <span>40%</span>
            </div>
            <div className="flex justify-between">
              <span>• Group completion rate</span>
              <span>30%</span>
            </div>
            <div className="flex justify-between">
              <span>• Community feedback</span>
              <span>20%</span>
            </div>
            <div className="flex justify-between">
              <span>• Account activity</span>
              <span>10%</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};