
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    Card<PERSON>ooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    <PERSON>alog<PERSON>eader,
    <PERSON>alogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Ta<PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    TabsList,
    TabsTrigger,
} from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Car, Plus, ShoppingBag, ShoppingCart, Smartphone, UserPlus, Users } from "lucide-react";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";

// Define types
interface GroupSavingsPlan {
  id: string;
  name: string;
  description: string;
  category: string;
  target_amount: number;
  contribution_amount: number;
  start_date: string;
  end_date: string;
  status: string;
  max_members: number;
  current_members: number;
  created_at: string;
}

interface GroupMember {
  id: string;
  user_id: string;
  name: string;
  email: string;
  contribution_amount: number;
  joined_at: string;
  role: string;
}

const GroupSavingsPlans = () => {
  const [plans, setPlans] = useState<GroupSavingsPlan[]>([]);
  const [members, setMembers] = useState<GroupMember[]>([]);
  const [activeTab, setActiveTab] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [addMemberDialogOpen, setAddMemberDialogOpen] = useState(false);
  const [viewMembersDialogOpen, setViewMembersDialogOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<GroupSavingsPlan | null>(null);
  
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    category: "electronics",
    targetAmount: 0,
    contributionAmount: 0,
    startDate: "",
    endDate: "",
    maxMembers: 10,
    isActive: true
  });

  const [memberFormData, setMemberFormData] = useState({
    userId: "",
    email: "",
    contributionAmount: 0,
    role: "member"
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    setFormData({
      ...formData,
      [name]: type === "number" ? parseFloat(value) : value
    });
  };

  const handleMemberInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    setMemberFormData({
      ...memberFormData,
      [name]: type === "number" ? parseFloat(value) : value
    });
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleMemberSelectChange = (name: string, value: string) => {
    setMemberFormData({
      ...memberFormData,
      [name]: value
    });
  };

  const fetchGroupSavingsPlans = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/v1/admin/group-savings', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch group savings plans');
      }

      const data = await response.json();
      setGroupSavingsPlans(data.data || []);
    } catch (error) {
      console.error("Error fetching group savings plans:", error);
      toast.error("Failed to load group savings plans");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchGroupMembers = async (planId: string) => {
    try {
      const response = await fetch(`/api/v1/admin/group-savings/${planId}/members`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch group members');
      }

      const data = await response.json();
      setMembers(data.data || []);
    } catch (error) {
      console.error("Error fetching group members:", error);
      toast.error("Failed to load group members");
    }
  };

  useEffect(() => {
    fetchGroupSavingsPlans();
  }, []);

  const handleCreatePlan = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/v1/group-savings', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          adminId: localStorage.getItem('userId') // Assuming user ID is stored
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create group savings plan');
      }

      const result = await response.json();

      toast.success("Group savings plan created successfully");
      setCreateDialogOpen(false);
      await fetchGroupSavingsPlans();

      // Reset form
      setFormData({
        name: "",
        description: "",
        category: "electronics",
        targetAmount: 0,
        contributionAmount: 0,
        startDate: "",
        endDate: "",
        maxMembers: 10,
        isActive: true
      });
    } catch (error) {
      console.error("Error creating group savings plan:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create group savings plan");
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddMember = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedPlan) return;
    
    setIsLoading(true);
    
    try {
      // In a real app, this would call a proper API endpoint
      toast.success("Member added to group successfully");
      setAddMemberDialogOpen(false);
      
      // Reset form
      setMemberFormData({
        userId: "",
        email: "",
        contributionAmount: 0,
        role: "member"
      });
    } catch (error) {
      console.error("Error adding member:", error);
      toast.error("Failed to add member to group");
    } finally {
      setIsLoading(false);
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "electronics":
        return <ShoppingCart className="h-5 w-5 text-blue-500" />;
      case "car":
        return <Car className="h-5 w-5 text-green-500" />;
      case "phone":
        return <Smartphone className="h-5 w-5 text-purple-500" />;
      case "grocery":
        return <ShoppingBag className="h-5 w-5 text-yellow-500" />;
      default:
        return <ShoppingCart className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500">Active</Badge>;
      case "completed":
        return <Badge className="bg-blue-500">Completed</Badge>;
      case "pending":
        return <Badge className="bg-yellow-500">Pending</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  const filteredPlans = plans.filter(plan => {
    const matchesSearch = 
      plan.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      plan.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      plan.category.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesTab = 
      activeTab === "all" || 
      (activeTab === "active" && plan.status === "active") ||
      (activeTab === "completed" && plan.status === "completed") ||
      (activeTab === plan.category);
    
    return matchesSearch && matchesTab;
  });

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Group Savings Plans</h2>
          <p className="text-muted-foreground">
            Manage group savings plans for different goods and products
          </p>
        </div>
        
        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-brand-blue text-white">
              <Plus className="mr-2 h-4 w-4" /> Create Plan
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Create New Group Savings Plan</DialogTitle>
              <DialogDescription>
                Set up a new group savings plan for collective purchasing
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleCreatePlan} className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="name">Plan Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select 
                  value={formData.category} 
                  onValueChange={(value) => handleSelectChange("category", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="electronics">Electronics & Appliances</SelectItem>
                    <SelectItem value="car">Cars & Vehicles</SelectItem>
                    <SelectItem value="phone">Phones & Gadgets</SelectItem>
                    <SelectItem value="grocery">Groceries & Food</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="targetAmount">Target Amount (₦)</Label>
                  <Input
                    id="targetAmount"
                    name="targetAmount"
                    type="number"
                    value={formData.targetAmount || ""}
                    onChange={handleInputChange}
                    min={1000}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="contributionAmount">Contribution Amount (₦)</Label>
                  <Input
                    id="contributionAmount"
                    name="contributionAmount"
                    type="number"
                    value={formData.contributionAmount || ""}
                    onChange={handleInputChange}
                    min={100}
                    required
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Start Date</Label>
                  <Input
                    id="startDate"
                    name="startDate"
                    type="date"
                    value={formData.startDate}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="endDate">End Date</Label>
                  <Input
                    id="endDate"
                    name="endDate"
                    type="date"
                    value={formData.endDate}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="maxMembers">Maximum Members</Label>
                <Input
                  id="maxMembers"
                  name="maxMembers"
                  type="number"
                  value={formData.maxMembers}
                  onChange={handleInputChange}
                  min={2}
                  required
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData({...formData, isActive: checked})}
                />
                <Label htmlFor="isActive">Activate Immediately</Label>
              </div>
              
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" className="bg-brand-blue text-white">
                  {isLoading ? "Creating..." : "Create Plan"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex flex-col space-y-4">
        <div className="flex justify-between items-center">
          <Tabs 
            defaultValue="all" 
            value={activeTab} 
            onValueChange={setActiveTab}
            className="w-full"
          >
            <div className="flex justify-between items-center mb-4">
              <TabsList>
                <TabsTrigger value="all">All Plans</TabsTrigger>
                <TabsTrigger value="active">Active</TabsTrigger>
                <TabsTrigger value="completed">Completed</TabsTrigger>
                <TabsTrigger value="electronics">Electronics</TabsTrigger>
                <TabsTrigger value="grocery">Groceries</TabsTrigger>
              </TabsList>
              
              <div className="w-[250px]">
                <Input
                  placeholder="Search plans..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            
            <TabsContent value={activeTab} className="m-0">
              {isLoading ? (
                <div className="flex justify-center items-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-brand-blue border-solid"></div>
                </div>
              ) : filteredPlans.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredPlans.map((plan) => (
                    <Card key={plan.id} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <div className="flex items-center">
                            {getCategoryIcon(plan.category)}
                            <CardTitle className="ml-2 text-lg">{plan.name}</CardTitle>
                          </div>
                          {getStatusBadge(plan.status)}
                        </div>
                        <CardDescription>{plan.description}</CardDescription>
                      </CardHeader>
                      <CardContent className="pb-2">
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Target Amount:</span>
                            <span className="font-semibold">₦{plan.target_amount.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Contribution:</span>
                            <span className="font-semibold">₦{plan.contribution_amount.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Members:</span>
                            <span className="font-semibold">{plan.current_members}/{plan.max_members}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Period:</span>
                            <span className="font-semibold">{plan.start_date} to {plan.end_date}</span>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="pt-2 flex justify-between">
                        <Button 
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedPlan(plan);
                            fetchGroupMembers(plan.id);
                            setViewMembersDialogOpen(true);
                          }}
                        >
                          <Users className="h-4 w-4 mr-1" /> View Members
                        </Button>
                        <Button 
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedPlan(plan);
                            setMemberFormData({
                              ...memberFormData,
                              contributionAmount: plan.contribution_amount
                            });
                            setAddMemberDialogOpen(true);
                          }}
                          disabled={plan.current_members >= plan.max_members || plan.status !== "active"}
                        >
                          <UserPlus className="h-4 w-4 mr-1" /> Add Member
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No group savings plans found. Create your first plan to get started.</p>
                  <Button
                    className="mt-4 bg-brand-blue text-white"
                    onClick={() => setCreateDialogOpen(true)}
                  >
                    <Plus className="mr-2 h-4 w-4" /> Create Plan
                  </Button>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Add Member Dialog */}
      <Dialog open={addMemberDialogOpen} onOpenChange={setAddMemberDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add Member to Group</DialogTitle>
            <DialogDescription>
              {selectedPlan && (
                <span>Add a new member to "{selectedPlan.name}" savings group</span>
              )}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleAddMember} className="space-y-4 pt-4">
            <div className="space-y-2">
              <Label htmlFor="email">Member Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={memberFormData.email}
                onChange={handleMemberInputChange}
                placeholder="Enter user email"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="contributionAmount">Contribution Amount (₦)</Label>
              <Input
                id="contributionAmount"
                name="contributionAmount"
                type="number"
                value={memberFormData.contributionAmount || ""}
                onChange={handleMemberInputChange}
                min={100}
                required
              />
              {selectedPlan && memberFormData.contributionAmount !== selectedPlan.contribution_amount && (
                <p className="text-xs text-amber-600">
                  This amount differs from the default group contribution of ₦{selectedPlan.contribution_amount}
                </p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="role">Member Role</Label>
              <Select 
                value={memberFormData.role} 
                onValueChange={(value) => handleMemberSelectChange("role", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="admin">Group Admin</SelectItem>
                  <SelectItem value="member">Regular Member</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setAddMemberDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" className="bg-brand-blue text-white">
                {isLoading ? "Adding..." : "Add Member"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* View Members Dialog */}
      <Dialog open={viewMembersDialogOpen} onOpenChange={setViewMembersDialogOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>Group Members</DialogTitle>
            <DialogDescription>
              {selectedPlan && (
                <span>Members of "{selectedPlan.name}" savings group</span>
              )}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Contribution</TableHead>
                  <TableHead>Joined</TableHead>
                  <TableHead>Role</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {members.map((member) => (
                  <TableRow key={member.id}>
                    <TableCell className="font-medium">{member.name}</TableCell>
                    <TableCell>{member.email}</TableCell>
                    <TableCell>₦{member.contribution_amount.toLocaleString()}</TableCell>
                    <TableCell>{new Date(member.joined_at).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <Badge variant={member.role === "admin" ? "default" : "outline"}>
                        {member.role === "admin" ? "Admin" : "Member"}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          <DialogFooter>
            <Button
              onClick={() => {
                if (selectedPlan) {
                  setMemberFormData({
                    ...memberFormData,
                    contributionAmount: selectedPlan.contribution_amount
                  });
                  setViewMembersDialogOpen(false);
                  setAddMemberDialogOpen(true);
                }
              }}
              className="bg-brand-blue text-white"
              disabled={!selectedPlan || selectedPlan.current_members >= selectedPlan.max_members || selectedPlan.status !== "active"}
            >
              <UserPlus className="mr-2 h-4 w-4" /> Add Member
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default GroupSavingsPlans;
