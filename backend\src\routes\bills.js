const express = require('express');
const router = express.Router();
const { auth, admin } = require('../middleware/auth');
const BillProvider = require('../models/BillProvider');
const BillPayment = require('../models/BillPayment');
const User = require('../models/User');
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');

// Get all active bill providers
router.get('/providers', auth, async (req, res) => {
  try {
    const providers = await BillProvider.find({ isActive: true })
      .select('-apiKey -secretKey')
      .sort({ category: 1, name: 1 });
    
    res.json({ success: true, data: providers });
  } catch (error) {
    console.error('Error fetching providers:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch providers' });
  }
});

// Get bill payment history
router.get('/payments', auth, async (req, res) => {
  try {
    const { page = 1, limit = 20, status } = req.query;
    const filter = { userId: req.user.id };
    
    if (status) filter.status = status;
    
    const payments = await BillPayment.find(filter)
      .populate('providerId', 'name category')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await BillPayment.countDocuments(filter);
    
    res.json({
      success: true,
      data: payments,
      pagination: {
        current: page,
        total: Math.ceil(total / limit),
        count: payments.length
      }
    });
  } catch (error) {
    console.error('Error fetching payments:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch payments' });
  }
});

// Verify customer (phone, meter number, etc.)
router.post('/verify-customer', auth, async (req, res) => {
  try {
    const { providerId, customerIdentifier, category } = req.body;
    
    const provider = await BillProvider.findById(providerId);
    if (!provider) {
      return res.status(404).json({ success: false, message: 'Provider not found' });
    }
    
    // Mock verification for demo - replace with actual API calls
    let customerInfo = {
      identifier: customerIdentifier,
      name: 'Demo Customer',
      address: 'Demo Address',
      isValid: true
    };
    
    if (category === 'airtime' || category === 'data') {
      // Validate phone number
      const phoneRegex = /^(\+234|0)[789][01]\d{8}$/;
      if (!phoneRegex.test(customerIdentifier)) {
        return res.status(400).json({ success: false, message: 'Invalid phone number' });
      }
    }
    
    res.json({ success: true, data: customerInfo });
  } catch (error) {
    console.error('Error verifying customer:', error);
    res.status(500).json({ success: false, message: 'Failed to verify customer' });
  }
});

// Initiate bill payment
router.post('/pay', auth, async (req, res) => {
  try {
    const { providerId, amount, customerIdentifier, category, metadata = {} } = req.body;
    
    const provider = await BillProvider.findById(providerId);
    if (!provider) {
      return res.status(404).json({ success: false, message: 'Provider not found' });
    }
    
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }
    
    // Calculate fee
    const fee = provider.feeType === 'percentage' 
      ? (amount * provider.fee) / 100 
      : provider.fee;
    const totalAmount = amount + fee;
    
    // Check user balance
    if (user.balance < totalAmount) {
      return res.status(400).json({ success: false, message: 'Insufficient balance' });
    }
    
    // Generate reference
    const reference = `BILL_${Date.now()}_${uuidv4().slice(0, 8)}`;
    
    // Create payment record
    const payment = new BillPayment({
      userId: req.user.id,
      providerId,
      category,
      amount,
      fee,
      totalAmount,
      reference,
      customerIdentifier,
      metadata
    });
    
    await payment.save();
    
    // Deduct from user balance
    user.balance -= totalAmount;
    await user.save();
    
    // Process payment with provider (mock for demo)
    try {
      // Mock successful payment
      payment.status = 'completed';
      payment.providerReference = `PROV_${Date.now()}`;
      payment.completedAt = new Date();
      payment.customerName = metadata.customerName || 'Demo Customer';
      
      await payment.save();
      
      res.json({ 
        success: true, 
        data: payment,
        message: 'Payment completed successfully'
      });
    } catch (providerError) {
      // Refund user if provider payment fails
      user.balance += totalAmount;
      await user.save();
      
      payment.status = 'failed';
      payment.errorMessage = providerError.message;
      payment.failedAt = new Date();
      await payment.save();
      
      res.status(400).json({ 
        success: false, 
        message: 'Payment failed at provider',
        error: providerError.message 
      });
    }
  } catch (error) {
    console.error('Error processing payment:', error);
    res.status(500).json({ success: false, message: 'Failed to process payment' });
  }
});

// Get payment status
router.get('/payments/:reference', auth, async (req, res) => {
  try {
    const payment = await BillPayment.findOne({ 
      reference: req.params.reference,
      userId: req.user.id 
    }).populate('providerId', 'name category');
    
    if (!payment) {
      return res.status(404).json({ success: false, message: 'Payment not found' });
    }
    
    res.json({ success: true, data: payment });
  } catch (error) {
    console.error('Error fetching payment:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch payment' });
  }
});

// Admin routes
// Get all providers (including sensitive data)
router.get('/admin/providers', [auth, admin], async (req, res) => {
  try {
    const providers = await BillProvider.find().sort({ category: 1, name: 1 });
    res.json({ success: true, data: providers });
  } catch (error) {
    console.error('Error fetching providers:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch providers' });
  }
});

// Create new provider
router.post('/admin/providers', [auth, admin], async (req, res) => {
  try {
    const provider = new BillProvider(req.body);
    await provider.save();
    res.status(201).json({ success: true, data: provider });
  } catch (error) {
    console.error('Error creating provider:', error);
    res.status(500).json({ success: false, message: 'Failed to create provider' });
  }
});

// Update provider
router.put('/admin/providers/:id', [auth, admin], async (req, res) => {
  try {
    const provider = await BillProvider.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    if (!provider) {
      return res.status(404).json({ success: false, message: 'Provider not found' });
    }
    
    res.json({ success: true, data: provider });
  } catch (error) {
    console.error('Error updating provider:', error);
    res.status(500).json({ success: false, message: 'Failed to update provider' });
  }
});

// Delete provider
router.delete('/admin/providers/:id', [auth, admin], async (req, res) => {
  try {
    const provider = await BillProvider.findByIdAndDelete(req.params.id);
    
    if (!provider) {
      return res.status(404).json({ success: false, message: 'Provider not found' });
    }
    
    res.json({ success: true, message: 'Provider deleted successfully' });
  } catch (error) {
    console.error('Error deleting provider:', error);
    res.status(500).json({ success: false, message: 'Failed to delete provider' });
  }
});

// Get all payments (admin)
router.get('/admin/payments', [auth, admin], async (req, res) => {
  try {
    const { page = 1, limit = 50, status, category } = req.query;
    const filter = {};
    
    if (status) filter.status = status;
    if (category) filter.category = category;
    
    const payments = await BillPayment.find(filter)
      .populate('userId', 'name email phone')
      .populate('providerId', 'name category')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await BillPayment.countDocuments(filter);
    
    res.json({
      success: true,
      data: payments,
      pagination: {
        current: page,
        total: Math.ceil(total / limit),
        count: payments.length
      }
    });
  } catch (error) {
    console.error('Error fetching payments:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch payments' });
  }
});

module.exports = router;