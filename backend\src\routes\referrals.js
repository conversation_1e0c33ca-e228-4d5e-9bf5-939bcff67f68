const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const User = require('../models/User');
const Referral = require('../models/Referral');
const Notification = require('../models/Notification');

// Get user's referral dashboard
router.get('/dashboard', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    
    // Get referral stats
    const stats = await Referral.getReferralStats(req.user.id);
    const referrals = await Referral.find({ referrerId: req.user.id })
      .populate('referredUserId', 'firstName lastName email createdAt')
      .sort({ createdAt: -1 });
    
    // Calculate tier based on referral count
    const totalReferrals = user.totalReferrals || 0;
    let tier = 'bronze';
    if (totalReferrals >= 50) tier = 'platinum';
    else if (totalReferrals >= 20) tier = 'gold';
    else if (totalReferrals >= 10) tier = 'silver';
    
    const dashboard = {
      referralCode: user.referralCode,
      totalReferrals: user.totalReferrals || 0,
      totalEarnings: user.referralEarnings || 0,
      tier,
      referralLink: `${process.env.FRONTEND_URL}/signup?ref=${user.referralCode}`,
      stats,
      recentReferrals: referrals.slice(0, 10),
      nextTierRequirement: getNextTierRequirement(tier, totalReferrals)
    };
    
    res.json({
      success: true,
      data: dashboard
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching referral dashboard',
      error: error.message
    });
  }
});

// Apply referral code during signup
router.post('/apply', async (req, res) => {
  try {
    const { referralCode, newUserId } = req.body;
    
    if (!referralCode || !newUserId) {
      return res.status(400).json({
        success: false,
        message: 'Referral code and user ID are required'
      });
    }
    
    // Find referrer by code
    const referrer = await User.findByReferralCode(referralCode);
    if (!referrer) {
      return res.status(404).json({
        success: false,
        message: 'Invalid referral code'
      });
    }
    
    // Check if user is trying to refer themselves
    if (referrer._id.toString() === newUserId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot refer yourself'
      });
    }
    
    // Check if referral already exists
    const existingReferral = await Referral.findOne({
      referrerId: referrer._id,
      referredUserId: newUserId
    });
    
    if (existingReferral) {
      return res.status(400).json({
        success: false,
        message: 'Referral already exists'
      });
    }
    
    // Create referral record
    const referral = new Referral({
      referrerId: referrer._id,
      referredUserId: newUserId,
      referralCode: referralCode.toUpperCase(),
      metadata: {
        referralSource: 'link',
        tier: 'bronze'
      }
    });
    
    await referral.save();
    
    // Update referred user
    await User.findByIdAndUpdate(newUserId, {
      referredBy: referrer._id
    });
    
    // Send notification to referrer
    await Notification.create({
      userId: referrer._id,
      type: 'referral_bonus',
      title: 'New Referral!',
      message: `Someone signed up using your referral code. You'll earn a bonus once they complete their first transaction.`,
      data: { referralId: referral._id }
    });
    
    res.json({
      success: true,
      message: 'Referral applied successfully',
      data: { referralId: referral._id }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error applying referral',
      error: error.message
    });
  }
});

// Complete referral (called when referred user makes first transaction)
router.post('/complete/:referralId', auth, async (req, res) => {
  try {
    const referral = await Referral.findById(req.params.referralId);
    
    if (!referral) {
      return res.status(404).json({
        success: false,
        message: 'Referral not found'
      });
    }
    
    if (referral.status === 'completed') {
      return res.status(400).json({
        success: false,
        message: 'Referral already completed'
      });
    }
    
    // Complete the referral
    await referral.complete();
    
    // Send notification to referrer
    await Notification.create({
      userId: referral.referrerId,
      type: 'referral_bonus',
      title: 'Referral Bonus Earned!',
      message: `You've earned ₦${referral.rewardAmount.toLocaleString()} from your referral!`,
      data: { 
        referralId: referral._id,
        amount: referral.rewardAmount
      }
    });
    
    res.json({
      success: true,
      message: 'Referral completed successfully',
      data: referral
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error completing referral',
      error: error.message
    });
  }
});

// Get referral leaderboard
router.get('/leaderboard', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    const leaderboard = await Referral.getTopReferrers(limit);
    
    res.json({
      success: true,
      data: leaderboard
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching leaderboard',
      error: error.message
    });
  }
});

// Validate referral code
router.get('/validate/:code', async (req, res) => {
  try {
    const { code } = req.params;
    
    const referrer = await User.findByReferralCode(code);
    
    if (!referrer) {
      return res.status(404).json({
        success: false,
        message: 'Invalid referral code'
      });
    }
    
    res.json({
      success: true,
      data: {
        valid: true,
        referrerName: referrer.fullName,
        referrerEmail: referrer.email
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error validating referral code',
      error: error.message
    });
  }
});

function getNextTierRequirement(currentTier, currentReferrals) {
  const tiers = {
    bronze: { next: 'silver', required: 10 },
    silver: { next: 'gold', required: 20 },
    gold: { next: 'platinum', required: 50 },
    platinum: { next: null, required: null }
  };
  
  const tier = tiers[currentTier];
  if (!tier.next) return null;
  
  return {
    nextTier: tier.next,
    referralsNeeded: tier.required - currentReferrals
  };
}

module.exports = router;