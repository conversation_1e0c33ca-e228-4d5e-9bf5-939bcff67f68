import * as React from "react";
import { cn } from "@/lib/utils";

interface FintechCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "glassmorphic" | "gradient";
  children: React.ReactNode;
}

const FintechCard = React.forwardRef<HTMLDivElement, FintechCardProps>(
  ({ className, variant = "default", children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "rounded-none border-0 transition-all duration-300",
          {
            "bg-background text-card-foreground shadow-[inset_8px_8px_16px_rgba(0,0,0,0.1),inset_-8px_-8px_16px_rgba(255,255,255,0.1)] hover:shadow-[inset_12px_12px_24px_rgba(0,0,0,0.15),inset_-12px_-12px_24px_rgba(255,255,255,0.15)]": variant === "default",
            "bg-background shadow-[inset_8px_8px_16px_rgba(0,0,0,0.1),inset_-8px_-8px_16px_rgba(255,255,255,0.1)] hover:shadow-[inset_12px_12px_24px_rgba(0,0,0,0.15),inset_-12px_-12px_24px_rgba(255,255,255,0.15)] dark:shadow-[inset_8px_8px_16px_rgba(0,0,0,0.3),inset_-8px_-8px_16px_rgba(255,255,255,0.02)]": variant === "glassmorphic",
            "bg-background shadow-[inset_6px_6px_12px_rgba(0,0,0,0.15),inset_-6px_-6px_12px_rgba(255,255,255,0.1)] hover:shadow-[inset_10px_10px_20px_rgba(0,0,0,0.2),inset_-10px_-10px_20px_rgba(255,255,255,0.15)]": variant === "gradient",
          },
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);
FintechCard.displayName = "FintechCard";

export { FintechCard };