import { apiService } from './api';

export interface SavingsPlan {
  id: string;
  name: string;
  description: string;
  targetAmount: number;
  currentAmount: number;
  interestRate: number;
  duration: number; // in months
  type: 'flex' | 'fixed' | 'safelock';
  isActive: boolean;
  userId: string;
  createdAt: string;
  updatedAt: string;
  maturityDate?: string;
}

export interface Transaction {
  id: string;
  type: 'deposit' | 'withdrawal' | 'interest' | 'penalty';
  amount: number;
  description: string;
  status: 'pending' | 'completed' | 'failed';
  userId: string;
  savingsPlanId?: string;
  createdAt: string;
  updatedAt: string;
  reference?: string;
}

export interface SavingsGoal {
  id: string;
  title: string;
  targetAmount: number;
  currentAmount: number;
  targetDate: string;
  description?: string;
  userId: string;
  isCompleted: boolean;
  createdAt: string;
  updatedAt: string;
}

export class SavingsService {
  // Savings Plans
  async getSavingsPlans(): Promise<SavingsPlan[]> {
    try {
      return await apiService.get<SavingsPlan[]>('/savings/plans');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch savings plans');
    }
  }

  async createSavingsPlan(planData: Omit<SavingsPlan, 'id' | 'createdAt' | 'updatedAt' | 'userId'>): Promise<SavingsPlan> {
    try {
      return await apiService.post<SavingsPlan>('/savings/plans', planData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create savings plan');
    }
  }

  async updateSavingsPlan(id: string, planData: Partial<SavingsPlan>): Promise<SavingsPlan> {
    try {
      return await apiService.put<SavingsPlan>(`/savings/plans/${id}`, planData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update savings plan');
    }
  }

  async deleteSavingsPlan(id: string): Promise<void> {
    try {
      await apiService.delete(`/savings/plans/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete savings plan');
    }
  }

  // Transactions
  async getTransactions(limit?: number, offset?: number): Promise<Transaction[]> {
    try {
      const params = new URLSearchParams();
      if (limit) params.append('limit', limit.toString());
      if (offset) params.append('offset', offset.toString());
      
      return await apiService.get<Transaction[]>(`/savings/transactions?${params}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch transactions');
    }
  }

  async createDeposit(amount: number, savingsPlanId?: string): Promise<Transaction> {
    try {
      return await apiService.post<Transaction>('/savings/deposit', {
        amount,
        savingsPlanId,
      });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Deposit failed');
    }
  }

  async createWithdrawal(amount: number, savingsPlanId?: string): Promise<Transaction> {
    try {
      return await apiService.post<Transaction>('/savings/withdrawal', {
        amount,
        savingsPlanId,
      });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Withdrawal failed');
    }
  }

  // Savings Goals
  async getSavingsGoals(): Promise<SavingsGoal[]> {
    try {
      return await apiService.get<SavingsGoal[]>('/savings/goals');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch savings goals');
    }
  }

  async createSavingsGoal(goalData: Omit<SavingsGoal, 'id' | 'createdAt' | 'updatedAt' | 'userId' | 'isCompleted'>): Promise<SavingsGoal> {
    try {
      return await apiService.post<SavingsGoal>('/savings/goals', goalData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create savings goal');
    }
  }

  async updateSavingsGoal(id: string, goalData: Partial<SavingsGoal>): Promise<SavingsGoal> {
    try {
      return await apiService.put<SavingsGoal>(`/savings/goals/${id}`, goalData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update savings goal');
    }
  }

  async deleteSavingsGoal(id: string): Promise<void> {
    try {
      await apiService.delete(`/savings/goals/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete savings goal');
    }
  }

  // Dashboard Data
  async getDashboardData(): Promise<{
    totalBalance: number;
    totalSavings: number;
    activeGoals: number;
    recentTransactions: Transaction[];
    savingsGrowth: { month: string; amount: number }[];
  }> {
    try {
      return await apiService.get('/savings/dashboard');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch dashboard data');
    }
  }
}

export const savingsService = new SavingsService();