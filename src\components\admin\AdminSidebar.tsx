import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { 
  LayoutDashboard, 
  Users, 
  CreditCard, 
  FileText, 
  Settings, 
  TrendingUp,
  Bell,
  UserCheck,
  Banknote,
  Shield,
  Monitor
} from 'lucide-react';

const AdminSidebar = () => {
  const location = useLocation();

  const menuItems = [
    {
      title: "Dashboard",
      href: "/admin",
      icon: LayoutDashboard,
    },
    {
      title: "User Management",
      href: "/admin/users",
      icon: Users,
    },
    {
      title: "KYC Management",
      href: "/admin/kyc",
      icon: UserCheck,
    },
    {
      title: "Savings Plans",
      href: "/admin/savings-plans",
      icon: TrendingUp,
    },
    {
      title: "Withdrawals",
      href: "/admin/withdrawals",
      icon: Banknote,
    },
    {
      title: "Transactions",
      href: "/admin/transactions",
      icon: CreditCard,
    },
    {
      title: "Loan Management",
      href: "/admin/loans",
      icon: FileText,
    },
    {
      title: "Bill Payment Staff",
      href: "/admin/bill-payment-management",
      icon: Users,
    },
    {
      title: "Notifications",
      href: "/admin/notifications",
      icon: Bell,
    },
    {
      title: "Ads Management",
      href: "/admin/ads",
      icon: Monitor,
    },
    {
      title: "Security",
      href: "/admin/security",
      icon: Shield,
    },
    {
      title: "Settings",
      href: "/admin/settings",
      icon: Settings,
    },
  ];

  return (
    <div className="w-64 bg-card border-r border-border h-screen sticky top-0">
      <div className="p-6 border-b border-border">
        <div className="flex items-center gap-3">
          <div className="h-8 w-8 rounded-full overflow-hidden">
            <img 
              src="/lovable-uploads/371e0f57-a280-42c0-8e1b-50aee7ebddc6.png" 
              alt="Better Interest" 
              className="h-full w-full object-cover"
            />
          </div>
          <div>
            <h2 className="font-bold text-lg">
              <span className="text-primary">KOJA</span>PAY
            </h2>
            <p className="text-xs text-muted-foreground">Admin Portal</p>
          </div>
        </div>
      </div>

      <nav className="p-4 space-y-2">
        {menuItems.map((item) => (
          <Link
            key={item.href}
            to={item.href}
            className={cn(
              "flex items-center gap-3 px-3 py-2 rounded-none transition-colors font-bebas tracking-wide",
              "hover:bg-primary/10 hover:text-primary",
              location.pathname === item.href 
                ? "bg-primary/15 text-primary" 
                : "text-muted-foreground"
            )}
          >
            <item.icon className="h-4 w-4" />
            <span className="text-sm font-medium font-bebas tracking-wide uppercase">{item.title}</span>
          </Link>
        ))}
      </nav>
    </div>
  );
};

export default AdminSidebar;