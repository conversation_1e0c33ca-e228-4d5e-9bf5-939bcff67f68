
// If this file doesn't already exist, we need to create it
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { updateUserByAdmin } from "@/integrations/supabase/admin-users";
import { AlertCircle, Calendar, Edit, Mail, Phone, ShieldCheck, User } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

const UserProfileDetails = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    phone: "",
    status: "",
    isAdmin: false
  });

  useEffect(() => {
    if (!userId) return;
    
    const fetchUserDetails = async () => {
      setIsLoading(true);
      try {
        // Fetch user details from API
        const response = await fetch(`/api/v1/admin/users/${userId}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to fetch user details');
        }

        const data = await response.json();
        const userData = data.data;

        setUser(userData);
        setFormData({
          firstName: userData.first_name || "",
          lastName: userData.last_name || "",
          phone: userData.phone || "",
          status: userData.status || "active",
          isAdmin: userData.isAdmin || false
        });
      } catch (error) {
        console.error("Error fetching user details:", error);
        toast.error("Failed to load user details");
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchUserDetails();
  }, [userId]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCancel = () => {
    // Reset form data to original user data
    if (user) {
      setFormData({
        firstName: user.first_name,
        lastName: user.last_name,
        phone: user.phone || "",
        status: user.status || "active",
        isAdmin: user.isAdmin || false
      });
    }
    setIsEditing(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!userId) return;
    
    try {
      const { data, error } = await updateUserByAdmin(userId, {
        firstName: formData.firstName,
        lastName: formData.lastName,
        phone: formData.phone,
        status: formData.status as 'active' | 'suspended' | 'blocked',
        isAdmin: formData.isAdmin
      });
      
      if (error) throw error;
      
      setUser(data);
      setIsEditing(false);
      toast.success("User profile updated successfully");
    } catch (error) {
      console.error("Error updating user:", error);
      toast.error("Failed to update user profile");
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-brand-blue border-solid"></div>
      </div>
    );
  }
  
  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <AlertCircle className="h-12 w-12 text-brand-yellow mb-4" />
        <h2 className="text-xl font-semibold">User Not Found</h2>
        <p className="text-muted-foreground">The requested user could not be found.</p>
        <Button 
          variant="outline" 
          className="mt-4"
          onClick={() => navigate("/admin/users")}
        >
          Return to User List
        </Button>
      </div>
    );
  }

  const getBadgeVariant = (status: string) => {
    switch (status) {
      case "active":
        return "success";
      case "suspended":
        return "secondary";
      case "blocked":
        return "destructive";
      default:
        return "default";
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">User Profile</h2>
          <p className="text-muted-foreground">
            View and manage user details
          </p>
        </div>
        
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => navigate("/admin/users")}
          >
            Back to Users
          </Button>
          
          {!isEditing ? (
            <Button
              className="bg-brand-blue text-white"
              onClick={() => setIsEditing(true)}
            >
              <Edit className="mr-2 h-4 w-4" /> Edit Profile
            </Button>
          ) : (
            <Button
              variant="outline"
              onClick={handleCancel}
            >
              Cancel
            </Button>
          )}
        </div>
      </div>

      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4">
              <Avatar className="h-16 w-16">
                <AvatarFallback className="text-xl bg-brand-blue text-white">
                  {user.first_name && user.last_name 
                    ? `${user.first_name[0]}${user.last_name[0]}` 
                    : "U"}
                </AvatarFallback>
              </Avatar>
              
              <div>
                <CardTitle className="text-2xl">
                  {user.first_name} {user.last_name}
                </CardTitle>
                <CardDescription className="flex items-center mt-1">
                  <Mail className="h-4 w-4 mr-1" />
                  {user.email}
                </CardDescription>
              </div>
            </div>
            
            <div className="flex flex-col items-end space-y-2">
              <Badge variant={getBadgeVariant(user.status)}>
                {user.status || "Active"}
              </Badge>
              
              {user.isAdmin && (
                <Badge variant="outline" className="flex items-center">
                  <ShieldCheck className="h-3 w-3 mr-1" /> Admin
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {isEditing ? (
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleChange}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="status">Account Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleSelectChange("status", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="suspended">Suspended</SelectItem>
                    <SelectItem value="blocked">Blocked</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="isAdmin" 
                  name="isAdmin"
                  checked={formData.isAdmin}
                  onCheckedChange={(checked) => 
                    setFormData(prev => ({ ...prev, isAdmin: !!checked }))
                  }
                />
                <Label htmlFor="isAdmin">Admin privileges</Label>
              </div>
              
              <div className="flex justify-end pt-4">
                <Button type="submit" className="bg-brand-blue text-white">
                  Save Changes
                </Button>
              </div>
            </form>
          ) : (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex items-center text-muted-foreground">
                    <User className="h-4 w-4 mr-2" />
                    <span>Personal Information</span>
                  </div>
                  
                  <div className="grid grid-cols-1 gap-2">
                    <div>
                      <span className="text-muted-foreground text-sm">First Name</span>
                      <p className="font-medium">{user.first_name}</p>
                    </div>
                    
                    <div>
                      <span className="text-muted-foreground text-sm">Last Name</span>
                      <p className="font-medium">{user.last_name}</p>
                    </div>
                    
                    <div>
                      <span className="text-muted-foreground text-sm">Phone</span>
                      <p className="font-medium">
                        <Phone className="h-3 w-3 inline mr-1" />
                        {user.phone || "Not provided"}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center text-muted-foreground">
                    <ShieldCheck className="h-4 w-4 mr-2" />
                    <span>Account Details</span>
                  </div>
                  
                  <div className="grid grid-cols-1 gap-2">
                    <div>
                      <span className="text-muted-foreground text-sm">Account Status</span>
                      <p className="font-medium">
                        <Badge variant={getBadgeVariant(user.status)}>
                          {user.status || "Active"}
                        </Badge>
                      </p>
                    </div>
                    
                    <div>
                      <span className="text-muted-foreground text-sm">Role</span>
                      <p className="font-medium">
                        {user.isAdmin ? "Admin" : "User"}
                      </p>
                    </div>
                    
                    <div>
                      <span className="text-muted-foreground text-sm">Created On</span>
                      <p className="font-medium">
                        <Calendar className="h-3 w-3 inline mr-1" />
                        {new Date(user.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default UserProfileDetails;
