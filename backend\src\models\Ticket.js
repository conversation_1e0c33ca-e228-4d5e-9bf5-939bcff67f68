const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true
  },
  senderId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true
  },
  senderType: {
    type: String,
    enum: ['user', 'staff', 'bot'],
    required: true
  },
  content: {
    type: String,
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  status: {
    type: String,
    enum: ['sent', 'delivered', 'read'],
    default: 'sent'
  },
  attachments: [{
    filename: String,
    url: String,
    type: String
  }]
});

const ticketSchema = new mongoose.Schema({
  ticketId: {
    type: String,
    required: true,
    unique: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  subject: {
    type: String,
    required: true
  },
  category: {
    type: String,
    enum: ['technical', 'account', 'payment', 'kyc', 'general'],
    default: 'general'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  status: {
    type: String,
    enum: ['open', 'in_progress', 'pending_user', 'resolved', 'closed'],
    default: 'open'
  },
  messages: [messageSchema],
  tags: [String],
  escalationLevel: {
    type: Number,
    default: 0
  },
  escalatedAt: Date,
  escalatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  resolvedAt: Date,
  resolvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  firstResponseTime: Date,
  lastActivity: {
    type: Date,
    default: Date.now
  },
  satisfaction: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    feedback: String,
    submittedAt: Date
  },
  metadata: {
    userAgent: String,
    ip: String,
    source: {
      type: String,
      enum: ['chat', 'email', 'phone', 'form'],
      default: 'chat'
    }
  }
}, {
  timestamps: true
});

// Generate ticket ID
ticketSchema.pre('save', async function(next) {
  if (!this.ticketId) {
    const date = new Date();
    const year = date.getFullYear().toString().substr(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    const count = await this.constructor.countDocuments({
      createdAt: {
        $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
        $lt: new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1)
      }
    });
    
    this.ticketId = `BI${year}${month}${day}${(count + 1).toString().padStart(4, '0')}`;
  }
  next();
});

// Update last activity on message add
ticketSchema.pre('save', function(next) {
  if (this.isModified('messages')) {
    this.lastActivity = new Date();
  }
  next();
});

// Auto-escalation for high priority tickets
ticketSchema.pre('save', function(next) {
  if (this.priority === 'high' || this.priority === 'urgent') {
    const now = new Date();
    const timeSinceCreation = now - this.createdAt;
    
    // Escalate after 2 hours for high priority, 1 hour for urgent
    const escalationTime = this.priority === 'urgent' ? 3600000 : 7200000;
    
    if (timeSinceCreation > escalationTime && this.escalationLevel === 0) {
      this.escalationLevel = 1;
      this.escalatedAt = now;
    }
  }
  next();
});

module.exports = mongoose.model('Ticket', ticketSchema);