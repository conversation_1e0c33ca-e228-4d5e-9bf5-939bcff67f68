import React from 'react';

// MongoDB Collections
export const COLLECTIONS = {
  GROUP_SAVINGS: 'group_savings_plans',
  GROUP_MEMBERS: 'group_members',
  GROUP_CONTRIBUTIONS: 'group_contributions',
  GROUP_INVITES: 'group_invites'
};

// Types
export interface GroupSavingsPlan {
  _id?: string;
  name: string;
  description: string;
  category: string;
  targetAmount: number;
  contributionAmount: number;
  startDate: Date;
  endDate: Date;
  status: 'active' | 'pending' | 'completed' | 'cancelled';
  maxMembers: number;
  currentMembers: number;
  createdBy: string; // user ID
  inviteCode: string;
  rules: {
    minContribution: number;
    maxContribution: number;
    frequency: 'daily' | 'weekly' | 'monthly';
    penaltyRate: number;
  };
  metadata: {
    totalCollected: number;
    nextContributionDate: Date;
    completionPercentage: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface GroupMember {
  _id?: string;
  groupId: string;
  userId: string;
  userName: string;
  userEmail: string;
  role: 'admin' | 'member' | 'coordinator';
  contributionAmount: number;
  totalContributed: number;
  joinedAt: Date;
  status: 'active' | 'inactive' | 'suspended';
  lastContribution: Date;
  paymentMethod?: string;
}

export interface GroupContribution {
  _id?: string;
  groupId: string;
  memberId: string;
  amount: number;
  type: 'regular' | 'penalty' | 'bonus' | 'withdrawal';
  status: 'pending' | 'completed' | 'failed';
  paymentMethod: string;
  transactionRef: string;
  createdAt: Date;
  processedAt?: Date;
}

export interface GroupInvite {
  _id?: string;
  groupId: string;
  inviteCode: string;
  invitedBy: string;
  email?: string;
  phone?: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  expiresAt: Date;
  createdAt: Date;
}

// Utility functions that don't require hooks
export const generateInviteCode = (): string => {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
};

// Hook for using group savings functionality
export const useGroupSavings = () => {
  // This function creates group savings operations using the mongodb hook
  const createGroup = async (planData: Omit<GroupSavingsPlan, '_id' | 'inviteCode' | 'currentMembers' | 'metadata' | 'createdAt' | 'updatedAt'>) => {
    // This would be implemented in the component using the hook
    console.log('Creating group with data:', planData);
    // Return mock data for now
    return {
      ...planData,
      _id: 'mock-id',
      inviteCode: generateInviteCode(),
      currentMembers: 1,
      metadata: {
        totalCollected: 0,
        nextContributionDate: new Date(planData.startDate),
        completionPercentage: 0
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };
  };

  const joinGroup = async (inviteCode: string) => {
    console.log('Joining group with code:', inviteCode);
    // Mock implementation
    return true;
  };

  const getGroups = async () => {
    console.log('Getting groups');
    // Mock implementation
    return [];
  };

  return {
    createGroup,
    joinGroup,
    getGroups,
    generateInviteCode
  };
};

// Simple service without hooks for basic operations
export const groupSavingsService = {
  generateInviteCode,
  // Other utility functions that don't require hooks
};
