
// MongoDB Collections
export const COLLECTIONS = {
  GROUP_SAVINGS: 'group_savings_plans',
  GROUP_MEMBERS: 'group_members',
  GROUP_CONTRIBUTIONS: 'group_contributions',
  GROUP_INVITES: 'group_invites'
};

// Types
export interface GroupSavingsPlan {
  _id?: string;
  name: string;
  description: string;
  category: string;
  targetAmount: number;
  contributionAmount: number;
  startDate: Date;
  endDate: Date;
  status: 'active' | 'pending' | 'completed' | 'cancelled';
  maxMembers: number;
  currentMembers: number;
  createdBy: string; // user ID
  inviteCode: string;
  rules: {
    minContribution: number;
    maxContribution: number;
    frequency: 'daily' | 'weekly' | 'monthly';
    penaltyRate: number;
  };
  metadata: {
    totalCollected: number;
    nextContributionDate: Date;
    completionPercentage: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface GroupMember {
  _id?: string;
  groupId: string;
  userId: string;
  userName: string;
  userEmail: string;
  role: 'admin' | 'member' | 'coordinator';
  contributionAmount: number;
  totalContributed: number;
  joinedAt: Date;
  status: 'active' | 'inactive' | 'suspended';
  lastContribution: Date;
  paymentMethod?: string;
}

export interface GroupContribution {
  _id?: string;
  groupId: string;
  memberId: string;
  amount: number;
  type: 'regular' | 'penalty' | 'bonus' | 'withdrawal';
  status: 'pending' | 'completed' | 'failed';
  paymentMethod: string;
  transactionRef: string;
  createdAt: Date;
  processedAt?: Date;
}

export interface GroupInvite {
  _id?: string;
  groupId: string;
  inviteCode: string;
  invitedBy: string;
  email?: string;
  phone?: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  expiresAt: Date;
  createdAt: Date;
}

// Utility functions that don't require hooks
export const generateInviteCode = (): string => {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
};

// Hook for using group savings functionality
export const useGroupSavings = () => {
  // This function creates group savings operations using the mongodb hook
  const createGroup = async (planData: Omit<GroupSavingsPlan, '_id' | 'inviteCode' | 'currentMembers' | 'metadata' | 'createdAt' | 'updatedAt'>) => {
    // Create group via API
    const response = await fetch('/api/v1/group-savings', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(planData)
    });

    if (!response.ok) {
      throw new Error('Failed to create group savings plan');
    }

    const data = await response.json();
    return data.data;
  };

  const joinGroup = async (inviteCode: string) => {
    try {
      const response = await fetch('/api/v1/group-savings/join', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ inviteCode })
      });

      if (!response.ok) {
        throw new Error('Failed to join group');
      }

      const result = await response.json();
      return result.success;
    } catch (error) {
      console.error('Error joining group:', error);
      throw error;
    }
  };

  const getGroups = async () => {
    try {
      const response = await fetch('/api/v1/group-savings', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch groups');
      }

      const result = await response.json();
      return result.data || [];
    } catch (error) {
      console.error('Error fetching groups:', error);
      return [];
    }
  };

  return {
    createGroup,
    joinGroup,
    getGroups,
    generateInviteCode
  };
};

// Simple service without hooks for basic operations
export const groupSavingsService = {
  generateInviteCode,
  // Other utility functions that don't require hooks
};
