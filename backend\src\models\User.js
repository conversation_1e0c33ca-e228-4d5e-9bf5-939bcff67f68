const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  firstName: {
    type: String,
    required: [true, 'First name is required'],
    trim: true
  },
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    unique: true
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters']
  },
  pin: {
    type: String,
    required: [true, 'PIN is required'],
    length: [4, 'PIN must be exactly 4 digits']
  },
  role: {
    type: String,
    enum: ['user', 'admin', 'staff'],
    default: 'user'
  },
  staffPermissions: {
    modules: [{ 
      type: String, 
      enum: ['bill_payment', 'user_management', 'savings_plans', 'withdrawals', 'kyc', 'notifications', 'analytics', 'all']
    }],
    lastUpdated: { type: Date, default: Date.now },
    updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  kycStatus: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  balance: {
    type: Number,
    default: 0
  },
  savingsGoal: {
    amount: { type: Number, default: 0 },
    targetDate: Date,
    description: String
  },
  profile: {
    avatar: String,
    dateOfBirth: Date,
    address: {
      street: String,
      city: String,
      state: String,
      country: String,
      zipCode: String
    },
    refreshToken: {
      type: String,
      default: null
    }
  },
  socialAuth: {
    googleId: String,
    facebookId: String,
    provider: String,
    linkedAt: { type: Date, default: Date.now }
  },
  roundUpSettings: {
    enabled: { type: Boolean, default: true },
    multiplier: { type: Number, default: 1, min: 1, max: 5 },
    goalAmount: { type: Number, default: 50000 }
  },
  investmentProfile: {
    riskTolerance: { type: String, enum: ['conservative', 'moderate', 'aggressive'], default: 'moderate' },
    investmentExperience: { type: String, enum: ['beginner', 'intermediate', 'advanced'], default: 'beginner' },
    preferredAssets: [{ type: String, enum: ['stocks', 'bonds', 'mutual_funds', 'real_estate'] }],
    autoInvest: { type: Boolean, default: false },
    monthlyInvestmentGoal: { type: Number, default: 0 }
  },
  socialPreferences: {
    allowGroupInvites: { type: Boolean, default: true },
    shareAchievements: { type: Boolean, default: true },
    showInLeaderboard: { type: Boolean, default: true },
    allowMentions: { type: Boolean, default: true }
  },
  reputation: {
    score: { type: Number, default: 100, min: 0, max: 100 },
    level: { type: String, enum: ['bronze', 'silver', 'gold', 'platinum'], default: 'bronze' },
    badges: [{ type: String }],
    groupContributions: { type: Number, default: 0 },
    onTimePayments: { type: Number, default: 0 },
    totalPayments: { type: Number, default: 0 }
  },
  refreshToken: {
    type: String,
    default: null
  }
}, {
  timestamps: true
});

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Hash PIN before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('pin')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.pin = await bcrypt.hash(this.pin, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Compare PIN method
userSchema.methods.comparePin = async function(candidatePin) {
  return bcrypt.compare(candidatePin, this.pin);
};

module.exports = mongoose.model('User', userSchema);