import Header from '@/components/landing/Header';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import AOS from 'aos';
import 'aos/dist/aos.css';
import { motion } from 'framer-motion';
import {
    ArrowRight,
    Award,
    BarChart3,
    Gift,
    Lock,
    PiggyBank,
    Play,
    Shield,
    Smartphone,
    Target,
    TrendingUp,
    Users,
    Wallet,
    Zap
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';

const ModernLanding = () => {
  const navigate = useNavigate();

  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: 'ease-out-cubic',
    });
  }, []);

  const [stats] = useState([
    { value: '50,000+', label: 'Active Users', icon: Users },
    { value: '₦2.5B+', label: 'Total Savings', icon: TrendingUp },
    { value: '15%', label: 'Annual Returns', icon: Award },
    { value: '99.9%', label: 'Uptime', icon: Shield },
  ]);

  const features = [
    {
      icon: PiggyBank,
      title: 'Smart Savings',
      description: 'Automated savings with competitive interest rates up to 15% annually',
      color: 'from-logo-green to-logo-green-dark'
    },
    {
      icon: Target,
      title: 'Goal Planning',
      description: 'Set and achieve your financial goals with our intelligent planning tools',
      color: 'from-logo-yellow-orange to-logo-yellow-orange-dark'
    },
    {
      icon: Lock,
      title: 'Secure & Insured',
      description: 'Bank-level security with NDIC insurance up to ₦500,000 per account',
      color: 'from-logo-green-light to-logo-green'
    },
    {
      icon: Smartphone,
      title: 'Mobile First',
      description: 'Seamless experience across all devices with our modern mobile app',
      color: 'from-logo-yellow-orange-light to-logo-yellow-orange'
    },
    {
      icon: BarChart3,
      title: 'Real-time Analytics',
      description: 'Track your progress with detailed insights and performance metrics',
      color: 'from-logo-green to-logo-green-light'
    },
    {
      icon: Zap,
      title: 'Instant Transfers',
      description: 'Lightning-fast transactions with real-time notifications',
      color: 'from-logo-yellow-orange to-logo-yellow-orange-light'
    }
  ];

  const testimonials = [
    {
      name: 'Sarah Johnson',
      role: 'Software Engineer',
      content: 'Better Interest helped me save ₦500,000 in just 8 months. The automated savings feature is a game-changer!',
      rating: 5,
      avatar: '/api/placeholder/40/40'
    },
    {
      name: 'Michael Adebayo',
      role: 'Business Owner',
      content: 'The best savings platform in Nigeria. Great returns and excellent customer service.',
      rating: 5,
      avatar: '/api/placeholder/40/40'
    },
    {
      name: 'Fatima Ibrahim',
      role: 'Teacher',
      content: 'I love how easy it is to set savings goals and track my progress. Highly recommended!',
      rating: 5,
      avatar: '/api/placeholder/40/40'
    }
  ];

  return (
    <>
      <Helmet>
        <title>Better Interest | Smart Digital Savings Platform</title>
        <meta name="description" content="Grow your money with Nigeria's leading digital savings platform. Earn up to 15% annual returns with automated savings, goal planning, and secure investments." />
        <meta name="keywords" content="savings, investment, Nigeria, fintech, digital banking, high interest" />
        <meta property="og:title" content="Better Interest | Smart Digital Savings Platform" />
        <meta property="og:description" content="Grow your money with Nigeria's leading digital savings platform. Earn up to 15% annual returns." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://demo.kojaonline.store" />
      </Helmet>

      <div className="min-h-screen bg-background">
        <Header />
        
        {/* Hero Section */}
        <section className="relative pt-20 pb-16 overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-background to-primary/10" />
          <div className="absolute top-20 left-10 w-72 h-72 bg-primary/10 rounded-full blur-3xl" />
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-emerald-500/10 rounded-full blur-3xl" />
          
          <div className="container mx-auto px-4 relative z-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
              {/* Left Column - Content */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="space-y-8"
              >
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium">
                  <Zap className="w-4 h-4 mr-2" />
                  Nigeria's #1 Savings Platform
                </div>
                
                <h1 className="text-5xl md:text-7xl font-bold leading-tight">
                  <span className="bg-gradient-to-r from-primary to-emerald-600 bg-clip-text text-transparent">
                    Better Interest,
                  </span>
                  <br />
                  <span className="text-foreground">
                    Better Future
                  </span>
                </h1>
                
                <p className="text-xl text-muted-foreground leading-relaxed max-w-lg">
                  Grow your money with automated savings, competitive returns up to 15% annually, 
                  and intelligent financial planning tools designed for modern Nigerians.
                </p>

                <div className="flex flex-col sm:flex-row gap-4">
                  <button
                    className="btn-neumorphic-inward text-lg px-8 py-6 group"
                    onClick={() => navigate('/signup')}
                  >
                    Start Saving Today
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                  </button>

                  <Button
                    variant="outline"
                    size="lg"
                    className="text-lg px-8 py-6 rounded-xl group border-logo-yellow-orange text-logo-yellow-orange hover:bg-logo-yellow-orange/10"
                    onClick={() => navigate('/demo')}
                  >
                    <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                    Watch Demo
                  </Button>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6 pt-8">
                  {stats.map((stat, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}
                      className="text-center"
                    >
                      <div className="text-2xl font-bold text-primary">{stat.value}</div>
                      <div className="text-sm text-muted-foreground">{stat.label}</div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Right Column - Visual */}
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="relative"
              >
                <div className="relative z-10">
                  <div className="bg-gradient-to-br from-primary/20 to-emerald-500/20 rounded-3xl p-8 backdrop-blur-sm border border-primary/20">
                    <div className="space-y-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center">
                            <Wallet className="h-6 w-6 text-white" />
                          </div>
                          <div>
                            <div className="font-semibold">Total Savings</div>
                            <div className="text-sm text-muted-foreground">Current Balance</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-primary">₦1,250,000</div>
                          <div className="text-sm text-emerald-600">+15% this year</div>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-background/50 rounded-xl p-4">
                          <div className="flex items-center space-x-2 mb-2">
                            <Target className="h-4 w-4 text-blue-500" />
                            <span className="text-sm font-medium">Goal Progress</span>
                          </div>
                          <div className="text-lg font-bold">85%</div>
                          <div className="w-full bg-muted rounded-full h-2 mt-2">
                            <div className="bg-blue-500 h-2 rounded-full w-[85%]"></div>
                          </div>
                        </div>
                        
                        <div className="bg-background/50 rounded-xl p-4">
                          <div className="flex items-center space-x-2 mb-2">
                            <TrendingUp className="h-4 w-4 text-emerald-500" />
                            <span className="text-sm font-medium">Monthly Growth</span>
                          </div>
                          <div className="text-lg font-bold">+12.5%</div>
                          <div className="text-xs text-emerald-600">Above average</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Floating Elements */}
                <motion.div
                  animate={{ y: [-10, 10, -10] }}
                  transition={{ duration: 4, repeat: Infinity }}
                  className="absolute -top-6 -right-6 w-24 h-24 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg"
                >
                  <Gift className="h-8 w-8 text-white" />
                </motion.div>
                
                <motion.div
                  animate={{ y: [10, -10, 10] }}
                  transition={{ duration: 3, repeat: Infinity }}
                  className="absolute -bottom-6 -left-6 w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg"
                >
                  <Shield className="h-6 w-6 text-white" />
                </motion.div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-24 bg-muted/30">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Choose Better Interest?
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Experience the future of digital savings with our comprehensive suite of financial tools 
                designed to help you achieve your goals faster and smarter.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <Card className="h-full hover:shadow-lg transition-all duration-300 border-0 bg-background/50 backdrop-blur-sm">
                    <CardContent className="p-8">
                      <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${feature.color} flex items-center justify-center mb-6`}>
                        <feature.icon className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="text-xl font-bold mb-4">{feature.title}</h3>
                      <p className="text-muted-foreground leading-relaxed">{feature.description}</p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default ModernLanding;
