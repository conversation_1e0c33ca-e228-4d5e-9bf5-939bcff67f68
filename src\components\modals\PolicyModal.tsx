import React, { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Shield, FileText, Eye, CheckCircle } from 'lucide-react';

interface PolicyModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAccept: () => void;
  title?: string;
  type?: 'general' | 'group' | 'rotational' | 'investment';
}

export const PolicyModal = ({ 
  open, 
  onOpenChange, 
  onAccept, 
  title = "Terms and Conditions",
  type = 'general'
}: PolicyModalProps) => {
  const [hasReadTerms, setHasReadTerms] = useState(false);
  const [hasReadPrivacy, setHasReadPrivacy] = useState(false);
  const [acknowledged, setAcknowledged] = useState(false);

  const handleAccept = () => {
    if (hasReadTerms && hasReadPrivacy && acknowledged) {
      onAccept();
      // Reset state
      setHasReadTerms(false);
      setHasReadPrivacy(false);
      setAcknowledged(false);
    }
  };

  const canProceed = hasReadTerms && hasReadPrivacy && acknowledged;

  const getSpecificTerms = () => {
    switch (type) {
      case 'group':
        return {
          title: "Group Savings Terms & Conditions",
          content: `
By participating in our Group Savings Plan, you agree to:

1. CONTRIBUTION OBLIGATIONS
• Make regular contributions as agreed upon by the group
• Adhere to the payment schedule and frequency
• Pay any applicable penalties for late payments

2. GROUP PARTICIPATION
• Respect other group members and their contributions
• Maintain active participation throughout the plan duration
• Follow group rules and decisions made by majority

3. WITHDRAWAL CONDITIONS
• Early withdrawal may result in penalty fees
• Group consent may be required for major withdrawals
• Funds are subject to group payout schedule

4. LIABILITY AND RISKS
• Understand that group performance affects individual returns
• Accept responsibility for your contribution obligations
• Acknowledge market risks that may affect returns
          `
        };
      case 'rotational':
        return {
          title: "Rotational Savings Terms & Conditions",
          content: `
By joining our Rotational Savings Plan, you agree to:

1. ROTATION SCHEDULE
• Contribute according to the established rotation cycle
• Accept your assigned position in the payout sequence
• Continue contributions even after receiving your payout

2. COMMITMENT REQUIREMENTS
• Complete the full rotation cycle
• Maintain consistent contribution amounts
• Provide advance notice for any payment difficulties

3. PAYOUT CONDITIONS
• Receive payout only when it's your designated turn
• Accept that early payout is not permitted
• Understand that missed contributions affect everyone

4. GROUP RESPONSIBILITY
• Support other members in completing their cycles
• Report any concerns about member non-compliance
• Participate in group decisions regarding defaults
          `
        };
      default:
        return {
          title: "General Terms & Conditions",
          content: `
By using our savings platform, you agree to:

1. ACCOUNT RESPONSIBILITIES
• Provide accurate and up-to-date information
• Maintain the security of your account credentials
• Notify us immediately of any unauthorized access

2. SAVINGS PLANS
• Understand the terms of each savings product
• Accept that returns are subject to market conditions
• Acknowledge that early withdrawal may incur penalties

3. PAYMENT PROCESSING
• Authorize automatic deductions as scheduled
• Ensure sufficient funds for planned contributions
• Accept payment processing fees where applicable

4. COMPLIANCE
• Adhere to all applicable laws and regulations
• Cooperate with Know Your Customer (KYC) requirements
• Report any suspicious activities
          `
        };
    }
  };

  const specificTerms = getSpecificTerms();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] rounded-none border-2 border-primary shadow-[8px_8px_16px_rgba(0,0,0,0.2),-8px_-8px_16px_rgba(255,255,255,0.1)]">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-none bg-primary/10 flex items-center justify-center shadow-[inset_2px_2px_4px_rgba(255,255,255,0.3),inset_-2px_-2px_4px_rgba(0,0,0,0.2)]">
              <Shield className="h-5 w-5 text-primary" />
            </div>
            <div>
              <DialogTitle className="text-xl">{specificTerms.title}</DialogTitle>
              <DialogDescription>
                Please read and acknowledge all terms before proceeding
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Terms Content */}
          <div className="bg-background border rounded-none p-4 shadow-[inset_2px_2px_4px_rgba(0,0,0,0.1),inset_-2px_-2px_4px_rgba(255,255,255,0.1)]">
            <div className="flex items-center gap-2 mb-3">
              <FileText className="h-4 w-4 text-primary" />
              <span className="font-medium">Terms and Conditions</span>
              <div className="ml-auto flex items-center gap-2">
                <Checkbox 
                  id="read-terms"
                  checked={hasReadTerms}
                  onCheckedChange={(checked) => setHasReadTerms(checked === true)}
                />
                <label htmlFor="read-terms" className="text-sm">I have read</label>
              </div>
            </div>
            <ScrollArea className="h-48 w-full">
              <div className="text-sm text-muted-foreground whitespace-pre-line">
                {specificTerms.content}
              </div>
            </ScrollArea>
          </div>

          {/* Privacy Policy */}
          <div className="bg-background border rounded-none p-4 shadow-[inset_2px_2px_4px_rgba(0,0,0,0.1),inset_-2px_-2px_4px_rgba(255,255,255,0.1)]">
            <div className="flex items-center gap-2 mb-3">
              <Eye className="h-4 w-4 text-primary" />
              <span className="font-medium">Privacy Policy</span>
              <div className="ml-auto flex items-center gap-2">
                <Checkbox 
                  id="read-privacy"
                  checked={hasReadPrivacy}
                  onCheckedChange={(checked) => setHasReadPrivacy(checked === true)}
                />
                <label htmlFor="read-privacy" className="text-sm">I have read</label>
              </div>
            </div>
            <ScrollArea className="h-32 w-full">
              <div className="text-sm text-muted-foreground">
                <p className="mb-2">
                  <strong>Data Collection:</strong> We collect personal and financial information necessary to provide our services, including but not limited to name, email, phone number, and transaction data.
                </p>
                <p className="mb-2">
                  <strong>Data Usage:</strong> Your information is used to process transactions, verify identity, provide customer support, and improve our services. We do not sell your data to third parties.
                </p>
                <p className="mb-2">
                  <strong>Data Security:</strong> We implement industry-standard security measures to protect your data, including encryption, secure servers, and regular security audits.
                </p>
                <p className="mb-2">
                  <strong>Third-Party Services:</strong> We may share necessary information with payment processors, verification services, and regulatory bodies as required by law.
                </p>
                <p>
                  <strong>Your Rights:</strong> You have the right to access, correct, or delete your personal information. Contact our support team for any privacy-related requests.
                </p>
              </div>
            </ScrollArea>
          </div>

          <Separator />

          {/* Insurance Information */}
          <div className="bg-green-50 dark:bg-green-900/20 border-2 border-green-200 dark:border-green-800 rounded-none p-4 shadow-[inset_2px_2px_4px_rgba(34,197,94,0.1),inset_-2px_-2px_4px_rgba(255,255,255,0.1)]">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="h-5 w-5 text-green-600" />
              <span className="font-medium text-green-800 dark:text-green-200">Deposit Insurance</span>
            </div>
            <p className="text-sm text-green-700 dark:text-green-300 mb-2">
              <strong>INSURED BY THE NDIC & AMAC MFB LTD</strong>
            </p>
            <p className="text-xs text-green-600 dark:text-green-400">
              Your deposits are protected by the Nigeria Deposit Insurance Corporation (NDIC) up to ₦500,000 per depositor. 
              AMAC Microfinance Bank Limited is licensed and regulated by the Central Bank of Nigeria.
            </p>
          </div>

          {/* Final Acknowledgment */}
          <div className="flex items-start gap-3 p-4 bg-primary/5 border border-primary/20 rounded-none">
            <Checkbox 
              id="acknowledge"
              checked={acknowledged}
              onCheckedChange={(checked) => setAcknowledged(checked === true)}
              className="mt-0.5"
            />
            <div>
              <label htmlFor="acknowledge" className="text-sm font-medium cursor-pointer">
                I acknowledge that I have read and understood all terms and conditions
              </label>
              <p className="text-xs text-muted-foreground mt-1">
                I agree to be bound by these terms and understand my rights and obligations.
              </p>
            </div>
          </div>
        </div>

        <DialogFooter className="flex gap-3">
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            className="rounded-none"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleAccept}
            disabled={!canProceed}
            className={`rounded-none ${canProceed ? 'bg-primary hover:bg-primary/90' : 'opacity-50'}`}
          >
            <CheckCircle className="mr-2 h-4 w-4" />
            Accept & Continue
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};