import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  Plus, 
  ThumbsUp, 
  ThumbsDown, 
  Edit, 
  Trash2,
  Eye,
  HelpCircle
} from 'lucide-react';
import { useAuth } from '@/hooks/use-auth';
import { toast } from 'sonner';

interface FAQ {
  _id: string;
  question: string;
  answer: string;
  category: string;
  tags: string[];
  isActive: boolean;
  views: number;
  helpful: number;
  notHelpful: number;
  createdAt: string;
}

interface FAQManagerProps {
  isAdmin?: boolean;
}

export const FAQManager = ({ isAdmin = false }: FAQManagerProps) => {
  const { user } = useAuth();
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingFaq, setEditingFaq] = useState<FAQ | null>(null);
  
  const [newFaq, setNewFaq] = useState({
    question: '',
    answer: '',
    category: 'general',
    tags: ''
  });

  const categories = [
    { value: 'account', label: 'Account' },
    { value: 'payments', label: 'Payments' },
    { value: 'kyc', label: 'KYC' },
    { value: 'savings', label: 'Savings' },
    { value: 'technical', label: 'Technical' },
    { value: 'general', label: 'General' }
  ];

  useEffect(() => {
    fetchFAQs();
  }, [searchTerm, selectedCategory]);

  const fetchFAQs = async () => {
    setLoading(true);
    try {
      const query = new URLSearchParams();
      if (searchTerm) query.append('search', searchTerm);
      if (selectedCategory) query.append('category', selectedCategory);

      const response = await fetch(`/api/support/faqs?${query}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setFaqs(data.data);
      }
    } catch (error) {
      console.error('Fetch FAQs error:', error);
      toast.error('Failed to fetch FAQs');
    } finally {
      setLoading(false);
    }
  };

  const createOrUpdateFaq = async () => {
    try {
      const method = editingFaq ? 'PUT' : 'POST';
      const url = editingFaq ? `/api/support/admin/faqs/${editingFaq._id}` : '/api/support/admin/faqs';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          ...newFaq,
          tags: newFaq.tags.split(',').map(tag => tag.trim()).filter(Boolean)
        })
      });

      const data = await response.json();
      if (data.success) {
        toast.success(editingFaq ? 'FAQ updated successfully' : 'FAQ created successfully');
        setShowCreateDialog(false);
        setEditingFaq(null);
        setNewFaq({ question: '', answer: '', category: 'general', tags: '' });
        fetchFAQs();
      } else {
        toast.error(data.message || 'Failed to save FAQ');
      }
    } catch (error) {
      console.error('Save FAQ error:', error);
      toast.error('Failed to save FAQ');
    }
  };

  const rateFaq = async (faqId: string, helpful: boolean) => {
    try {
      const response = await fetch(`/api/support/faqs/${faqId}/rate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ helpful })
      });

      const data = await response.json();
      if (data.success) {
        toast.success('Thank you for your feedback!');
        fetchFAQs();
      }
    } catch (error) {
      console.error('Rate FAQ error:', error);
      toast.error('Failed to submit rating');
    }
  };

  const deleteFaq = async (faqId: string) => {
    if (!confirm('Are you sure you want to delete this FAQ?')) return;

    try {
      const response = await fetch(`/api/support/admin/faqs/${faqId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();
      if (data.success) {
        toast.success('FAQ deleted successfully');
        fetchFAQs();
      } else {
        toast.error(data.message || 'Failed to delete FAQ');
      }
    } catch (error) {
      console.error('Delete FAQ error:', error);
      toast.error('Failed to delete FAQ');
    }
  };

  const startEdit = (faq: FAQ) => {
    setEditingFaq(faq);
    setNewFaq({
      question: faq.question,
      answer: faq.answer,
      category: faq.category,
      tags: faq.tags.join(', ')
    });
    setShowCreateDialog(true);
  };

  const groupedFaqs = faqs.reduce((acc, faq) => {
    if (!acc[faq.category]) {
      acc[faq.category] = [];
    }
    acc[faq.category].push(faq);
    return acc;
  }, {} as Record<string, FAQ[]>);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <div>
          <h1 className="text-2xl font-bold">Frequently Asked Questions</h1>
          <p className="text-muted-foreground">
            {isAdmin ? 'Manage FAQ content' : 'Find answers to common questions'}
          </p>
        </div>
        
        {isAdmin && (
          <Dialog open={showCreateDialog} onOpenChange={(open) => {
            setShowCreateDialog(open);
            if (!open) {
              setEditingFaq(null);
              setNewFaq({ question: '', answer: '', category: 'general', tags: '' });
            }
          }}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add FAQ
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl rounded-none">
              <DialogHeader>
                <DialogTitle>{editingFaq ? 'Edit FAQ' : 'Create New FAQ'}</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <Input
                  placeholder="Question"
                  value={newFaq.question}
                  onChange={(e) => setNewFaq({ ...newFaq, question: e.target.value })}
                  className="rounded-none"
                />
                <Textarea
                  placeholder="Answer"
                  value={newFaq.answer}
                  onChange={(e) => setNewFaq({ ...newFaq, answer: e.target.value })}
                  rows={6}
                  className="rounded-none"
                />
                <div className="grid grid-cols-2 gap-4">
                  <Select value={newFaq.category} onValueChange={(value) => setNewFaq({ ...newFaq, category: value })}>
                    <SelectTrigger className="rounded-none">
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((cat) => (
                        <SelectItem key={cat.value} value={cat.value}>
                          {cat.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Input
                    placeholder="Tags (comma separated)"
                    value={newFaq.tags}
                    onChange={(e) => setNewFaq({ ...newFaq, tags: e.target.value })}
                    className="rounded-none"
                  />
                </div>
                <Button onClick={createOrUpdateFaq} className="w-full rounded-none">
                  {editingFaq ? 'Update FAQ' : 'Create FAQ'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Search and Filters */}
      <Card className="rounded-none">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search FAQs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 rounded-none"
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="rounded-none">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Categories</SelectItem>
                {categories.map((cat) => (
                  <SelectItem key={cat.value} value={cat.value}>
                    {cat.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* FAQs Content */}
      {loading ? (
        <div className="text-center py-8">Loading FAQs...</div>
      ) : faqs.length === 0 ? (
        <Card className="rounded-none">
          <CardContent className="text-center py-8">
            <HelpCircle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">No FAQs found</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {Object.entries(groupedFaqs).map(([category, categoryFaqs]) => (
            <Card key={category} className="rounded-none">
              <CardHeader>
                <CardTitle className="capitalize flex items-center gap-2">
                  {categories.find(c => c.value === category)?.label || category}
                  <Badge variant="secondary" className="rounded-none">
                    {categoryFaqs.length}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Accordion type="single" collapsible className="w-full">
                  {categoryFaqs.map((faq) => (
                    <AccordionItem key={faq._id} value={faq._id}>
                      <AccordionTrigger className="text-left hover:no-underline">
                        <div className="flex items-center justify-between w-full pr-4">
                          <span>{faq.question}</span>
                          {isAdmin && (
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <Eye className="h-3 w-3" />
                                {faq.views}
                              </div>
                              <div className="flex items-center gap-1">
                                <ThumbsUp className="h-3 w-3" />
                                {faq.helpful}
                              </div>
                            </div>
                          )}
                        </div>
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          <div className="prose max-w-none text-sm">
                            {faq.answer}
                          </div>
                          
                          {faq.tags.length > 0 && (
                            <div className="flex flex-wrap gap-2">
                              {faq.tags.map((tag, index) => (
                                <Badge key={index} variant="outline" className="text-xs rounded-none">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          )}

                          <div className="flex items-center justify-between pt-4 border-t">
                            {!isAdmin ? (
                              <div className="flex items-center gap-4">
                                <span className="text-sm text-muted-foreground">Was this helpful?</span>
                                <div className="flex gap-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => rateFaq(faq._id, true)}
                                    className="rounded-none"
                                  >
                                    <ThumbsUp className="h-3 w-3 mr-1" />
                                    Yes ({faq.helpful})
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => rateFaq(faq._id, false)}
                                    className="rounded-none"
                                  >
                                    <ThumbsDown className="h-3 w-3 mr-1" />
                                    No ({faq.notHelpful})
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => startEdit(faq)}
                                  className="rounded-none"
                                >
                                  <Edit className="h-3 w-3 mr-1" />
                                  Edit
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => deleteFaq(faq._id)}
                                  className="text-red-600 hover:text-red-700 rounded-none"
                                >
                                  <Trash2 className="h-3 w-3 mr-1" />
                                  Delete
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};