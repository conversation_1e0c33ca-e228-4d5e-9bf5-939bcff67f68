const mongoose = require('mongoose');
const databaseHealthService = require('../services/DatabaseHealthService');

class DatabaseMonitoringMiddleware {
  constructor() {
    this.isEnabled = process.env.NODE_ENV !== 'test'; // Disable in test environment
    this.queryThreshold = parseInt(process.env.SLOW_QUERY_THRESHOLD) || 1000; // 1 second
    this.setupMongooseHooks();
  }

  /**
   * Setup Mongoose middleware hooks to monitor queries
   */
  setupMongooseHooks() {
    if (!this.isEnabled) return;

    // Pre-hook to start timing
    mongoose.plugin((schema, options) => {
      // Find operations
      schema.pre(['find', 'findOne', 'findOneAndUpdate', 'findOneAndDelete'], function() {
        this._startTime = Date.now();
        this._operation = this.op || 'find';
        this._modelName = this.model?.modelName || 'unknown';
      });

      // Count operations
      schema.pre(['count', 'countDocuments', 'estimatedDocumentCount'], function() {
        this._startTime = Date.now();
        this._operation = this.op || 'count';
        this._modelName = this.model?.modelName || 'unknown';
      });

      // Aggregate operations
      schema.pre('aggregate', function() {
        this._startTime = Date.now();
        this._operation = 'aggregate';
        this._modelName = this.model?.modelName || 'unknown';
      });

      // Save operations
      schema.pre(['save', 'insertMany'], function() {
        this._startTime = Date.now();
        this._operation = this.op || 'save';
        this._modelName = this.constructor?.modelName || 'unknown';
      });

      // Update operations
      schema.pre(['updateOne', 'updateMany', 'replaceOne'], function() {
        this._startTime = Date.now();
        this._operation = this.op || 'update';
        this._modelName = this.model?.modelName || 'unknown';
      });

      // Delete operations
      schema.pre(['deleteOne', 'deleteMany', 'remove'], function() {
        this._startTime = Date.now();
        this._operation = this.op || 'delete';
        this._modelName = this.model?.modelName || 'unknown';
      });

      // Post-hooks to measure execution time
      schema.post(['find', 'findOne', 'findOneAndUpdate', 'findOneAndDelete'], function(result) {
        this._recordQueryPerformance();
      });

      schema.post(['count', 'countDocuments', 'estimatedDocumentCount'], function(result) {
        this._recordQueryPerformance();
      });

      schema.post('aggregate', function(result) {
        this._recordQueryPerformance();
      });

      schema.post(['save', 'insertMany'], function(result) {
        this._recordQueryPerformance();
      });

      schema.post(['updateOne', 'updateMany', 'replaceOne'], function(result) {
        this._recordQueryPerformance();
      });

      schema.post(['deleteOne', 'deleteMany', 'remove'], function(result) {
        this._recordQueryPerformance();
      });

      // Error handling
      schema.post(['find', 'findOne', 'findOneAndUpdate', 'findOneAndDelete'], function(error) {
        if (error) {
          this._recordQueryError(error);
        }
      });

      schema.post(['save', 'insertMany', 'updateOne', 'updateMany', 'deleteOne', 'deleteMany'], function(error) {
        if (error) {
          this._recordQueryError(error);
        }
      });

      // Add helper methods to schema
      schema.methods._recordQueryPerformance = function() {
        if (this._startTime) {
          const duration = Date.now() - this._startTime;
          databaseHealthService.recordQueryPerformance(
            this._operation,
            duration,
            this._modelName
          );
        }
      };

      schema.statics._recordQueryPerformance = function() {
        if (this._startTime) {
          const duration = Date.now() - this._startTime;
          databaseHealthService.recordQueryPerformance(
            this._operation,
            duration,
            this._modelName
          );
        }
      };

      schema.methods._recordQueryError = function(error) {
        databaseHealthService.recordError('query_error', `${this._operation} on ${this._modelName}: ${error.message}`);
      };

      schema.statics._recordQueryError = function(error) {
        databaseHealthService.recordError('query_error', `${this._operation} on ${this._modelName}: ${error.message}`);
      };
    });

    console.log('📊 Database monitoring middleware enabled');
  }

  /**
   * Express middleware to monitor database operations in requests
   */
  requestMonitoring() {
    return (req, res, next) => {
      if (!this.isEnabled) return next();

      const startTime = Date.now();
      let queryCount = 0;
      let totalQueryTime = 0;

      // Track queries during this request
      const originalQuery = mongoose.Query.prototype.exec;
      mongoose.Query.prototype.exec = function(callback) {
        const queryStart = Date.now();
        queryCount++;

        const result = originalQuery.call(this, callback);
        
        if (result && typeof result.then === 'function') {
          return result.then(
            (data) => {
              const queryTime = Date.now() - queryStart;
              totalQueryTime += queryTime;
              
              // Log slow queries
              if (queryTime > this.queryThreshold) {
                console.warn(`🐌 Slow query in ${req.method} ${req.path}: ${queryTime}ms`);
              }
              
              return data;
            },
            (error) => {
              const queryTime = Date.now() - queryStart;
              totalQueryTime += queryTime;
              databaseHealthService.recordError('request_query_error', error.message);
              throw error;
            }
          );
        }
        
        return result;
      };

      // Restore original exec after request
      res.on('finish', () => {
        mongoose.Query.prototype.exec = originalQuery;
        
        const requestTime = Date.now() - startTime;
        
        // Add database metrics to response headers (for debugging)
        if (process.env.NODE_ENV === 'development') {
          res.set({
            'X-DB-Query-Count': queryCount.toString(),
            'X-DB-Query-Time': totalQueryTime.toString(),
            'X-Request-Time': requestTime.toString()
          });
        }
        
        // Log requests with many queries or long database time
        if (queryCount > 10 || totalQueryTime > 2000) {
          console.warn(`📊 Heavy DB usage in ${req.method} ${req.path}: ${queryCount} queries, ${totalQueryTime}ms DB time`);
        }
      });

      next();
    };
  }

  /**
   * Connection monitoring middleware
   */
  connectionMonitoring() {
    return (req, res, next) => {
      if (!this.isEnabled) return next();

      // Check if database is connected
      if (mongoose.connection.readyState !== 1) {
        return res.status(503).json({
          success: false,
          message: 'Database connection unavailable',
          error: 'SERVICE_UNAVAILABLE'
        });
      }

      // Add connection info to request
      req.dbConnection = {
        readyState: mongoose.connection.readyState,
        host: mongoose.connection.host,
        port: mongoose.connection.port,
        name: mongoose.connection.name
      };

      next();
    };
  }

  /**
   * Transaction monitoring wrapper
   */
  monitorTransaction(operation, metadata = {}) {
    return async (callback) => {
      if (!this.isEnabled) {
        return await callback();
      }

      const startTime = Date.now();
      const session = await mongoose.startSession();
      
      try {
        const result = await session.withTransaction(async () => {
          return await callback(session);
        });
        
        const duration = Date.now() - startTime;
        
        databaseHealthService.recordQueryPerformance(
          'transaction',
          duration,
          metadata.collection || 'multi-collection'
        );
        
        // Log long transactions
        if (duration > 5000) {
          console.warn(`⏰ Long transaction: ${operation} took ${duration}ms`);
        }
        
        return result;
        
      } catch (error) {
        const duration = Date.now() - startTime;
        
        databaseHealthService.recordError(
          'transaction_error',
          `Transaction ${operation} failed after ${duration}ms: ${error.message}`
        );
        
        throw error;
        
      } finally {
        await session.endSession();
      }
    };
  }

  /**
   * Bulk operation monitoring
   */
  monitorBulkOperation(operation, collection, count) {
    return async (callback) => {
      if (!this.isEnabled) {
        return await callback();
      }

      const startTime = Date.now();
      
      try {
        const result = await callback();
        const duration = Date.now() - startTime;
        
        databaseHealthService.recordQueryPerformance(
          `bulk_${operation}`,
          duration,
          collection
        );
        
        // Log bulk operations
        console.log(`📦 Bulk ${operation}: ${count} documents in ${collection} (${duration}ms)`);
        
        return result;
        
      } catch (error) {
        const duration = Date.now() - startTime;
        
        databaseHealthService.recordError(
          'bulk_operation_error',
          `Bulk ${operation} on ${collection} failed after ${duration}ms: ${error.message}`
        );
        
        throw error;
      }
    };
  }

  /**
   * Index monitoring
   */
  async monitorIndexUsage(collection) {
    if (!this.isEnabled) return;

    try {
      const db = mongoose.connection.db;
      const indexStats = await db.collection(collection).indexStats().toArray();
      
      // Find unused indexes
      const unusedIndexes = indexStats.filter(stat => 
        stat.accesses.ops === 0 && 
        stat.name !== '_id_' // Exclude default _id index
      );
      
      if (unusedIndexes.length > 0) {
        console.warn(`📋 Unused indexes in ${collection}:`, unusedIndexes.map(idx => idx.name));
      }
      
      return indexStats;
      
    } catch (error) {
      databaseHealthService.recordError('index_monitoring', error.message);
    }
  }

  /**
   * Memory usage monitoring
   */
  async monitorMemoryUsage() {
    if (!this.isEnabled) return;

    try {
      const db = mongoose.connection.db;
      const serverStatus = await db.admin().serverStatus();
      
      const memoryUsage = {
        resident: serverStatus.mem.resident,
        virtual: serverStatus.mem.virtual,
        mapped: serverStatus.mem.mapped || 0
      };
      
      // Alert on high memory usage (over 1GB resident)
      if (memoryUsage.resident > 1024) {
        console.warn(`🧠 High memory usage: ${memoryUsage.resident}MB resident`);
      }
      
      return memoryUsage;
      
    } catch (error) {
      databaseHealthService.recordError('memory_monitoring', error.message);
    }
  }

  /**
   * Enable/disable monitoring
   */
  setEnabled(enabled) {
    this.isEnabled = enabled;
    console.log(`📊 Database monitoring ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Get monitoring statistics
   */
  getStats() {
    return {
      enabled: this.isEnabled,
      queryThreshold: this.queryThreshold,
      connectionState: mongoose.connection.readyState,
      connectionName: mongoose.connection.name,
      healthStatus: databaseHealthService.getHealthStatus()
    };
  }
}

// Create singleton instance
const databaseMonitoring = new DatabaseMonitoringMiddleware();

module.exports = databaseMonitoring;
