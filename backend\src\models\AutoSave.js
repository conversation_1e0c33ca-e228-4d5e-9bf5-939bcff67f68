const mongoose = require('mongoose');

const AutoSaveSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  cardId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Card',
    required: true
  },
  // Frequency of auto-debit
  frequency: {
    type: String,
    enum: ['daily', 'weekly', 'monthly'],
    required: true,
    index: true
  },
  // Amount to debit each time
  amount: {
    type: Number,
    required: true,
    min: [100, 'Minimum AutoSave amount is ₦100'],
    max: [100000, 'Maximum AutoSave amount is ₦100,000']
  },
  // Next scheduled debit date
  nextDebitDate: {
    type: Date,
    required: true,
    index: true
  },
  // Whether AutoSave is currently active
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  // Target savings plan to credit (optional)
  targetPlanId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SavingsPlan'
  },
  // Failure handling
  failureCount: {
    type: Number,
    default: 0,
    max: 3
  },
  maxRetries: {
    type: Number,
    default: 3,
    min: 1,
    max: 5
  },
  // Last successful debit
  lastSuccessfulDebit: {
    type: Date
  },
  lastFailedDebit: {
    type: Date
  },
  // Statistics
  stats: {
    totalDebits: {
      type: Number,
      default: 0
    },
    totalAmount: {
      type: Number,
      default: 0
    },
    successfulDebits: {
      type: Number,
      default: 0
    },
    failedDebits: {
      type: Number,
      default: 0
    },
    averageAmount: {
      type: Number,
      default: 0
    }
  },
  // Settings
  settings: {
    // Skip weekends for daily AutoSave
    skipWeekends: {
      type: Boolean,
      default: true
    },
    // Pause during specific months (e.g., December)
    pauseMonths: [{
      type: Number,
      min: 1,
      max: 12
    }],
    // Maximum amount per month
    monthlyLimit: {
      type: Number,
      default: 0 // 0 means no limit
    },
    // Notification preferences
    notifications: {
      beforeDebit: {
        type: Boolean,
        default: true
      },
      afterSuccess: {
        type: Boolean,
        default: true
      },
      afterFailure: {
        type: Boolean,
        default: true
      },
      hoursBeforeNotification: {
        type: Number,
        default: 2,
        min: 1,
        max: 24
      }
    }
  },
  // Metadata
  metadata: {
    createdVia: {
      type: String,
      enum: ['web', 'mobile', 'api'],
      default: 'web'
    },
    ipAddress: String,
    userAgent: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for success rate
AutoSaveSchema.virtual('successRate').get(function() {
  if (this.stats.totalDebits === 0) return 100;
  return (this.stats.successfulDebits / this.stats.totalDebits) * 100;
});

// Virtual for next debit amount (considering monthly limits)
AutoSaveSchema.virtual('nextDebitAmount').get(function() {
  if (!this.settings.monthlyLimit || this.settings.monthlyLimit === 0) {
    return this.amount;
  }
  
  // Calculate current month's total
  const now = new Date();
  const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  
  // This would need to be calculated from actual transactions
  // For now, return the regular amount
  return this.amount;
});

// Virtual for days until next debit
AutoSaveSchema.virtual('daysUntilNextDebit').get(function() {
  if (!this.isActive) return null;
  
  const now = new Date();
  const nextDebit = new Date(this.nextDebitDate);
  const diffTime = nextDebit - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return Math.max(0, diffDays);
});

// Pre-save middleware to calculate next debit date
AutoSaveSchema.pre('save', function(next) {
  if (this.isNew || this.isModified('frequency') || this.isModified('nextDebitDate')) {
    this.calculateNextDebitDate();
  }
  
  // Update average amount
  if (this.stats.totalDebits > 0) {
    this.stats.averageAmount = this.stats.totalAmount / this.stats.totalDebits;
  }
  
  next();
});

// Indexes for efficient queries
AutoSaveSchema.index({ userId: 1, isActive: 1 });
AutoSaveSchema.index({ nextDebitDate: 1, isActive: 1 });
AutoSaveSchema.index({ frequency: 1, isActive: 1 });
AutoSaveSchema.index({ failureCount: 1 });

// Static methods
AutoSaveSchema.statics.getDueDebits = function(date = new Date()) {
  return this.find({
    isActive: true,
    nextDebitDate: { $lte: date },
    failureCount: { $lt: 3 }
  }).populate('userId cardId targetPlanId');
};

AutoSaveSchema.statics.getUserAutoSaves = function(userId) {
  return this.find({ userId })
    .populate('cardId targetPlanId')
    .sort({ createdAt: -1 });
};

AutoSaveSchema.statics.getActiveAutoSaves = function(userId) {
  return this.find({ userId, isActive: true })
    .populate('cardId targetPlanId')
    .sort({ nextDebitDate: 1 });
};

// Instance methods
AutoSaveSchema.methods.calculateNextDebitDate = function() {
  const now = new Date();
  let nextDate = new Date(now);
  
  switch (this.frequency) {
    case 'daily':
      nextDate.setDate(nextDate.getDate() + 1);
      
      // Skip weekends if enabled
      if (this.settings.skipWeekends) {
        while (nextDate.getDay() === 0 || nextDate.getDay() === 6) {
          nextDate.setDate(nextDate.getDate() + 1);
        }
      }
      break;
      
    case 'weekly':
      nextDate.setDate(nextDate.getDate() + 7);
      break;
      
    case 'monthly':
      nextDate.setMonth(nextDate.getMonth() + 1);
      break;
  }
  
  // Check if next month is in pause months
  if (this.settings.pauseMonths && this.settings.pauseMonths.includes(nextDate.getMonth() + 1)) {
    // Skip to next month
    nextDate.setMonth(nextDate.getMonth() + 1);
    nextDate.setDate(1); // First day of next month
  }
  
  this.nextDebitDate = nextDate;
  return nextDate;
};

AutoSaveSchema.methods.recordSuccessfulDebit = function(amount) {
  this.stats.totalDebits += 1;
  this.stats.successfulDebits += 1;
  this.stats.totalAmount += amount;
  this.lastSuccessfulDebit = new Date();
  this.failureCount = 0; // Reset failure count on success
  
  // Calculate next debit date
  this.calculateNextDebitDate();
  
  return this.save();
};

AutoSaveSchema.methods.recordFailedDebit = function(reason) {
  this.stats.totalDebits += 1;
  this.stats.failedDebits += 1;
  this.failureCount += 1;
  this.lastFailedDebit = new Date();
  
  // If max failures reached, deactivate
  if (this.failureCount >= this.maxRetries) {
    this.isActive = false;
  } else {
    // Retry tomorrow
    const retryDate = new Date();
    retryDate.setDate(retryDate.getDate() + 1);
    this.nextDebitDate = retryDate;
  }
  
  return this.save();
};

AutoSaveSchema.methods.pause = function() {
  this.isActive = false;
  return this.save();
};

AutoSaveSchema.methods.resume = function() {
  this.isActive = true;
  this.failureCount = 0;
  this.calculateNextDebitDate();
  return this.save();
};

AutoSaveSchema.methods.updateAmount = function(newAmount) {
  this.amount = newAmount;
  return this.save();
};

AutoSaveSchema.methods.updateFrequency = function(newFrequency) {
  this.frequency = newFrequency;
  this.calculateNextDebitDate();
  return this.save();
};

AutoSaveSchema.methods.updateCard = function(newCardId) {
  this.cardId = newCardId;
  this.failureCount = 0; // Reset failures with new card
  return this.save();
};

// Check if AutoSave should be paused this month
AutoSaveSchema.methods.shouldPauseThisMonth = function() {
  const currentMonth = new Date().getMonth() + 1;
  return this.settings.pauseMonths && this.settings.pauseMonths.includes(currentMonth);
};

// Get monthly total for current month
AutoSaveSchema.methods.getMonthlyTotal = async function() {
  const now = new Date();
  const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  
  // This would need to query actual AutoSave transactions
  // For now, return estimated based on frequency
  const daysInMonth = monthEnd.getDate();
  const daysElapsed = now.getDate();
  
  let estimatedTotal = 0;
  switch (this.frequency) {
    case 'daily':
      estimatedTotal = this.amount * daysElapsed;
      break;
    case 'weekly':
      estimatedTotal = this.amount * Math.floor(daysElapsed / 7);
      break;
    case 'monthly':
      estimatedTotal = daysElapsed > 1 ? this.amount : 0;
      break;
  }
  
  return estimatedTotal;
};

module.exports = mongoose.model('AutoSave', AutoSaveSchema);
