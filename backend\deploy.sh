#!/bin/bash

# ===========================================
# BETTER INTEREST - BACKEND DEPLOYMENT SCRIPT
# ===========================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="better-interest-backend"
DOMAIN="api.kojaonline.store"
PORT=5000
NODE_ENV="production"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    log_info "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install Node.js 16 or higher."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    if ! command -v pm2 &> /dev/null; then
        log_warning "PM2 is not installed. Installing..."
        npm install -g pm2
    fi
    
    log_success "All dependencies are available."
}

# Validate environment variables
validate_environment() {
    log_info "Validating environment variables..."
    
    if [ ! -f ".env" ]; then
        log_error ".env file not found. Please create it from .env.example"
        exit 1
    fi
    
    # Check if required environment variables are set
    source .env
    
    if [ -z "$MONGODB_URI" ]; then
        log_error "MONGODB_URI is not set in .env file"
        exit 1
    fi
    
    if [ -z "$JWT_SECRET" ]; then
        log_error "JWT_SECRET is not set in .env file"
        exit 1
    fi
    
    log_success "Environment validation completed."
}

# Install dependencies
install_dependencies() {
    log_info "Installing dependencies..."
    npm ci --production
    log_success "Dependencies installed successfully."
}

# Run tests
run_tests() {
    log_info "Running tests..."
    
    # Install dev dependencies for testing
    npm ci
    
    # Run tests if available
    if [ -f "package.json" ] && grep -q "test" package.json; then
        npm test
        log_success "Tests passed."
    else
        log_warning "No tests found."
    fi
}

# Setup database
setup_database() {
    log_info "Setting up database..."
    
    # Test database connection
    node -e "
        require('dotenv').config();
        const mongoose = require('mongoose');
        mongoose.connect(process.env.MONGODB_URI)
            .then(() => {
                console.log('Database connection successful');
                process.exit(0);
            })
            .catch((err) => {
                console.error('Database connection failed:', err);
                process.exit(1);
            });
    "
    
    log_success "Database setup completed."
}

# Setup PM2 ecosystem
setup_pm2() {
    log_info "Setting up PM2 ecosystem..."
    
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: '${APP_NAME}',
    script: 'src/server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development',
      PORT: ${PORT}
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: ${PORT}
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max_old_space_size=1024'
  }]
};
EOF
    
    # Create logs directory
    mkdir -p logs
    
    log_success "PM2 ecosystem configured."
}

# Deploy application
deploy_application() {
    log_info "Deploying application..."
    
    # Stop existing application
    pm2 stop $APP_NAME || true
    pm2 delete $APP_NAME || true
    
    # Start application
    pm2 start ecosystem.config.js --env production
    
    # Save PM2 configuration
    pm2 save
    pm2 startup
    
    log_success "Application deployed successfully."
}

# Setup nginx (if available)
setup_nginx() {
    if command -v nginx &> /dev/null; then
        log_info "Setting up Nginx reverse proxy..."
        
        cat > /tmp/nginx-betterinterest << EOF
server {
    listen 80;
    server_name ${DOMAIN};
    
    location / {
        proxy_pass http://localhost:${PORT};
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF
        
        log_info "Nginx configuration created at /tmp/nginx-betterinterest"
        log_warning "Please copy this to your nginx sites-available directory and enable it."
    else
        log_warning "Nginx not found. Please configure your web server manually."
    fi
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    # Wait for application to start
    sleep 10
    
    # Check if application is running
    if pm2 list | grep -q $APP_NAME; then
        log_success "Application is running in PM2."
    else
        log_error "Application is not running in PM2."
        exit 1
    fi
    
    # Test API endpoint
    if curl -f -s "http://localhost:${PORT}/health" > /dev/null; then
        log_success "API health check passed."
    else
        log_warning "API health check failed. Please check application logs."
    fi
    
    log_success "Deployment verification completed."
}

# Show deployment info
show_deployment_info() {
    log_info "Deployment completed successfully!"
    echo ""
    echo "🌐 API URL: https://${DOMAIN}"
    echo "🔧 Local URL: http://localhost:${PORT}"
    echo "📊 PM2 Status: pm2 status"
    echo "📝 Logs: pm2 logs ${APP_NAME}"
    echo "🔄 Restart: pm2 restart ${APP_NAME}"
    echo ""
    echo "Next steps:"
    echo "1. Configure your domain DNS to point to this server"
    echo "2. Set up SSL certificate (Let's Encrypt recommended)"
    echo "3. Configure firewall to allow port 80 and 443"
    echo "4. Monitor application logs and performance"
    echo ""
}

# Main deployment function
main() {
    local skip_tests=${1:-false}
    
    log_info "Starting Better Interest Backend deployment..."
    log_info "Target domain: ${DOMAIN}"
    log_info "Skip tests: $skip_tests"
    
    # Pre-deployment checks
    check_dependencies
    validate_environment
    install_dependencies
    
    # Testing (unless skipped)
    if [ "$skip_tests" != "true" ]; then
        run_tests
    else
        log_warning "Skipping tests as requested."
    fi
    
    # Database setup
    setup_database
    
    # Application deployment
    setup_pm2
    deploy_application
    
    # Web server setup
    setup_nginx
    
    # Verification
    verify_deployment
    
    # Show info
    show_deployment_info
}

# Handle script arguments
case "${1:-}" in
    "skip-tests")
        main "true"
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [skip-tests]"
        echo ""
        echo "Options:"
        echo "  skip-tests  Skip running tests before deployment"
        echo "  help        Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0              # Deploy with tests"
        echo "  $0 skip-tests   # Deploy without tests"
        ;;
    *)
        main "false"
        ;;
esac
