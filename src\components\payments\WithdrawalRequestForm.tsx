
import React from "react";
import { PaystackWithdrawalForm } from "./PaystackWithdrawalForm";

interface WithdrawalRequestFormProps {
  onSubmit?: (data: any) => void;
  isLoading?: boolean;
}

export function WithdrawalRequestForm({ 
  onSubmit, 
  isLoading = false
}: WithdrawalRequestFormProps) {
  return (
    <PaystackWithdrawalForm 
      onSubmit={onSubmit}
      isLoading={isLoading}
    />
  );
}
