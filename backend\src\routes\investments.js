const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const Investment = require('../models/Investment');
const InvestmentProduct = require('../models/InvestmentProduct');
const User = require('../models/User');

// Get all investment products
router.get('/products', async (req, res) => {
  try {
    const { type, riskLevel, minAmount, maxAmount } = req.query;
    
    let filter = { isActive: true };
    
    if (type) filter.type = type;
    if (riskLevel) filter.riskLevel = riskLevel;
    if (minAmount || maxAmount) {
      filter.minimumAmount = {};
      if (minAmount) filter.minimumAmount.$gte = parseInt(minAmount);
      if (maxAmount) filter.minimumAmount.$lte = parseInt(maxAmount);
    }
    
    const products = await InvestmentProduct.find(filter)
      .sort({ ytdReturn: -1, minimumAmount: 1 });
    
    res.json({ success: true, data: products });
  } catch (error) {
    console.error('Error fetching investment products:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch investment products' });
  }
});

// Get user's investment portfolio
router.get('/portfolio', auth, async (req, res) => {
  try {
    const investments = await Investment.find({ userId: req.user.id, status: 'active' })
      .populate('productId', 'name type riskLevel')
      .sort({ investmentDate: -1 });
    
    // Calculate portfolio summary
    const totalValue = investments.reduce((sum, inv) => sum + inv.currentValue, 0);
    const totalInvested = investments.reduce((sum, inv) => sum + inv.amount, 0);
    const totalReturns = totalValue - totalInvested;
    
    const portfolio = {
      totalValue,
      totalInvested,
      totalReturns,
      returnPercentage: totalInvested > 0 ? (totalReturns / totalInvested) * 100 : 0,
      investments: investments.map(inv => ({
        id: inv._id,
        productName: inv.productId.name,
        productType: inv.productId.type,
        amount: inv.amount,
        units: inv.units,
        currentValue: inv.currentValue,
        profitLoss: inv.profitLoss,
        percentageGain: inv.percentageGain,
        investmentDate: inv.investmentDate,
        status: inv.status
      }))
    };
    
    res.json({ success: true, data: portfolio });
  } catch (error) {
    console.error('Error fetching portfolio:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch portfolio' });
  }
});

// Create new investment
router.post('/invest', auth, async (req, res) => {
  try {
    const { productId, amount } = req.body;
    
    if (!productId || !amount || amount <= 0) {
      return res.status(400).json({ 
        success: false, 
        message: 'Product ID and valid amount are required' 
      });
    }
    
    const product = await InvestmentProduct.findById(productId);
    if (!product || !product.isActive) {
      return res.status(404).json({ 
        success: false, 
        message: 'Investment product not found or inactive' 
      });
    }
    
    if (amount < product.minimumAmount) {
      return res.status(400).json({ 
        success: false, 
        message: `Minimum investment amount is ₦${product.minimumAmount}` 
      });
    }
    
    // For demo purposes, using a fixed unit price of ₦100
    const unitPrice = 100;
    const units = amount / unitPrice;
    
    const investment = new Investment({
      userId: req.user.id,
      productId: productId,
      amount: amount,
      units: units,
      unitPrice: unitPrice,
      currentValue: amount,
      status: 'active',
      investmentDate: new Date()
    });
    
    // Add initial transaction
    investment.addTransaction({
      type: 'buy',
      amount: amount,
      units: units,
      unitPrice: unitPrice,
      description: `Initial investment in ${product.name}`,
      date: new Date(),
      reference: `INV_${Date.now()}`
    });
    
    await investment.save();
    
    // Update product metadata
    await InvestmentProduct.findByIdAndUpdate(productId, {
      $inc: { 
        'metadata.totalInvestments': amount,
        'metadata.totalInvestors': 1
      }
    });
    
    await investment.populate('productId', 'name type riskLevel');
    
    res.status(201).json({ 
      success: true, 
      data: investment,
      message: 'Investment created successfully' 
    });
  } catch (error) {
    console.error('Error creating investment:', error);
    res.status(500).json({ success: false, message: 'Failed to create investment' });
  }
});

// Get investment details
router.get('/:investmentId', auth, async (req, res) => {
  try {
    const investment = await Investment.findOne({
      _id: req.params.investmentId,
      userId: req.user.id
    }).populate('productId');
    
    if (!investment) {
      return res.status(404).json({ 
        success: false, 
        message: 'Investment not found' 
      });
    }
    
    res.json({ success: true, data: investment });
  } catch (error) {
    console.error('Error fetching investment:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch investment' });
  }
});

// Liquidate investment
router.post('/:investmentId/liquidate', auth, async (req, res) => {
  try {
    const investment = await Investment.findOne({
      _id: req.params.investmentId,
      userId: req.user.id
    }).populate('productId');
    
    if (!investment) {
      return res.status(404).json({ 
        success: false, 
        message: 'Investment not found' 
      });
    }
    
    if (investment.status !== 'active') {
      return res.status(400).json({ 
        success: false, 
        message: 'Investment is not active' 
      });
    }
    
    // Calculate liquidation value (current value minus any exit fees)
    const exitFee = investment.productId.fees?.exitFee || 0;
    const liquidationValue = investment.currentValue - (investment.currentValue * exitFee / 100);
    
    investment.status = 'liquidated';
    investment.liquidationDate = new Date();
    investment.currentValue = liquidationValue;
    
    // Add liquidation transaction
    await investment.addTransaction({
      type: 'sell',
      amount: liquidationValue,
      units: investment.units,
      unitPrice: liquidationValue / investment.units,
      description: `Liquidation of ${investment.productId.name}`,
      date: new Date(),
      reference: `LIQ_${Date.now()}`
    });
    
    res.json({ 
      success: true, 
      data: investment,
      message: 'Investment liquidated successfully' 
    });
  } catch (error) {
    console.error('Error liquidating investment:', error);
    res.status(500).json({ success: false, message: 'Failed to liquidate investment' });
  }
});

// Admin routes
// Get all investments (admin only)
router.get('/admin/all', [auth, async (req, res, next) => {
  const user = await User.findById(req.user.id);
  if (!user || !['admin', 'staff'].includes(user.role)) {
    return res.status(403).json({ success: false, message: 'Access denied' });
  }
  next();
}], async (req, res) => {
  try {
    const { page = 1, limit = 50 } = req.query;
    
    const investments = await Investment.find()
      .populate('userId', 'firstName lastName email')
      .populate('productId', 'name type riskLevel')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await Investment.countDocuments();
    
    res.json({
      success: true,
      data: {
        investments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching all investments:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch investments' });
  }
});

// Update investment product (admin only)
router.put('/admin/products/:productId', [auth, async (req, res, next) => {
  const user = await User.findById(req.user.id);
  if (!user || !['admin', 'staff'].includes(user.role)) {
    return res.status(403).json({ success: false, message: 'Access denied' });
  }
  next();
}], async (req, res) => {
  try {
    const product = await InvestmentProduct.findByIdAndUpdate(
      req.params.productId,
      req.body,
      { new: true, runValidators: true }
    );
    
    if (!product) {
      return res.status(404).json({ 
        success: false, 
        message: 'Investment product not found' 
      });
    }
    
    res.json({ success: true, data: product });
  } catch (error) {
    console.error('Error updating investment product:', error);
    res.status(500).json({ success: false, message: 'Failed to update investment product' });
  }
});

module.exports = router;