
import { useState } from "react";
import { NeumorphismButton, NeumorphismCard, NeumorphismCardContent, NeumorphismCardDescription, NeumorphismCardHeader, NeumorphismCardTitle } from "@/components/ui/neumorphism-card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CreditCard, Shield, Zap } from "lucide-react";
import { toast } from "sonner";
import { paymentsService } from "@/services/payments";
import { useAuth } from "@/hooks/use-auth";

interface PaystackPaymentProps {
  onSuccess: (amount: number) => void;
  onError: (error: string) => void;
}

export const PaystackPayment = ({ onSuccess, onError }: PaystackPaymentProps) => {
  const [amount, setAmount] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();

  const handlePayment = async () => {
    if (!amount || parseFloat(amount) < 100) {
      toast.error("Minimum deposit amount is ₦100");
      return;
    }

    if (!user?.email) {
      toast.error("User email is required for payment");
      return;
    }

    setIsLoading(true);
    
    try {
      // Initialize Paystack payment
      const paymentData = await paymentsService.initializePaystackPayment(
        parseFloat(amount),
        user.email,
        {
          userId: user.id,
          purpose: 'wallet_funding'
        }
      );

      // Redirect to Paystack payment page
      window.location.href = paymentData.authorizationUrl;
      
    } catch (error: any) {
      setIsLoading(false);
      const errorMessage = error.message || "Payment initialization failed";
      onError(errorMessage);
      toast.error(errorMessage);
    }
  };

  return (
    <NeumorphismCard className="w-full max-w-md">
      <NeumorphismCardHeader className="text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <CreditCard className="h-5 w-5 text-primary" />
          <NeumorphismCardTitle>Deposit Funds</NeumorphismCardTitle>
        </div>
        <NeumorphismCardDescription>
          Secure automatic payment via Paystack
        </NeumorphismCardDescription>
      </NeumorphismCardHeader>
      <NeumorphismCardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="amount">Amount (₦)</Label>
          <Input
            id="amount"
            type="number"
            placeholder="Enter amount"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            min="100"
            className="rounded-none border-0 shadow-[inset_4px_4px_8px_rgba(0,0,0,0.1),inset_-4px_-4px_8px_rgba(255,255,255,0.1)] focus:shadow-[inset_6px_6px_12px_rgba(0,0,0,0.15),inset_-6px_-6px_12px_rgba(255,255,255,0.15)]"
          />
          <p className="text-xs text-muted-foreground">Minimum: ₦100</p>
        </div>

        <div className="flex items-center gap-2 p-3 bg-background shadow-[inset_2px_2px_4px_rgba(34,197,94,0.1),inset_-2px_-2px_4px_rgba(255,255,255,0.1)] rounded-none">
          <Shield className="h-4 w-4 text-green-600" />
          <span className="text-sm text-green-700 dark:text-green-400">
            Secured by Paystack SSL encryption
          </span>
        </div>

        <NeumorphismButton 
          onClick={handlePayment} 
          disabled={isLoading || !amount}
          variant="primary"
          className="w-full gap-2"
        >
          {isLoading ? (
            <>
              <span className="animate-spin">⟳</span>
              Processing...
            </>
          ) : (
            <>
              <Zap className="h-4 w-4" />
              Pay ₦{amount || "0"}
            </>
          )}
        </NeumorphismButton>

        <div className="text-center">
          <p className="text-xs text-muted-foreground">
            Your payment is secured and encrypted
          </p>
        </div>
      </NeumorphismCardContent>
    </NeumorphismCard>
  );
};
