import { FintechCard } from '@/components/ui/fintech-card';
import { StatCard } from '@/components/ui/stat-card';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ActivityTimeline } from '@/components/analytics/ActivityTimeline';
import { SavingsChart } from '@/components/analytics/SavingsChart';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useNavigate } from 'react-router-dom';
import { 
  Users, 
  Wallet, 
  TrendingUp, 
  AlertCircle,
  CreditCard,
  FileText,
  Clock,
  CheckCircle,
  DollarSign,
  Target,
  PieChart,
  ArrowUpRight,
  ArrowDownRight,
  Shield,
  Settings,
  Bell,
  Eye
} from 'lucide-react';

const AdminDashboard = () => {
  const navigate = useNavigate();

  // Enhanced admin stats with modern design
  const enhancedStats = [
    {
      title: "Total Users",
      value: "12,543",
      change: "+8.2%",
      changeType: "increase",
      icon: <Users className="h-5 w-5" />,
      color: "from-green-500 to-emerald-600",
      bgGradient: "from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20",
      trend: "up"
    },
    {
      title: "Total Deposits",
      value: "₦2.4B",
      change: "+12.5%",
      changeType: "increase",
      icon: <Wallet className="h-5 w-5" />,
      color: "from-green-500 to-emerald-600",
      bgGradient: "from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20",
      trend: "up"
    },
    {
      title: "Active Loans",
      value: "₦450M",
      change: "-2.1%",
      changeType: "decrease",
      icon: <CreditCard className="h-5 w-5" />,
      color: "from-orange-500 to-red-500",
      bgGradient: "from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20",
      trend: "down"
    },
    {
      title: "Pending KYC",
      value: "234",
      change: "+15.3%",
      changeType: "increase",
      icon: <AlertCircle className="h-5 w-5" />,
      color: "from-purple-500 to-violet-600",
      bgGradient: "from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20",
      trend: "up"
    }
  ];

  // Priority Actions - Inspired by the cybersecurity dashboard
  const priorityActions = [
    {
      title: "KYC Verification Pending",
      description: "Review 234 pending KYC applications",
      value: "234",
      change: "+12%",
      status: "warning",
      action: () => navigate('/admin/verification')
    },
    {
      title: "Withdrawal Requests",
      description: "Process pending withdrawal requests",
      value: "47",
      change: "+5%", 
      status: "info",
      action: () => navigate('/admin/requests')
    },
    {
      title: "System Health",
      description: "All systems operational",
      value: "98%",
      change: "+2%",
      status: "success", 
      action: () => navigate('/admin/analytics')
    }
  ];

  // Quick insights inspired by accounting dashboard
  const quickInsights = [
    { label: "New Users Today", value: "127", icon: <Users className="h-4 w-4" />, color: "text-blue-600" },
    { label: "Revenue This Month", value: "₦1.2M", icon: <DollarSign className="h-4 w-4" />, color: "text-green-600" },
    { label: "Active Savings Plans", value: "1,847", icon: <Target className="h-4 w-4" />, color: "text-purple-600" },
    { label: "Support Tickets", value: "23", icon: <FileText className="h-4 w-4" />, color: "text-orange-600" }
  ];

  return (
    <div className="space-y-6 p-4 lg:p-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8" data-aos="fade-down">
        <div>
          <h1 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
            Admin Control Center 🎛️
          </h1>
          <p className="text-muted-foreground mt-1">Comprehensive overview of your financial platform</p>
        </div>
        <div className="flex items-center gap-3 mt-4 lg:mt-0">
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
          <Button onClick={() => navigate('/admin/notifications')} className="gap-2">
            <Bell className="h-4 w-4" />
            Notifications
          </Button>
        </div>
      </div>

      {/* Enhanced Stats Grid - Mobile Responsive */}
      <div className="mobile-grid gap-3 sm:gap-4 lg:gap-6" data-aos="fade-up" data-aos-delay="100">
        {enhancedStats.map((stat, index) => (
          <Card 
            key={index} 
            className={`mobile-card relative overflow-hidden border-0 bg-gradient-to-br ${stat.bgGradient} hover:shadow-lg transition-all duration-300 hover:scale-105 active:scale-95 cursor-pointer`}
            data-aos="zoom-in"
            data-aos-delay={100 + index * 50}
          >
            <CardContent className="p-3 sm:p-4 lg:p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-xs sm:text-sm font-medium text-muted-foreground">{stat.title}</p>
                  <p className="text-lg sm:text-xl lg:text-2xl font-bold">{stat.value}</p>
                  <div className="flex items-center gap-1">
                    {stat.trend === "up" ? (
                      <ArrowUpRight className="h-3 w-3 text-green-600" />
                    ) : (
                      <ArrowDownRight className="h-3 w-3 text-red-600" />
                    )}
                    <span className={`text-xs font-medium ${stat.trend === "up" ? "text-green-600" : "text-red-600"}`}>
                      {stat.change}
                    </span>
                  </div>
                </div>
                <div className={`p-2 sm:p-3 rounded-full bg-gradient-to-r ${stat.color} text-white shadow-lg`}>
                  {stat.icon}
                </div>
              </div>
              {/* Decorative gradient overlay */}
              <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-10 translate-x-10" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Priority Actions & Analytics Grid - Mobile Responsive */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6" data-aos="fade-up" data-aos-delay="200">
        
        {/* Priority Actions - Mobile Responsive */}
        <div className="lg:col-span-1">
          <Card className="mobile-card border-0 bg-gradient-to-br from-card to-accent/5 shadow-lg">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-orange-600" />
                Priority Actions
              </CardTitle>
              <CardDescription>Items requiring immediate attention</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {priorityActions.map((action, index) => (
                <div 
                  key={index}
                  className="p-4 rounded-lg border border-border/50 hover:shadow-md transition-all duration-200 cursor-pointer"
                  onClick={action.action}
                  data-aos="slide-right"
                  data-aos-delay={250 + index * 50}
                >
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant={action.status === 'warning' ? 'destructive' : action.status === 'success' ? 'default' : 'secondary'}>
                      {action.status}
                    </Badge>
                    <span className="text-sm text-muted-foreground">{action.change}</span>
                  </div>
                  <h4 className="font-semibold text-sm mb-1">{action.title}</h4>
                  <p className="text-xs text-muted-foreground mb-2">{action.description}</p>
                  <div className="text-lg font-bold">{action.value}</div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Main Analytics Area - Mobile Responsive */}
        <div className="lg:col-span-2 space-y-4 lg:space-y-6">
          
          {/* Quick Insights Grid - Mobile Responsive */}
          <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-2 sm:gap-3 lg:gap-4" data-aos="fade-left" data-aos-delay="300">
            {quickInsights.map((insight, index) => (
              <Card 
                key={index} 
                className="mobile-card-compact border-0 bg-gradient-to-br from-background to-muted/5 hover:shadow-md transition-all duration-200 active:scale-95"
                data-aos="zoom-in"
                data-aos-delay={350 + index * 25}
              >
                <CardContent className="p-2 sm:p-3 lg:p-4 text-center">
                  <div className={`inline-flex p-1.5 sm:p-2 rounded-full bg-muted/50 mb-1 sm:mb-2 ${insight.color}`}>
                    {insight.icon}
                  </div>
                  <p className="text-sm sm:text-base lg:text-lg font-bold">{insight.value}</p>
                  <p className="text-xs text-muted-foreground truncate">{insight.label}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Activity Timeline */}
          <Card className="border-0 bg-gradient-to-br from-card to-muted/5 shadow-lg" data-aos="fade-left" data-aos-delay="400">
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2 text-sm sm:text-base">
                  <Clock className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
                  Recent System Activity
                </CardTitle>
                <CardDescription>Latest platform events and transactions</CardDescription>
              </div>
              <Button variant="ghost" size="sm" onClick={() => navigate('/admin/analytics')}>
                View Details <ArrowUpRight className="h-4 w-4 ml-1" />
              </Button>
            </CardHeader>
            <CardContent>
              <ActivityTimeline />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* System Overview Cards - Mobile Responsive */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6" data-aos="fade-up" data-aos-delay="500">
        
        {/* User Growth Chart */}
        <Card className="mobile-card border-0 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-700 text-sm sm:text-base">
              <TrendingUp className="h-4 w-4 sm:h-5 sm:w-5" />
              User Growth
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 sm:space-y-4">
              <div className="text-xl sm:text-2xl font-bold text-green-700">+24.5%</div>
              <p className="text-sm text-muted-foreground">Growth compared to last month</p>
              <Progress value={75} className="h-2" />
            </div>
          </CardContent>
        </Card>

        {/* Revenue Analytics */}
        <Card className="mobile-card border-0 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-700">
              <DollarSign className="h-5 w-5" />
              Revenue Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <SavingsChart />
          </CardContent>
        </Card>

        {/* System Health */}
        <Card className="mobile-card border-0 bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-purple-700">
              <Shield className="h-5 w-5" />
              System Health
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Server Uptime</span>
                <span className="font-medium text-green-600">99.9%</span>
              </div>
              <Progress value={99.9} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Database Performance</span>
                <span className="font-medium text-blue-600">98.2%</span>
              </div>
              <Progress value={98.2} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>API Response Time</span>
                <span className="font-medium text-orange-600">125ms</span>
              </div>
              <Progress value={85} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Feature Highlight for Admins */}
      <Card className="overflow-hidden border-0 bg-gradient-to-r from-primary/10 via-accent/10 to-secondary/10 shadow-lg" data-aos="fade-up" data-aos-delay="600">
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-4">
            <div className="flex items-center gap-4">
              <div className="p-3 rounded-full bg-gradient-to-r from-primary to-primary/80 text-white shadow-lg">
                <PieChart className="h-6 w-6" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">Advanced Analytics Available</h3>
                <p className="text-sm text-muted-foreground">Get deeper insights into user behavior and platform performance</p>
              </div>
            </div>
            <Button 
              onClick={() => navigate('/admin/analytics')}
              className="bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-md whitespace-nowrap"
            >
              <Eye className="h-4 w-4 mr-2" />
              View Analytics
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminDashboard;