import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Coins, TrendingUp, Target, Settings } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface Transaction {
  id: string;
  amount: number;
  roundUp: number;
  description: string;
  date: string;
}

interface RoundUpSettings {
  enabled: boolean;
  multiplier: number; // 1x, 2x, 3x round up
  goalAmount: number;
}

export const RoundUpSavings = () => {
  const { toast } = useToast();
  const [settings, setSettings] = useState<RoundUpSettings>({
    enabled: true,
    multiplier: 1,
    goalAmount: 50000
  });
  const [totalSaved, setTotalSaved] = useState(12450);
  const [monthlyTarget] = useState(5000);
  const [recentTransactions] = useState<Transaction[]>([
    {
      id: '1',
      amount: 2350,
      roundUp: 650,
      description: 'Coffee at Cafe Neo',
      date: '2024-01-15'
    },
    {
      id: '2',
      amount: 15750,
      roundUp: 250,
      description: 'Grocery Shopping',
      date: '2024-01-14'
    },
    {
      id: '3',
      amount: 8900,
      roundUp: 1100,
      description: 'Uber Ride',
      date: '2024-01-13'
    }
  ]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const calculateRoundUp = (amount: number, multiplier: number = 1) => {
    const roundedUp = Math.ceil(amount / 100) * 100;
    return (roundedUp - amount) * multiplier;
  };

  const handleSettingsChange = (key: keyof RoundUpSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    toast({
      title: "Settings Updated",
      description: "Your round-up settings have been saved.",
    });
  };

  const progress = (totalSaved / settings.goalAmount) * 100;

  return (
    <div className="space-y-6">
      {/* Main Round-Up Card */}
      <Card className="bg-gradient-to-br from-primary/5 to-secondary/5 border-primary/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Coins className="h-6 w-6 text-primary" />
              <CardTitle className="text-xl">Round-Up Savings</CardTitle>
            </div>
            <Badge variant="secondary" className="bg-green-100 text-green-700">
              Active
            </Badge>
          </div>
          <CardDescription>
            Automatically save your spare change from every transaction
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Savings Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center space-y-2">
              <p className="text-sm text-muted-foreground">Total Saved</p>
              <p className="text-2xl font-bold text-primary">
                {formatCurrency(totalSaved)}
              </p>
            </div>
            <div className="text-center space-y-2">
              <p className="text-sm text-muted-foreground">This Month</p>
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(3450)}
              </p>
            </div>
            <div className="text-center space-y-2">
              <p className="text-sm text-muted-foreground">Avg. Round-Up</p>
              <p className="text-2xl font-bold text-blue-600">
                {formatCurrency(667)}
              </p>
            </div>
          </div>

          {/* Goal Progress */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Target className="h-4 w-4 text-primary" />
                <span className="text-sm font-medium">Goal Progress</span>
              </div>
              <span className="text-sm text-muted-foreground">
                {formatCurrency(totalSaved)} / {formatCurrency(settings.goalAmount)}
              </span>
            </div>
            <Progress value={progress} className="h-2" />
            <p className="text-xs text-muted-foreground">
              {Math.round(progress)}% complete • {formatCurrency(settings.goalAmount - totalSaved)} to go
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Settings Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Settings className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">Round-Up Settings</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Enable/Disable */}
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Enable Round-Up</p>
              <p className="text-sm text-muted-foreground">
                Automatically round up your transactions
              </p>
            </div>
            <Switch
              checked={settings.enabled}
              onCheckedChange={(checked) => handleSettingsChange('enabled', checked)}
            />
          </div>

          {/* Multiplier */}
          <div className="space-y-3">
            <p className="font-medium">Round-Up Multiplier</p>
            <div className="grid grid-cols-3 gap-3">
              {[1, 2, 3].map((mult) => (
                <Button
                  key={mult}
                  variant={settings.multiplier === mult ? "default" : "outline"}
                  onClick={() => handleSettingsChange('multiplier', mult)}
                  className="h-12"
                >
                  {mult}x
                </Button>
              ))}
            </div>
            <p className="text-xs text-muted-foreground">
              Higher multipliers save more but may impact your spending money
            </p>
          </div>

          {/* Goal Amount */}
          <div className="space-y-3">
            <p className="font-medium">Savings Goal</p>
            <div className="grid grid-cols-2 gap-3">
              {[25000, 50000, 100000, 200000].map((amount) => (
                <Button
                  key={amount}
                  variant={settings.goalAmount === amount ? "default" : "outline"}
                  onClick={() => handleSettingsChange('goalAmount', amount)}
                  className="h-10"
                >
                  {formatCurrency(amount)}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Transactions */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">Recent Round-Ups</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentTransactions.map((transaction) => (
              <div
                key={transaction.id}
                className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
              >
                <div>
                  <p className="font-medium">{transaction.description}</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(transaction.date).toLocaleDateString()}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-muted-foreground">
                    {formatCurrency(transaction.amount)}
                  </p>
                  <p className="text-sm font-medium text-green-600">
                    +{formatCurrency(transaction.roundUp)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};