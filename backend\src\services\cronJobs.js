const cron = require('node-cron');
const Ticket = require('../models/Ticket');
const User = require('../models/User');
const { sendEmail } = require('./emailService');

// Auto-escalate tickets based on priority and response time
const autoEscalateTickets = async () => {
  try {
    console.log('Running ticket escalation check...');
    
    const now = new Date();
    const urgentThreshold = new Date(now.getTime() - 60 * 60 * 1000); // 1 hour
    const highThreshold = new Date(now.getTime() - 2 * 60 * 60 * 1000); // 2 hours
    const mediumThreshold = new Date(now.getTime() - 4 * 60 * 60 * 1000); // 4 hours
    
    // Find tickets that need escalation
    const ticketsToEscalate = await Ticket.find({
      status: { $in: ['open', 'in_progress'] },
      $or: [
        { 
          priority: 'urgent', 
          lastActivity: { $lt: urgentThreshold },
          escalationLevel: { $lt: 3 }
        },
        { 
          priority: 'high', 
          lastActivity: { $lt: highThreshold },
          escalationLevel: { $lt: 2 }
        },
        { 
          priority: 'medium', 
          lastActivity: { $lt: mediumThreshold },
          escalationLevel: { $lt: 1 }
        }
      ]
    }).populate('userId assignedTo');

    for (const ticket of ticketsToEscalate) {
      ticket.escalationLevel += 1;
      ticket.escalatedAt = now;
      
      await ticket.save();
      
      // Send escalation email
      const managers = await User.find({ 
        role: { $in: ['admin', 'manager'] },
        isActive: true 
      });
      
      for (const manager of managers) {
        await sendEmail({
          to: manager.email,
          template: 'ticket-escalated',
          data: {
            ticketId: ticket.ticketId,
            subject: ticket.subject,
            priority: ticket.priority,
            escalationLevel: ticket.escalationLevel
          }
        });
      }
      
      console.log(`Escalated ticket ${ticket.ticketId} to level ${ticket.escalationLevel}`);
    }
    
    console.log(`Escalation check completed. ${ticketsToEscalate.length} tickets escalated.`);
  } catch (error) {
    console.error('Auto-escalation error:', error);
  }
};

// Close resolved tickets after satisfaction survey
const autoCloseResolvedTickets = async () => {
  try {
    console.log('Running auto-close resolved tickets...');
    
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    
    const ticketsToClose = await Ticket.find({
      status: 'resolved',
      resolvedAt: { $lt: sevenDaysAgo }
    });
    
    for (const ticket of ticketsToClose) {
      ticket.status = 'closed';
      await ticket.save();
      console.log(`Auto-closed ticket ${ticket.ticketId}`);
    }
    
    console.log(`Auto-close completed. ${ticketsToClose.length} tickets closed.`);
  } catch (error) {
    console.error('Auto-close error:', error);
  }
};

// Send daily support summary
const sendDailySupportSummary = async () => {
  try {
    console.log('Generating daily support summary...');
    
    const today = new Date();
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    
    const [
      newTickets,
      resolvedTickets,
      openTickets,
      urgentTickets
    ] = await Promise.all([
      Ticket.countDocuments({
        createdAt: { $gte: yesterday, $lt: today }
      }),
      Ticket.countDocuments({
        resolvedAt: { $gte: yesterday, $lt: today }
      }),
      Ticket.countDocuments({
        status: { $in: ['open', 'in_progress'] }
      }),
      Ticket.countDocuments({
        status: { $in: ['open', 'in_progress'] },
        priority: 'urgent'
      })
    ]);
    
    const summary = {
      date: yesterday.toDateString(),
      newTickets,
      resolvedTickets,
      openTickets,
      urgentTickets
    };
    
    // Send to managers
    const managers = await User.find({ 
      role: { $in: ['admin', 'manager'] },
      isActive: true 
    });
    
    for (const manager of managers) {
      await sendEmail({
        to: manager.email,
        subject: `Daily Support Summary - ${summary.date}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>Daily Support Summary</h2>
            <p><strong>Date:</strong> ${summary.date}</p>
            <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
              <tr style="background: #f5f5f5;">
                <td style="padding: 10px; border: 1px solid #ddd;"><strong>Metric</strong></td>
                <td style="padding: 10px; border: 1px solid #ddd;"><strong>Count</strong></td>
              </tr>
              <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">New Tickets</td>
                <td style="padding: 10px; border: 1px solid #ddd;">${summary.newTickets}</td>
              </tr>
              <tr style="background: #f9f9f9;">
                <td style="padding: 10px; border: 1px solid #ddd;">Resolved Tickets</td>
                <td style="padding: 10px; border: 1px solid #ddd;">${summary.resolvedTickets}</td>
              </tr>
              <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">Open Tickets</td>
                <td style="padding: 10px; border: 1px solid #ddd;">${summary.openTickets}</td>
              </tr>
              <tr style="background: #f9f9f9;">
                <td style="padding: 10px; border: 1px solid #ddd;">Urgent Tickets</td>
                <td style="padding: 10px; border: 1px solid #ddd; ${summary.urgentTickets > 0 ? 'color: red; font-weight: bold;' : ''}">${summary.urgentTickets}</td>
              </tr>
            </table>
          </div>
        `
      });
    }
    
    console.log('Daily support summary sent successfully');
  } catch (error) {
    console.error('Daily summary error:', error);
  }
};

// Start all cron jobs
const startCronJobs = () => {
  console.log('Starting cron jobs...');
  
  // Auto-escalate tickets every 30 minutes
  cron.schedule('*/30 * * * *', autoEscalateTickets);
  
  // Auto-close resolved tickets daily at 2 AM
  cron.schedule('0 2 * * *', autoCloseResolvedTickets);
  
  // Send daily summary at 9 AM
  cron.schedule('0 9 * * *', sendDailySupportSummary);
  
  console.log('Cron jobs started successfully');
};

module.exports = {
  startCronJobs,
  autoEscalateTickets,
  autoCloseResolvedTickets,
  sendDailySupportSummary
};