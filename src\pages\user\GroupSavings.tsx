import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Plus, Search, Users, TrendingUp, Target, AlertCircle } from 'lucide-react';
import { GroupSavingsCard } from '@/components/group-savings/GroupSavingsCard';
import { CreateGroupDialog } from '@/components/group-savings/CreateGroupDialog';
import { JoinGroupDialog } from '@/components/group-savings/JoinGroupDialog';
import { GroupSavingsPlan, groupSavingsService } from '@/services/group-savings';
import { useAuth } from '@/hooks/use-auth';
import { toast } from 'sonner';
import { useIsMobile } from '@/hooks/use-mobile';

export const GroupSavings: React.FC = () => {
  const { user } = useAuth();
  const isMobile = useIsMobile();
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('discover');
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [joinDialogOpen, setJoinDialogOpen] = useState(false);
  
  // Data states
  const [allGroups, setAllGroups] = useState<GroupSavingsPlan[]>([]);
  const [myGroups, setMyGroups] = useState<GroupSavingsPlan[]>([]);
  const [stats, setStats] = useState({
    totalGroups: 0,
    activeGroups: 0,
    totalSaved: 0,
    completedGoals: 0
  });

  // Load data
  useEffect(() => {
    loadData();
  }, [user?.id]);

  const loadData = async () => {
    if (!user?.id) return;
    
    setLoading(true);
    try {
      // Load all groups for discovery
      const [allGroupsData, userGroupsData] = await Promise.all([
        groupSavingsService.getGroups({ status: 'active' }),
        groupSavingsService.getUserGroups(user.id)
      ]);

      setAllGroups(allGroupsData);
      setMyGroups(userGroupsData);

      // Calculate stats
      const activeUserGroups = userGroupsData.filter(g => g.status === 'active');
      const completedUserGroups = userGroupsData.filter(g => g.status === 'completed');
      const totalSaved = userGroupsData.reduce((sum, group) => sum + group.metadata.totalCollected, 0);

      setStats({
        totalGroups: userGroupsData.length,
        activeGroups: activeUserGroups.length,
        totalSaved,
        completedGoals: completedUserGroups.length
      });
    } catch (error) {
      console.error('Error loading group savings data:', error);
      toast.error('Failed to load group savings data');
    } finally {
      setLoading(false);
    }
  };

  const handleGroupCreated = (group: GroupSavingsPlan) => {
    setMyGroups(prev => [group, ...prev]);
    setStats(prev => ({
      ...prev,
      totalGroups: prev.totalGroups + 1,
      activeGroups: prev.activeGroups + 1
    }));
    toast.success('Group created successfully!');
  };

  const handleGroupJoined = (group: GroupSavingsPlan) => {
    setMyGroups(prev => [group, ...prev]);
    setStats(prev => ({
      ...prev,
      totalGroups: prev.totalGroups + 1,
      activeGroups: prev.activeGroups + 1
    }));
    toast.success('Successfully joined group!');
  };

  const filteredGroups = (groups: GroupSavingsPlan[]) => {
    return groups.filter(group => 
      group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      group.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      group.category.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  // Get groups user is not already a member of
  const discoverableGroups = allGroups.filter(group => 
    !myGroups.some(myGroup => myGroup._id === group._id)
  );

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-4 space-y-6">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-foreground">Group Savings</h1>
            <p className="text-muted-foreground">
              Save together, achieve more. Join or create group savings plans.
            </p>
          </div>
          
          <div className="flex gap-2">
            <Button
              onClick={() => setJoinDialogOpen(true)}
              variant="outline"
              size={isMobile ? "sm" : "default"}
              className="flex-1 sm:flex-none"
            >
              Join Group
            </Button>
            <Button
              onClick={() => setCreateDialogOpen(true)}
              size={isMobile ? "sm" : "default"}
              className="flex-1 sm:flex-none bg-primary hover:bg-primary/90"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Group
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-blue-500" />
                <div>
                  <p className="text-xs text-muted-foreground">Total Groups</p>
                  <p className="text-lg font-semibold">{stats.totalGroups}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-green-500" />
                <div>
                  <p className="text-xs text-muted-foreground">Active</p>
                  <p className="text-lg font-semibold">{stats.activeGroups}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-purple-500" />
                <div>
                  <p className="text-xs text-muted-foreground">Total Saved</p>
                  <p className="text-sm font-semibold">₦{stats.totalSaved.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Badge className="bg-green-500 hover:bg-green-600">
                  {stats.completedGoals}
                </Badge>
                <div>
                  <p className="text-xs text-muted-foreground">Completed</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search groups by name, description, or category..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="w-full">
          <TabsTrigger value="discover" className="flex-1">
            Discover Groups
          </TabsTrigger>
          <TabsTrigger value="my-groups" className="flex-1">
            My Groups ({myGroups.length})
          </TabsTrigger>
        </TabsList>

        {/* Discover Groups Tab */}
        <TabsContent value="discover" className="space-y-4">
          {filteredGroups(discoverableGroups).length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Users className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Groups Found</h3>
                <p className="text-muted-foreground text-center max-w-md">
                  {searchQuery 
                    ? "No groups match your search criteria. Try different keywords."
                    : "No active groups available to join right now. Create your own group to get started!"
                  }
                </p>
                {!searchQuery && (
                  <Button
                    onClick={() => setCreateDialogOpen(true)}
                    className="mt-4"
                  >
                    Create First Group
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredGroups(discoverableGroups).map((group) => (
                <GroupSavingsCard
                  key={group._id}
                  plan={group}
                  showJoinButton={true}
                  showWarning={true}
                  onJoin={(plan) => {
                    // Could implement direct join or open join dialog with pre-filled code
                    setJoinDialogOpen(true);
                  }}
                />
              ))}
            </div>
          )}
        </TabsContent>

        {/* My Groups Tab */}
        <TabsContent value="my-groups" className="space-y-4">
          {myGroups.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Groups Yet</h3>
                <p className="text-muted-foreground text-center max-w-md">
                  You haven't joined any group savings plans yet. Join an existing group or create your own!
                </p>
                <div className="flex gap-2 mt-4">
                  <Button
                    onClick={() => setJoinDialogOpen(true)}
                    variant="outline"
                  >
                    Join Group
                  </Button>
                  <Button
                    onClick={() => setCreateDialogOpen(true)}
                  >
                    Create Group
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : filteredGroups(myGroups).length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Search className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Results</h3>
                <p className="text-muted-foreground text-center">
                  No groups match your search criteria.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredGroups(myGroups).map((group) => (
                <GroupSavingsCard
                  key={group._id}
                  plan={group}
                  showJoinButton={false}
                  showWarning={false}
                />
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Dialogs */}
      <CreateGroupDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onGroupCreated={handleGroupCreated}
        currentUserId={user?.id || ''}
      />

      <JoinGroupDialog
        open={joinDialogOpen}
        onOpenChange={setJoinDialogOpen}
        onGroupJoined={handleGroupJoined}
        currentUserId={user?.id || ''}
        currentUserName={user?.email || 'User'}
        currentUserEmail={user?.email || ''}
      />
    </div>
  );
};