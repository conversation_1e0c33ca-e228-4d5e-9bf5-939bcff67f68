import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Share2, Copy, Check, Facebook, MessageCircle, Linkedin, Instagram } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface SocialShareProps {
  title: string;
  description?: string;
  url?: string;
  hashtags?: string[];
  image?: string;
  children?: React.ReactNode;
}

interface SocialPlatform {
  name: string;
  icon: React.ComponentType<any>;
  color: string;
  shareUrl: (params: ShareParams) => string;
}

interface ShareParams {
  url: string;
  title: string;
  description?: string;
  hashtags?: string;
  image?: string;
}

const socialPlatforms: SocialPlatform[] = [
  {
    name: 'Facebook',
    icon: Facebook,
    color: 'bg-blue-600 hover:bg-blue-700',
    shareUrl: ({ url, title, description }) => 
      `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}&quote=${encodeURIComponent(title + (description ? ' - ' + description : ''))}`
  },
  {
    name: 'X (Twitter)',
    icon: () => (
      <svg viewBox="0 0 24 24" className="h-4 w-4 fill-current">
        <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
      </svg>
    ),
    color: 'bg-black hover:bg-gray-800',
    shareUrl: ({ url, title, hashtags }) => 
      `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}${hashtags ? '&hashtags=' + hashtags : ''}`
  },
  {
    name: 'WhatsApp',
    icon: MessageCircle,
    color: 'bg-green-600 hover:bg-green-700',
    shareUrl: ({ url, title, description }) => 
      `https://wa.me/?text=${encodeURIComponent(title + (description ? ' - ' + description : '') + ' ' + url)}`
  },
  {
    name: 'LinkedIn',
    icon: Linkedin,
    color: 'bg-blue-700 hover:bg-blue-800',
    shareUrl: ({ url, title, description }) => 
      `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}&summary=${encodeURIComponent(description || '')}`
  },
  {
    name: 'Instagram',
    icon: Instagram,
    color: 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600',
    shareUrl: ({ title }) => 
      `https://www.instagram.com/` // Instagram doesn't support direct sharing via URL, but we'll show the platform
  }
];

export const SocialShare: React.FC<SocialShareProps> = ({
  title,
  description,
  url = window.location.href,
  hashtags = [],
  image,
  children
}) => {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [copied, setCopied] = useState(false);
  const [customMessage, setCustomMessage] = useState(title);
  const [customHashtags, setCustomHashtags] = useState(hashtags.join(' '));

  const shareParams: ShareParams = {
    url,
    title: customMessage,
    description,
    hashtags: customHashtags.replace(/[#\s]/g, '').split(',').filter(Boolean).join(','),
    image
  };

  const copyToClipboard = async () => {
    try {
      const shareText = `${customMessage}${description ? ' - ' + description : ''}\n\n${url}${customHashtags ? '\n\n' + customHashtags : ''}`;
      await navigator.clipboard.writeText(shareText);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      toast({
        title: "Copied!",
        description: "Link copied to clipboard",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  const handleShare = (platform: SocialPlatform) => {
    if (platform.name === 'Instagram') {
      toast({
        title: "Instagram Sharing",
        description: "Please copy the content and share manually on Instagram",
      });
      return;
    }

    const shareUrl = platform.shareUrl(shareParams);
    window.open(shareUrl, '_blank', 'width=600,height=400');
    
    toast({
      title: "Shared!",
      description: `Opened ${platform.name} sharing window`,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button variant="outline" size="sm">
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Share this content</DialogTitle>
          <DialogDescription>
            Customize your message and share on your favorite platform
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Custom Message */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Custom Message</label>
            <Textarea
              value={customMessage}
              onChange={(e) => setCustomMessage(e.target.value)}
              placeholder="Enter your custom message..."
              className="resize-none"
              rows={3}
            />
          </div>

          {/* Custom Hashtags */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Hashtags</label>
            <Input
              value={customHashtags}
              onChange={(e) => setCustomHashtags(e.target.value)}
              placeholder="#savings #fintech #investment"
            />
            <p className="text-xs text-muted-foreground">
              Separate hashtags with spaces or commas
            </p>
          </div>

          {/* URL Preview */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Link</label>
            <div className="flex space-x-2">
              <Input value={url} readOnly className="flex-1" />
              <Button
                variant="outline"
                size="icon"
                onClick={copyToClipboard}
                className="shrink-0"
              >
                {copied ? (
                  <Check className="h-4 w-4 text-green-600" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {/* Social Platforms */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Share on</label>
            <div className="grid grid-cols-2 gap-3">
              {socialPlatforms.map((platform) => {
                const Icon = platform.icon;
                return (
                  <Button
                    key={platform.name}
                    variant="outline"
                    className={`justify-start space-x-2 text-white border-0 ${platform.color}`}
                    onClick={() => handleShare(platform)}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{platform.name}</span>
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Preview of what will be shared */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Preview</label>
            <div className="p-3 bg-muted rounded-lg space-y-2">
              <p className="text-sm font-medium">{customMessage}</p>
              {description && (
                <p className="text-sm text-muted-foreground">{description}</p>
              )}
              <p className="text-xs text-blue-600 break-all">{url}</p>
              {customHashtags && (
                <div className="flex flex-wrap gap-1">
                  {customHashtags.split(/[\s,]+/).filter(Boolean).map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag.startsWith('#') ? tag : `#${tag}`}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Copy All Button */}
          <Button 
            variant="outline" 
            className="w-full" 
            onClick={copyToClipboard}
          >
            {copied ? (
              <>
                <Check className="h-4 w-4 mr-2 text-green-600" />
                Copied to Clipboard!
              </>
            ) : (
              <>
                <Copy className="h-4 w-4 mr-2" />
                Copy All Content
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};