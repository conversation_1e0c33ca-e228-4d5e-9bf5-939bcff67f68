import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableCaption,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { useToast } from "@/components/ui/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const transactions = [
  {
    id: 1,
    date: "2024-03-15",
    type: "Deposit",
    amount: 50000,
    status: "Completed",
  },
  {
    id: 2,
    date: "2024-03-10",
    type: "Withdrawal",
    amount: 25000,
    status: "Pending",
  },
  {
    id: 3,
    date: "2024-03-05",
    type: "Interest",
    amount: 1200,
    status: "Completed",
  },
  {
    id: 4,
    date: "2024-02-28",
    type: "Deposit",
    amount: 30000,
    status: "Completed",
  },
  {
    id: 5,
    date: "2024-02-20",
    type: "Withdrawal",
    amount: 15000,
    status: "Completed",
  },
];

const columns = [
  {
    accessorKey: "date",
    header: "Date",
  },
  {
    accessorKey: "type",
    header: "Type",
  },
  {
    accessorKey: "amount",
    header: "Amount",
  },
  {
    accessorKey: "status",
    header: "Status",
  },
];

type DateValue = Date | undefined

const Payments = () => {
  const [date, setDate] = useState<DateValue>(new Date())
  const [searchQuery, setSearchQuery] = useState("");
  const [typeFilter, setTypeFilter] = useState("All");
  const [statusFilter, setStatusFilter] = useState("All");
  const { toast } = useToast();

  const filteredTransactions = transactions.filter((transaction) => {
    const matchesSearch =
      transaction.type.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesType = typeFilter === "All" || transaction.type === typeFilter;
    const matchesStatus =
      statusFilter === "All" || transaction.status === statusFilter;

    return matchesSearch && matchesType && matchesStatus;
  });

  return (
    <div className="container max-w-7xl mx-auto space-y-6 py-12">
      <Card className="mobile-card overflow-x-auto">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">Transactions</CardTitle>
          <CardDescription>View and manage your transactions.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full md:w-auto">
                <Label htmlFor="search" className="text-sm font-medium">Search:</Label>
                <Input
                  type="search"
                  id="search"
                  placeholder="Search transactions..."
                  className="w-full sm:w-64 rounded-none border-0 border-b-2 border-gray-300 focus:border-primary"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full md:w-auto">
                <Select
                  value={typeFilter}
                  onValueChange={setTypeFilter}
                >
                  <SelectTrigger className="w-full sm:w-[180px] rounded-none">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Types</SelectItem>
                    <SelectItem value="Deposit">Deposit</SelectItem>
                    <SelectItem value="Withdrawal">Withdrawal</SelectItem>
                    <SelectItem value="Interest">Interest</SelectItem>
                  </SelectContent>
                </Select>
                <Select
                  value={statusFilter}
                  onValueChange={setStatusFilter}
                >
                  <SelectTrigger className="w-full sm:w-[180px] rounded-none">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Statuses</SelectItem>
                    <SelectItem value="Completed">Completed</SelectItem>
                    <SelectItem value="Pending">Pending</SelectItem>
                  </SelectContent>
                </Select>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="date"
                      variant={"secondary"}
                      className={cn(
                        "pl-3 text-left font-normal w-full sm:w-auto rounded-none",
                        !date && "text-muted-foreground"
                      )}
                    >
                      {date ? (
                        format(date, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                      <CalendarIcon className="ml-2 h-4 w-4 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={date}
                      onSelect={setDate}
                      disabled={(date) =>
                        date > new Date() || date < new Date("1900-01-01")
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <div className="border border-gray-200 dark:border-gray-700 overflow-x-auto">
              <Table className="min-w-full">
                <TableHeader>
                  <TableRow className="border-b">
                    {columns.map((column) => (
                      <TableHead key={column.accessorKey} className="px-3 py-4 text-left font-semibold whitespace-nowrap">
                        {column.header}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTransactions.map((transaction) => (
                    <TableRow key={transaction.id} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                      <TableCell className="px-3 py-4 whitespace-nowrap">{transaction.date}</TableCell>
                      <TableCell className="px-3 py-4 whitespace-nowrap">{transaction.type}</TableCell>
                      <TableCell className="px-3 py-4 whitespace-nowrap font-semibold">₦{transaction.amount.toLocaleString()}</TableCell>
                      <TableCell className="px-3 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          transaction.status === 'Completed' 
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        }`}>
                          {transaction.status}
                        </span>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Payments;
