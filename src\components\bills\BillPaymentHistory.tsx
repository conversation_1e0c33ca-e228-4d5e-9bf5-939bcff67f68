import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useToast } from '@/hooks/use-toast';
import { Search, Filter, Download, RefreshCw, Phone, Zap, Tv, Wifi, Plane, GraduationCap } from 'lucide-react';
import axios from 'axios';
import { format } from 'date-fns';

interface BillPayment {
  _id: string;
  reference: string;
  category: string;
  amount: number;
  fee: number;
  totalAmount: number;
  customerIdentifier: string;
  customerName?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  providerId: {
    name: string;
    category: string;
  };
  createdAt: string;
  completedAt?: string;
  failedAt?: string;
  errorMessage?: string;
}

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  processing: 'bg-blue-100 text-blue-800',
  completed: 'bg-green-100 text-green-800',
  failed: 'bg-red-100 text-red-800',
  cancelled: 'bg-gray-100 text-gray-800'
};

const categoryIcons = {
  airtime: Phone,
  data: Wifi,
  electricity: Zap,
  cable_tv: Tv,
  internet: Wifi,
  flight: Plane,
  education: GraduationCap,
  betting: Phone
};

export const BillPaymentHistory = () => {
  const [payments, setPayments] = useState<BillPayment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    fetchPayments();
  }, [page, statusFilter]);

  const fetchPayments = async (reset = false) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: reset ? '1' : page.toString(),
        limit: '20'
      });

      if (statusFilter) params.append('status', statusFilter);

      const response = await axios.get(`/api/v1/bills/payments?${params}`);
      const newPayments = response.data.data;

      if (reset) {
        setPayments(newPayments);
        setPage(1);
      } else {
        setPayments(prev => [...prev, ...newPayments]);
      }

      setHasMore(newPayments.length === 20);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch payment history',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    setPage(1);
    fetchPayments(true);
  };

  const filteredPayments = payments.filter(payment =>
    payment.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
    payment.customerIdentifier.toLowerCase().includes(searchTerm.toLowerCase()) ||
    payment.providerId.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const exportPayments = () => {
    const csvContent = [
      ['Reference', 'Provider', 'Category', 'Amount', 'Fee', 'Total', 'Customer', 'Status', 'Date'].join(','),
      ...filteredPayments.map(payment => [
        payment.reference,
        payment.providerId.name,
        payment.category,
        payment.amount,
        payment.fee,
        payment.totalAmount,
        payment.customerIdentifier,
        payment.status,
        format(new Date(payment.createdAt), 'yyyy-MM-dd HH:mm')
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bill-payments-${format(new Date(), 'yyyy-MM-dd')}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Payment History
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button onClick={handleRefresh} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4" />
            </Button>
            <Button onClick={exportPayments} variant="outline" size="sm">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Filters */}
        <div className="flex gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by reference, customer, or provider..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Statuses</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="processing">Processing</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="failed">Failed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Payments Table */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Service</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Reference</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredPayments.map((payment) => {
                const Icon = categoryIcons[payment.category as keyof typeof categoryIcons];
                return (
                  <TableRow key={payment._id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        <div>
                          <div className="font-medium">{payment.providerId.name}</div>
                          <div className="text-sm text-muted-foreground capitalize">
                            {payment.category.replace('_', ' ')}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{payment.customerIdentifier}</div>
                        {payment.customerName && (
                          <div className="text-sm text-muted-foreground">{payment.customerName}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">₦{payment.totalAmount.toLocaleString()}</div>
                        <div className="text-sm text-muted-foreground">
                          Amount: ₦{payment.amount.toLocaleString()} + Fee: ₦{payment.fee.toLocaleString()}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={statusColors[payment.status]}>
                        {payment.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {format(new Date(payment.createdAt), 'MMM dd, yyyy')}
                        <div className="text-muted-foreground">
                          {format(new Date(payment.createdAt), 'HH:mm')}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <code className="text-xs bg-muted px-2 py-1 rounded">
                        {payment.reference}
                      </code>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>

        {/* Load More */}
        {hasMore && !loading && (
          <div className="text-center">
            <Button onClick={() => setPage(p => p + 1)} variant="outline">
              Load More
            </Button>
          </div>
        )}

        {filteredPayments.length === 0 && !loading && (
          <div className="text-center py-8 text-muted-foreground">
            No payments found
          </div>
        )}
      </CardContent>
    </Card>
  );
};