const validator = require('validator');

// Registration validation
const validateRegistration = (req, res, next) => {
  const { firstName, lastName, email, phone, password, pin } = req.body;
  const errors = [];

  // Validate required fields
  if (!firstName || firstName.trim().length < 2) {
    errors.push('First name must be at least 2 characters long');
  }

  if (!lastName || lastName.trim().length < 2) {
    errors.push('Last name must be at least 2 characters long');
  }

  if (!email || !validator.isEmail(email)) {
    errors.push('Valid email is required');
  }

  if (!phone || !validator.isMobilePhone(phone, 'en-NG')) {
    errors.push('Valid Nigerian phone number is required');
  }

  if (!password || password.length < 6) {
    errors.push('Password must be at least 6 characters long');
  }

  if (!pin || !/^\d{4}$/.test(pin)) {
    errors.push('PIN must be exactly 4 digits');
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors
    });
  }

  next();
};

// Login validation
const validateLogin = (req, res, next) => {
  const { email, password } = req.body;
  const errors = [];

  if (!email || !validator.isEmail(email)) {
    errors.push('Valid email is required');
  }

  if (!password) {
    errors.push('Password is required');
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors
    });
  }

  next();
};

// Savings plan validation
const validateSavingsPlan = (req, res, next) => {
  const { name, targetAmount, duration, frequency } = req.body;
  const errors = [];

  if (!name || name.trim().length < 3) {
    errors.push('Plan name must be at least 3 characters long');
  }

  if (!targetAmount || targetAmount < 1000) {
    errors.push('Target amount must be at least ₦1,000');
  }

  if (!duration || duration < 1) {
    errors.push('Duration must be at least 1 day');
  }

  if (!frequency || !['daily', 'weekly', 'monthly'].includes(frequency)) {
    errors.push('Frequency must be daily, weekly, or monthly');
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors
    });
  }

  next();
};

// Group savings validation
const validateGroupSavings = (req, res, next) => {
  const { name, description, targetAmount, contributionAmount, maxMembers } = req.body;
  const errors = [];

  if (!name || name.trim().length < 3) {
    errors.push('Group name must be at least 3 characters long');
  }

  if (!description || description.trim().length < 10) {
    errors.push('Description must be at least 10 characters long');
  }

  if (!targetAmount || targetAmount < 10000) {
    errors.push('Target amount must be at least ₦10,000');
  }

  if (!contributionAmount || contributionAmount < 1000) {
    errors.push('Contribution amount must be at least ₦1,000');
  }

  if (!maxMembers || maxMembers < 2 || maxMembers > 50) {
    errors.push('Maximum members must be between 2 and 50');
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors
    });
  }

  next();
};

// Bill payment validation
const validateBillPayment = (req, res, next) => {
  const { billType, provider, amount, beneficiary } = req.body;
  const errors = [];

  if (!billType || !['airtime', 'data', 'electricity', 'cable_tv', 'internet'].includes(billType)) {
    errors.push('Valid bill type is required');
  }

  if (!provider || provider.trim().length < 2) {
    errors.push('Provider is required');
  }

  if (!amount || amount < 50) {
    errors.push('Amount must be at least ₦50');
  }

  if (!beneficiary || beneficiary.trim().length < 3) {
    errors.push('Beneficiary information is required');
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors
    });
  }

  next();
};

// KYC validation
const validateKyc = (req, res, next) => {
  const { documentType, documentNumber, dateOfBirth, address } = req.body;
  const errors = [];

  if (!documentType || !['nin', 'bvn', 'passport', 'drivers_license'].includes(documentType)) {
    errors.push('Valid document type is required');
  }

  if (!documentNumber || documentNumber.trim().length < 5) {
    errors.push('Document number is required');
  }

  if (!dateOfBirth || !validator.isDate(dateOfBirth)) {
    errors.push('Valid date of birth is required');
  }

  if (!address || address.trim().length < 10) {
    errors.push('Address must be at least 10 characters long');
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors
    });
  }

  next();
};

// Withdrawal validation
const validateWithdrawal = (req, res, next) => {
  const { amount, bankCode, accountNumber } = req.body;
  const errors = [];

  if (!amount || amount < 100) {
    errors.push('Minimum withdrawal amount is ₦100');
  }

  if (!bankCode || bankCode.trim().length !== 3) {
    errors.push('Valid bank code is required');
  }

  if (!accountNumber || !/^\d{10}$/.test(accountNumber)) {
    errors.push('Account number must be exactly 10 digits');
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors
    });
  }

  next();
};

// Fixed Deposit validation
const validateFixedDeposit = (req, res, next) => {
  const { amount, duration } = req.body;
  const errors = [];

  // Validate amount
  if (!amount || typeof amount !== 'number') {
    errors.push('Amount is required and must be a number');
  } else if (amount < 10000) {
    errors.push('Minimum fixed deposit amount is ₦10,000');
  } else if (amount > ********) {
    errors.push('Maximum fixed deposit amount is ₦10,000,000');
  }

  // Validate duration
  if (!duration || typeof duration !== 'number') {
    errors.push('Duration is required and must be a number');
  } else if (duration < 30) {
    errors.push('Minimum duration is 30 days');
  } else if (duration > 365) {
    errors.push('Maximum duration is 365 days');
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors
    });
  }

  next();
};

module.exports = {
  validateRegistration,
  validateLogin,
  validateSavingsPlan,
  validateGroupSavings,
  validateBillPayment,
  validateKyc,
  validateWithdrawal,
  validateFixedDeposit
};
