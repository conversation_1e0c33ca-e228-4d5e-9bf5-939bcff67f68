import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuth } from '@/hooks/use-auth';
import { 
  MessageCircle, 
  Phone, 
  Mail, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  Search,
  FileText,
  HelpCircle,
  Headphones,
  Users,
  Star,
  Send,
  ExternalLink,
  MessageSquare,
  Zap,
  Globe
} from 'lucide-react';
import { toast } from 'sonner';

const HelpSupport = () => {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTopic, setSelectedTopic] = useState('');
  const [chatOpen, setChatOpen] = useState(false);
  const [ticketOpen, setTicketOpen] = useState(false);
  const [chatWidget, setChatWidget] = useState<'intercom' | 'tidio' | 'jivo' | null>(null);
  const [liveChatActive, setLiveChatActive] = useState(false);

  // Initialize multiple chat widgets
  useEffect(() => {
    // Intercom Integration
    const initIntercom = () => {
      const intercomAppId = 'vhpq8e5j'; // Demo app ID - replace with your actual ID
      (window as any).intercomSettings = {
        app_id: intercomAppId,
        name: `${user?.profile?.first_name || ''} ${user?.profile?.last_name || ''}`.trim() || "Better Interest User",
        email: user?.email || "<EMAIL>",
        user_id: user?.id,
        created_at: Math.floor(Date.now() / 1000),
        custom_launcher_selector: '#intercom-launcher'
      };

      const script = document.createElement('script');
      script.async = true;
      script.src = `https://widget.intercom.io/widget/${intercomAppId}`;
      document.head.appendChild(script);
    };

    // Tidio Integration
    const initTidio = () => {
      const tidioKey = 'your_tidio_key'; // Replace with your Tidio key
      (window as any).tidioChatApi = (window as any).tidioChatApi || {};
      const script = document.createElement('script');
      script.async = true;
      script.src = `//code.tidio.co/${tidioKey}.js`;
      document.head.appendChild(script);
    };

    // JivoChat Integration
    const initJivo = () => {
      const jivoId = 'your_jivo_id'; // Replace with your Jivo ID
      const script = document.createElement('script');
      script.async = true;
      script.src = `//code.jivosite.com/widget/${jivoId}`;
      document.head.appendChild(script);
    };

    // Initialize based on preference or default to Intercom
    if (chatWidget === 'intercom' || !chatWidget) {
      initIntercom();
    } else if (chatWidget === 'tidio') {
      initTidio();
    } else if (chatWidget === 'jivo') {
      initJivo();
    }

    return () => {
      // Cleanup scripts if needed
    };
  }, [user, chatWidget]);

  const faqData = [
    {
      category: "Account & Security",
      questions: [
        {
          q: "How do I reset my password?",
          a: "Go to Settings > Security > Change Password. You'll need to verify your current password and enter a new one."
        },
        {
          q: "How does KYC verification work?",
          a: "Our KYC process uses Dojah verification. Click the KYC Verification link in your sidebar and follow the step-by-step process."
        },
        {
          q: "Is my financial data secure?",
          a: "Yes, we use bank-level encryption and comply with international security standards to protect your data."
        }
      ]
    },
    {
      category: "Savings Plans",
      questions: [
        {
          q: "What types of savings plans are available?",
          a: "We offer Fixed Deposits, Flexible Savings, Safe Lock, and Group Savings plans with competitive interest rates."
        },
        {
          q: "How are interest rates calculated?",
          a: "Interest rates vary by plan type and duration. Use our Interest Calculator on the dashboard for precise calculations."
        },
        {
          q: "Can I withdraw before maturity?",
          a: "This depends on your plan type. Fixed Deposits and Safe Lock have penalties, while Flexible Savings allow anytime withdrawal."
        }
      ]
    },
    {
      category: "Payments & Withdrawals",
      questions: [
        {
          q: "How do I add a payment method?",
          a: "Go to Payments > Payment Methods > Add New. We support debit cards and bank transfers via Paystack."
        },
        {
          q: "How long do withdrawals take?",
          a: "Withdrawals typically process within 1-3 business days depending on your bank and withdrawal method."
        },
        {
          q: "Are there withdrawal fees?",
          a: "Standard withdrawals are free. Express withdrawals may have small fees depending on your plan."
        }
      ]
    },
    {
      category: "Group Savings",
      questions: [
        {
          q: "How do group savings work?",
          a: "Create or join groups with friends/family. Set contribution schedules and achieve savings goals together with accountability."
        },
        {
          q: "What happens if a group member defaults?",
          a: "Group members are responsible for their contributions. Default policies are outlined in our Group Savings Terms."
        },
        {
          q: "Can I leave a group early?",
          a: "Yes, but terms depend on the group agreement. Some groups may have early exit penalties."
        }
      ]
    }
  ];

  const contactMethods = [
    {
      icon: <MessageCircle className="h-6 w-6" />,
      title: "Live Chat",
      description: "Get instant help from our support team",
      availability: "24/7",
      action: () => {
        if ((window as any).Intercom) {
          (window as any).Intercom('show');
          setLiveChatActive(true);
        } else if ((window as any).tidioChatApi) {
          (window as any).tidioChatApi.open();
          setLiveChatActive(true);
        } else {
          setChatOpen(true);
        }
      },
      primary: true
    },
    {
      icon: <Phone className="h-6 w-6" />,
      title: "Phone Support",
      description: "+234 800 BETTER (238837)",
      availability: "Mon-Fri 8AM-8PM",
      action: () => window.open('tel:+*************')
    },
    {
      icon: <Mail className="h-6 w-6" />,
      title: "Email Support",
      description: "<EMAIL>",
      availability: "Response within 24hrs",
      action: () => window.open('mailto:<EMAIL>')
    },
    {
      icon: <MessageSquare className="h-6 w-6" />,
      title: "WhatsApp Support",
      description: "Chat on WhatsApp",
      availability: "Mon-Fri 9AM-6PM",
      action: () => window.open('https://wa.me/*************?text=Hello%20Better%20Interest%20Support')
    },
    {
      icon: <FileText className="h-6 w-6" />,
      title: "Submit Ticket",
      description: "Detailed issue reporting",
      availability: "Track your request",
      action: () => setTicketOpen(true)
    }
  ];

  const chatProviders = [
    {
      id: 'intercom',
      name: 'Intercom',
      icon: <MessageCircle className="h-5 w-5" />,
      description: 'Professional customer messaging',
      features: ['Smart routing', 'File sharing', 'Screen sharing']
    },
    {
      id: 'tidio',
      name: 'Tidio',
      icon: <Zap className="h-5 w-5" />,
      description: 'Live chat & chatbots',
      features: ['Chatbots', 'Visitor tracking', 'Mobile apps']
    },
    {
      id: 'jivo',
      name: 'JivoChat',
      icon: <Globe className="h-5 w-5" />,
      description: 'Omnichannel communication',
      features: ['Social media', 'Phone calls', 'Email integration']
    }
  ];

  const filteredFAQ = faqData.map(category => ({
    ...category,
    questions: category.questions.filter(item =>
      searchQuery === '' ||
      item.q.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.a.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(category => category.questions.length > 0);

  const handleTicketSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    toast.success("Support ticket submitted! We'll respond within 24 hours.");
    setTicketOpen(false);
  };

  return (
    <div className="space-y-6 max-w-6xl mx-auto">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-3">
          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
            <Headphones className="h-6 w-6 text-primary" />
          </div>
          <h1 className="text-3xl font-bold">Help & Support</h1>
        </div>
        <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
          We're here to help you succeed with your financial goals. Get instant support or browse our knowledge base.
        </p>
      </div>

      {/* Chat Provider Selection */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Choose Your Preferred Chat Experience
          </CardTitle>
          <CardDescription>
            Select the chat provider that best suits your needs
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            {chatProviders.map((provider) => (
              <Card 
                key={provider.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  chatWidget === provider.id ? 'ring-2 ring-primary bg-primary/5' : ''
                }`}
                onClick={() => setChatWidget(provider.id as any)}
              >
                <CardContent className="p-4 space-y-3">
                  <div className="flex items-center gap-2">
                    {provider.icon}
                    <h4 className="font-medium">{provider.name}</h4>
                  </div>
                  <p className="text-sm text-muted-foreground">{provider.description}</p>
                  <div className="space-y-1">
                    {provider.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center gap-1 text-xs text-muted-foreground">
                        <CheckCircle className="h-3 w-3 text-primary" />
                        {feature}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5 mb-6">
        {contactMethods.map((method, index) => (
          <Card 
            key={index} 
            className={`cursor-pointer transition-all hover:shadow-lg hover:-translate-y-1 mobile-card ${
              method.primary ? 'ring-2 ring-primary/20 bg-primary/5' : ''
            }`}
            onClick={method.action}
          >
            <CardContent className="p-3 sm:p-4 md:p-6 text-center space-y-2 sm:space-y-3">
              <div className={`w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full mx-auto flex items-center justify-center ${
                method.primary ? 'bg-primary text-primary-foreground' : 'bg-muted'
              }`}>
                {method.icon}
              </div>
              <div className="space-y-1">
                <h3 className="font-semibold text-xs sm:text-sm md:text-base">{method.title}</h3>
                <p className="text-xs sm:text-sm text-muted-foreground line-clamp-2">{method.description}</p>
                <Badge variant="secondary" className="mt-1 text-xs">
                  <Clock className="h-3 w-3 mr-1" />
                  {method.availability}
                </Badge>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content */}
      <Tabs defaultValue="faq" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="faq">FAQ</TabsTrigger>
          <TabsTrigger value="guides">Guides</TabsTrigger>
          <TabsTrigger value="status">System Status</TabsTrigger>
        </TabsList>

        <TabsContent value="faq" className="space-y-6">
          {/* Search */}
          <Card>
            <CardContent className="p-6">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search frequently asked questions..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </CardContent>
          </Card>

          {/* FAQ Sections */}
          <div className="grid gap-6">
            {filteredFAQ.map((category, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <HelpCircle className="h-5 w-5" />
                    {category.category}
                  </CardTitle>
                  <CardDescription>
                    {category.questions.length} questions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Accordion type="single" collapsible className="w-full">
                    {category.questions.map((item, qIndex) => (
                      <AccordionItem key={qIndex} value={`item-${index}-${qIndex}`}>
                        <AccordionTrigger className="text-left">
                          {item.q}
                        </AccordionTrigger>
                        <AccordionContent className="text-muted-foreground">
                          {item.a}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="guides" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {[
              {
                title: "Getting Started Guide",
                description: "Complete setup and verification process",
                steps: 8,
                duration: "10 min read"
              },
              {
                title: "Savings Plans Overview",
                description: "Compare all available savings options",
                steps: 5,
                duration: "5 min read"
              },
              {
                title: "Group Savings Setup",
                description: "Create and manage savings groups",
                steps: 6,
                duration: "7 min read"
              },
              {
                title: "Security Best Practices",
                description: "Keep your account safe and secure",
                steps: 4,
                duration: "3 min read"
              }
            ].map((guide, index) => (
              <Card key={index} className="cursor-pointer hover:shadow-lg transition-all">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    {guide.title}
                    <Badge>{guide.duration}</Badge>
                  </CardTitle>
                  <CardDescription>{guide.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <FileText className="h-4 w-4" />
                    {guide.steps} steps
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="status" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                System Status - All Systems Operational
              </CardTitle>
              <CardDescription>
                Last updated: {new Date().toLocaleString()}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {[
                { service: "Payment Processing", status: "operational" },
                { service: "User Authentication", status: "operational" },
                { service: "KYC Verification", status: "operational" },
                { service: "Interest Calculations", status: "operational" },
                { service: "Withdrawal Processing", status: "operational" }
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                  <span className="font-medium">{item.service}</span>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-green-600 capitalize">{item.status}</span>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Chat Dialog Fallback */}
      <Dialog open={chatOpen} onOpenChange={setChatOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Live Chat</DialogTitle>
            <DialogDescription>
              Chat widget is loading. Please try refreshing the page or contact us directly.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Button onClick={() => window.open('mailto:<EMAIL>')} className="w-full">
              <Mail className="h-4 w-4 mr-2" />
              Email Support Instead
            </Button>
            <Button variant="outline" onClick={() => setChatOpen(false)} className="w-full">
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Support Ticket Dialog */}
      <Dialog open={ticketOpen} onOpenChange={setTicketOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Submit Support Ticket</DialogTitle>
            <DialogDescription>
              Provide details about your issue and we'll get back to you within 24 hours.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleTicketSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Priority</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Category</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="account">Account Issues</SelectItem>
                    <SelectItem value="payment">Payment Problems</SelectItem>
                    <SelectItem value="technical">Technical Support</SelectItem>
                    <SelectItem value="feature">Feature Request</SelectItem>
                    <SelectItem value="kyc">KYC Verification</SelectItem>
                    <SelectItem value="withdrawal">Withdrawal Issues</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Subject</label>
              <Input placeholder="Brief description of your issue" required />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Description</label>
              <Textarea 
                placeholder="Please provide detailed information about your issue, including steps to reproduce and any error messages..."
                className="min-h-32"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Attach Files (Optional)</label>
              <Input type="file" multiple accept=".jpg,.jpeg,.png,.pdf,.doc,.docx" />
              <p className="text-xs text-muted-foreground">
                You can attach screenshots, documents, or other relevant files
              </p>
            </div>
            <div className="flex gap-2">
              <Button type="submit" className="flex-1">
                <Send className="h-4 w-4 mr-2" />
                Submit Ticket
              </Button>
              <Button type="button" variant="outline" onClick={() => setTicketOpen(false)}>
                Cancel
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default HelpSupport;