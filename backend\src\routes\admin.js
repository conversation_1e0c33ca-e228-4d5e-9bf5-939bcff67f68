const express = require('express');
const router = express.Router();
const { auth, admin } = require('../middleware/auth');
const User = require('../models/User');
const SavingsPlan = require('../models/SavingsPlan');
const GroupSavingsPlan = require('../models/GroupSavingsPlan');
const GroupContribution = require('../models/GroupContribution');
const Kyc = require('../models/Kyc');
const Withdrawal = require('../models/Withdrawal');
const BillPayment = require('../models/BillPayment');
const Investment = require('../models/Investment');

// Admin dashboard statistics
router.get('/dashboard', [auth, admin], async (req, res) => {
  try {
    const { period = '30' } = req.query; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    // User statistics
    const totalUsers = await User.countDocuments();
    const newUsers = await User.countDocuments({
      createdAt: { $gte: startDate }
    });
    const activeUsers = await User.countDocuments({
      isVerified: true,
      isActive: { $ne: false }
    });

    // Savings statistics
    const totalSavingsPlans = await SavingsPlan.countDocuments();
    const activeSavingsPlans = await SavingsPlan.countDocuments({ status: 'active' });
    const totalSavingsAmount = await SavingsPlan.aggregate([
      { $group: { _id: null, total: { $sum: '$currentAmount' } } }
    ]);

    // Transaction statistics
    const recentTransactions = await SavingsPlan.aggregate([
      { $unwind: '$transactions' },
      { $match: { 'transactions.date': { $gte: startDate } } },
      {
        $group: {
          _id: '$transactions.type',
          count: { $sum: 1 },
          totalAmount: { $sum: '$transactions.amount' }
        }
      }
    ]);

    // KYC statistics
    const kycStats = await Kyc.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Withdrawal statistics
    const withdrawalStats = await Withdrawal.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);

    // Bill payment statistics
    const billPaymentStats = await BillPayment.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);

    // Investment statistics
    const investmentStats = await Investment.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          totalValue: { $sum: '$currentValue' }
        }
      }
    ]);

    const dashboard = {
      users: {
        total: totalUsers,
        new: newUsers,
        active: activeUsers,
        growthRate: totalUsers > 0 ? ((newUsers / totalUsers) * 100).toFixed(2) : 0
      },
      savings: {
        totalPlans: totalSavingsPlans,
        activePlans: activeSavingsPlans,
        totalAmount: totalSavingsAmount[0]?.total || 0,
        averagePerPlan: totalSavingsPlans > 0 ? 
          ((totalSavingsAmount[0]?.total || 0) / totalSavingsPlans).toFixed(2) : 0
      },
      transactions: recentTransactions.reduce((acc, txn) => {
        acc[txn._id] = {
          count: txn.count,
          totalAmount: txn.totalAmount
        };
        return acc;
      }, {}),
      kyc: kycStats.reduce((acc, stat) => {
        acc[stat._id] = stat.count;
        return acc;
      }, {}),
      withdrawals: withdrawalStats.reduce((acc, stat) => {
        acc[stat._id] = {
          count: stat.count,
          totalAmount: stat.totalAmount
        };
        return acc;
      }, {}),
      billPayments: billPaymentStats.reduce((acc, stat) => {
        acc[stat._id] = {
          count: stat.count,
          totalAmount: stat.totalAmount
        };
        return acc;
      }, {}),
      investments: investmentStats.reduce((acc, stat) => {
        acc[stat._id] = {
          count: stat.count,
          totalAmount: stat.totalAmount,
          totalValue: stat.totalValue
        };
        return acc;
      }, {})
    };

    res.json({
      success: true,
      data: dashboard
    });
  } catch (error) {
    console.error('Error fetching admin dashboard:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard data'
    });
  }
});

// Get all users with pagination and filters
router.get('/users', [auth, admin], async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      search, 
      status, 
      kycStatus, 
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const filter = {};
    
    // Search filter
    if (search) {
      filter.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } }
      ];
    }

    // Status filter
    if (status) {
      if (status === 'active') {
        filter.isActive = { $ne: false };
      } else if (status === 'inactive') {
        filter.isActive = false;
      }
    }

    // Sort options
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const users = await User.find(filter)
      .select('-password -pin')
      .sort(sortOptions)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('kycStatus');

    const total = await User.countDocuments(filter);

    // Get KYC status for each user
    const usersWithKyc = await Promise.all(
      users.map(async (user) => {
        const kyc = await Kyc.findOne({ userId: user._id });
        return {
          ...user.toObject(),
          kycStatus: kyc?.status || 'pending',
          kycLevel: kyc?.level || 'tier1'
        };
      })
    );

    res.json({
      success: true,
      data: usersWithKyc,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users'
    });
  }
});

// Get specific user details
router.get('/users/:userId', [auth, admin], async (req, res) => {
  try {
    const user = await User.findById(req.params.userId).select('-password -pin');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get user's savings plans
    const savingsPlans = await SavingsPlan.find({ userId: req.params.userId });
    
    // Get user's KYC information
    const kyc = await Kyc.findOne({ userId: req.params.userId });
    
    // Get user's withdrawals
    const withdrawals = await Withdrawal.find({ userId: req.params.userId })
      .sort({ createdAt: -1 })
      .limit(10);
    
    // Get user's investments
    const investments = await Investment.find({ userId: req.params.userId })
      .populate('productId', 'name type');

    const userDetails = {
      user,
      kyc,
      savingsPlans,
      withdrawals,
      investments,
      summary: {
        totalSavings: savingsPlans.reduce((sum, plan) => sum + plan.currentAmount, 0),
        activePlans: savingsPlans.filter(plan => plan.status === 'active').length,
        totalInvestments: investments.reduce((sum, inv) => sum + inv.currentValue, 0),
        pendingWithdrawals: withdrawals.filter(w => w.status === 'pending').length
      }
    };

    res.json({
      success: true,
      data: userDetails
    });
  } catch (error) {
    console.error('Error fetching user details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user details'
    });
  }
});

// Update user status (activate/deactivate)
router.patch('/users/:userId/status', [auth, admin], async (req, res) => {
  try {
    const { isActive, reason } = req.body;

    const user = await User.findByIdAndUpdate(
      req.params.userId,
      { 
        isActive,
        ...(reason && { statusChangeReason: reason }),
        statusChangedAt: new Date(),
        statusChangedBy: req.user.id
      },
      { new: true }
    ).select('-password -pin');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: user,
      message: `User ${isActive ? 'activated' : 'deactivated'} successfully`
    });
  } catch (error) {
    console.error('Error updating user status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user status'
    });
  }
});

// Get KYC submissions for review
router.get('/kyc/pending', [auth, admin], async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;

    const kycSubmissions = await Kyc.find({
      status: { $in: ['pending', 'under_review'] }
    })
    .populate('userId', 'firstName lastName email phone')
    .sort({ submittedAt: 1 })
    .limit(limit * 1)
    .skip((page - 1) * limit);

    const total = await Kyc.countDocuments({
      status: { $in: ['pending', 'under_review'] }
    });

    res.json({
      success: true,
      data: kycSubmissions,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching pending KYC:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch pending KYC submissions'
    });
  }
});

// Review KYC submission
router.post('/kyc/:kycId/review', [auth, admin], async (req, res) => {
  try {
    const { status, comments, level } = req.body;

    if (!['approved', 'rejected', 'under_review'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status'
      });
    }

    const kyc = await Kyc.findById(req.params.kycId);
    
    if (!kyc) {
      return res.status(404).json({
        success: false,
        message: 'KYC submission not found'
      });
    }

    await kyc.addReview(req.user.id, status, comments);

    // Update KYC level if provided
    if (level && ['tier1', 'tier2', 'tier3'].includes(level)) {
      kyc.level = level;
      await kyc.save();
    }

    // Update user KYC status
    await User.findByIdAndUpdate(kyc.userId, {
      kycStatus: status
    });

    res.json({
      success: true,
      data: kyc,
      message: `KYC ${status} successfully`
    });
  } catch (error) {
    console.error('Error reviewing KYC:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to review KYC submission'
    });
  }
});

// Get withdrawal requests for approval
router.get('/withdrawals/pending', [auth, admin], async (req, res) => {
  try {
    const { page = 1, limit = 20, priority } = req.query;

    const filter = { status: 'pending' };
    if (priority) filter.priority = priority;

    const withdrawals = await Withdrawal.find(filter)
      .populate('userId', 'firstName lastName email')
      .populate('savingsPlanId', 'name planType')
      .sort({ priority: -1, createdAt: 1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Withdrawal.countDocuments(filter);

    res.json({
      success: true,
      data: withdrawals,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching pending withdrawals:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch pending withdrawals'
    });
  }
});

// Approve/Reject withdrawal
router.post('/withdrawals/:withdrawalId/review', [auth, admin], async (req, res) => {
  try {
    const { action, comments, scheduledDate } = req.body;

    if (!['approved', 'rejected'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid action. Must be "approved" or "rejected"'
      });
    }

    const withdrawal = await Withdrawal.findById(req.params.withdrawalId);
    
    if (!withdrawal) {
      return res.status(404).json({
        success: false,
        message: 'Withdrawal request not found'
      });
    }

    await withdrawal.addApproval(req.user.id, 'admin', action, comments);

    // Set scheduled date if provided and approved
    if (action === 'approved' && scheduledDate) {
      withdrawal.scheduledDate = new Date(scheduledDate);
      await withdrawal.save();
    }

    res.json({
      success: true,
      data: withdrawal,
      message: `Withdrawal ${action} successfully`
    });
  } catch (error) {
    console.error('Error reviewing withdrawal:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to review withdrawal'
    });
  }
});

// System settings management
router.get('/settings', [auth, admin], async (req, res) => {
  try {
    // In a real application, you'd store these in a separate Settings model
    const settings = {
      interestRates: {
        flex: 8.5,
        fixed: 12.0,
        safelock: 15.0,
        target: 10.0
      },
      fees: {
        withdrawalFee: 100,
        transferFee: 50,
        penaltyRate: 5.0
      },
      limits: {
        dailyWithdrawal: 1000000,
        monthlyWithdrawal: 5000000,
        minimumSavings: 1000
      },
      features: {
        roundUpSavings: true,
        socialFeatures: true,
        billPayments: true,
        investments: true
      }
    };

    res.json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('Error fetching settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch settings'
    });
  }
});

// Update system settings
router.put('/settings', [auth, admin], async (req, res) => {
  try {
    const { category, settings } = req.body;

    // In a real application, you'd update a Settings model
    // For now, we'll just return success
    
    res.json({
      success: true,
      message: `${category} settings updated successfully`
    });
  } catch (error) {
    console.error('Error updating settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update settings'
    });
  }
});

// Generate admin reports
router.get('/reports/:type', [auth, admin], async (req, res) => {
  try {
    const { type } = req.params;
    const { startDate, endDate, format = 'json' } = req.query;

    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();

    let reportData = {};

    switch (type) {
      case 'users':
        reportData = await User.aggregate([
          { $match: { createdAt: { $gte: start, $lte: end } } },
          {
            $group: {
              _id: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
              count: { $sum: 1 }
            }
          },
          { $sort: { _id: 1 } }
        ]);
        break;

      case 'savings':
        reportData = await SavingsPlan.aggregate([
          { $match: { createdAt: { $gte: start, $lte: end } } },
          {
            $group: {
              _id: "$planType",
              count: { $sum: 1 },
              totalAmount: { $sum: "$currentAmount" },
              avgAmount: { $avg: "$currentAmount" }
            }
          }
        ]);
        break;

      case 'transactions':
        reportData = await SavingsPlan.aggregate([
          { $unwind: "$transactions" },
          { $match: { "transactions.date": { $gte: start, $lte: end } } },
          {
            $group: {
              _id: "$transactions.type",
              count: { $sum: 1 },
              totalAmount: { $sum: "$transactions.amount" }
            }
          }
        ]);
        break;

      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid report type'
        });
    }

    res.json({
      success: true,
      data: reportData,
      meta: {
        type,
        startDate: start,
        endDate: end,
        generatedAt: new Date()
      }
    });
  } catch (error) {
    console.error('Error generating report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate report'
    });
  }
});

// Group savings management
router.get('/group-savings', [auth, admin], async (req, res) => {
  try {
    const { status, page = 1, limit = 20 } = req.query;

    const query = {};
    if (status) query.status = status;

    const skip = (page - 1) * limit;

    const [groups, total] = await Promise.all([
      GroupSavingsPlan.find(query)
        .populate('adminId', 'firstName lastName email')
        .populate('members.userId', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit)),
      GroupSavingsPlan.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: groups,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching group savings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch group savings plans'
    });
  }
});

// Get group savings members
router.get('/group-savings/:id/members', [auth, admin], async (req, res) => {
  try {
    const { id } = req.params;

    const group = await GroupSavingsPlan.findById(id)
      .populate('members.userId', 'firstName lastName email phone kycStatus')
      .populate('adminId', 'firstName lastName email');

    if (!group) {
      return res.status(404).json({
        success: false,
        message: 'Group savings plan not found'
      });
    }

    // Get contribution details for each member
    const membersWithContributions = await Promise.all(
      group.members.map(async (member) => {
        const contributions = await GroupContribution.find({
          groupId: id,
          userId: member.userId._id,
          status: 'completed'
        }).sort({ contributionDate: -1 });

        const totalContributed = contributions.reduce((sum, contrib) => sum + contrib.amount, 0);

        return {
          id: member.userId._id,
          user_id: member.userId._id,
          name: `${member.userId.firstName} ${member.userId.lastName}`,
          email: member.userId.email,
          phone: member.userId.phone,
          contribution_amount: member.contributionAmount,
          total_contributed: totalContributed,
          joined_at: member.joinedAt,
          role: member.role,
          last_contribution: contributions[0]?.contributionDate || null,
          contribution_count: contributions.length
        };
      })
    );

    res.json({
      success: true,
      data: membersWithContributions
    });
  } catch (error) {
    console.error('Error fetching group members:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch group members'
    });
  }
});

// Create user endpoint for admin
router.post('/users', [auth, admin], async (req, res) => {
  try {
    const { email, password, firstName, lastName, phone, isAdmin } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this email already exists'
      });
    }

    // Create new user
    const user = new User({
      email,
      password,
      firstName,
      lastName,
      phone,
      role: isAdmin ? 'admin' : 'user',
      isVerified: true, // Admin-created users are auto-verified
      emailVerified: true
    });

    await user.save();

    // Remove password from response
    const userResponse = user.toObject();
    delete userResponse.password;

    res.status(201).json({
      success: true,
      message: 'User created successfully',
      data: userResponse
    });
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create user',
      error: error.message
    });
  }
});

// Verify user phone (admin action)
router.post('/users/:id/verify-phone', [auth, admin], async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    user.phoneVerified = true;
    await user.save();

    res.json({
      success: true,
      message: 'User phone verified successfully'
    });
  } catch (error) {
    console.error('Error verifying user phone:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify user phone'
    });
  }
});

module.exports = router;
