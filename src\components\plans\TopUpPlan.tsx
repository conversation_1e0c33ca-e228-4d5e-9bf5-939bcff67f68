import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { FintechCard } from '@/components/ui/fintech-card';
import { FloatingLabelInput } from '@/components/ui/floating-label-input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Target, Calculator, Calendar, TrendingUp } from 'lucide-react';
import { PaystackIntegration } from '@/components/payments/PaystackIntegration';

const TopUpPlan = () => {
  const [targetAmount, setTargetAmount] = useState('');
  const [timeline, setTimeline] = useState('');
  const [timelineUnit, setTimelineUnit] = useState('months');
  const [currentBalance, setCurrentBalance] = useState(0);
  const [calculatedData, setCalculatedData] = useState<{
    requiredAmount: number;
    dailyAmount: number;
    weeklyAmount: number;
    monthlyAmount: number;
    frequency: string;
    recommendedAmount: number;
  } | null>(null);
  const [showPayment, setShowPayment] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (targetAmount && timeline) {
      calculateTopUpPlan();
    }
  }, [targetAmount, timeline, timelineUnit, currentBalance]);

  const calculateTopUpPlan = () => {
    const target = parseFloat(targetAmount);
    const time = parseFloat(timeline);
    
    if (!target || !time || target <= currentBalance) return;

    const requiredAmount = target - currentBalance;
    const timeInDays = timelineUnit === 'months' ? time * 30 : 
                      timelineUnit === 'weeks' ? time * 7 : time;

    const dailyAmount = requiredAmount / timeInDays;
    const weeklyAmount = dailyAmount * 7;
    const monthlyAmount = dailyAmount * 30;

    // Determine best frequency based on daily amount
    let frequency = 'daily';
    let recommendedAmount = dailyAmount;

    if (dailyAmount < 100) {
      frequency = 'weekly';
      recommendedAmount = weeklyAmount;
    } else if (dailyAmount > 1000) {
      frequency = 'monthly';
      recommendedAmount = monthlyAmount;
    }

    setCalculatedData({
      requiredAmount,
      dailyAmount,
      weeklyAmount,
      monthlyAmount,
      frequency,
      recommendedAmount
    });
  };

  const handleCreatePlan = () => {
    if (!calculatedData) {
      toast({
        title: "Error",
        description: "Please enter valid target amount and timeline",
        variant: "destructive",
      });
      return;
    }

    setShowPayment(true);
  };

  const handlePaymentSuccess = (amount: number) => {
    setCurrentBalance(prev => prev + amount);
    toast({
      title: "Top-Up Successful!",
      description: `₦${amount.toLocaleString()} added to your Top-Up plan.`,
    });
    setShowPayment(false);
  };

  const handlePaymentError = (error: string) => {
    toast({
      title: "Payment Failed",
      description: error,
      variant: "destructive",
    });
  };

  if (showPayment) {
    return (
      <FintechCard className="p-6">
        <div className="mb-4">
          <Button 
            variant="outline" 
            onClick={() => setShowPayment(false)}
            className="mb-4"
          >
            ← Back to Plan
          </Button>
        </div>
        <PaystackIntegration
          title="Fund Your Top-Up Plan"
          onSuccess={handlePaymentSuccess}
          onError={handlePaymentError}
          purpose="top_up_plan"
        />
      </FintechCard>
    );
  }

  return (
    <FintechCard className="p-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="h-10 w-10 rounded-none bg-primary/10 flex items-center justify-center shadow-[inset_2px_2px_4px_rgba(255,255,255,0.3),inset_-2px_-2px_4px_rgba(0,0,0,0.2)]">
          <Target className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h2 className="text-xl font-semibold">Top-Up Plan</h2>
          <p className="text-sm text-muted-foreground">Flexible savings with auto-calculated frequency</p>
        </div>
      </div>

      <div className="space-y-6">
        {/* Current Balance */}
        <div className="bg-green-50 dark:bg-green-900/20 border-2 border-green-200 dark:border-green-800 rounded-none p-4 shadow-[inset_2px_2px_4px_rgba(34,197,94,0.1),inset_-2px_-2px_4px_rgba(255,255,255,0.1)]">
          <div className="flex items-center gap-2 mb-1">
            <TrendingUp className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-green-600">Current Balance</span>
          </div>
          <p className="text-2xl font-bold text-green-600">
            ₦{currentBalance.toLocaleString()}
          </p>
        </div>

        {/* Plan Configuration */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FloatingLabelInput
            id="targetAmount"
            type="number"
            label="Target Amount (₦)"
            value={targetAmount}
            onChange={(e) => setTargetAmount(e.target.value)}
            placeholder="50,000"
          />
          
          <div className="space-y-2">
            <Label htmlFor="timeline">Timeline</Label>
            <div className="flex gap-2">
              <FloatingLabelInput
                id="timeline"
                type="number"
                label="Duration"
                value={timeline}
                onChange={(e) => setTimeline(e.target.value)}
                placeholder="6"
                className="flex-1"
              />
              <Select value={timelineUnit} onValueChange={setTimelineUnit}>
                <SelectTrigger className="w-32 rounded-none shadow-[inset_2px_2px_4px_rgba(0,0,0,0.1),inset_-2px_-2px_4px_rgba(255,255,255,0.1)]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="days">Days</SelectItem>
                  <SelectItem value="weeks">Weeks</SelectItem>
                  <SelectItem value="months">Months</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Auto-Calculated Results */}
        {calculatedData && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border-2 border-blue-200 dark:border-blue-800 rounded-none p-4 shadow-[inset_2px_2px_4px_rgba(59,130,246,0.1),inset_-2px_-2px_4px_rgba(255,255,255,0.1)]">
            <div className="flex items-center gap-2 mb-3">
              <Calculator className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-600">Auto-Calculated Plan</span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <p className="text-xs text-blue-600/80">Daily</p>
                <p className="font-semibold text-blue-600">₦{calculatedData.dailyAmount.toFixed(2)}</p>
              </div>
              <div className="text-center">
                <p className="text-xs text-blue-600/80">Weekly</p>
                <p className="font-semibold text-blue-600">₦{calculatedData.weeklyAmount.toFixed(2)}</p>
              </div>
              <div className="text-center">
                <p className="text-xs text-blue-600/80">Monthly</p>
                <p className="font-semibold text-blue-600">₦{calculatedData.monthlyAmount.toFixed(2)}</p>
              </div>
            </div>
            
            <div className="mt-4 p-3 bg-blue-100 dark:bg-blue-800/30 rounded-none">
              <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                Recommended: ₦{calculatedData.recommendedAmount.toFixed(2)} {calculatedData.frequency}
              </p>
              <p className="text-xs text-blue-600 dark:text-blue-300 mt-1">
                Total needed: ₦{calculatedData.requiredAmount.toLocaleString()}
              </p>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <Button 
            variant="outline" 
            onClick={handleCreatePlan}
            disabled={!calculatedData}
            className="border-primary text-primary hover:bg-primary hover:text-white"
          >
            <Calendar className="mr-2 h-4 w-4" />
            Create Plan
          </Button>
          <Button 
            onClick={() => setShowPayment(true)}
            disabled={!calculatedData}
            className="bg-primary hover:bg-primary/90"
          >
            <Target className="mr-2 h-4 w-4" />
            Make Deposit
          </Button>
        </div>
      </div>
    </FintechCard>
  );
};

export default TopUpPlan;