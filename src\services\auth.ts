export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: 'user' | 'admin';
  kycStatus: 'pending' | 'verified' | 'rejected';
  profile?: {
    first_name: string;
    last_name: string;
    avatar_url?: string;
    status?: 'active' | 'blocked' | 'suspended';
    kyc_status?: 'pending' | 'verified' | 'rejected';
  };
  balance?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
}

export class AuthService {

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      // Real API call only
      const response = await apiService.post<AuthResponse>('/auth/login', credentials);
      this.setAuthData(response);
      return response;
    } catch (error: any) {
      console.error('Login error:', error);
      throw error;
    }
  }

  async register(data: RegisterData): Promise<AuthResponse> {
    try {
      // Real API call only
      const response = await apiService.post<AuthResponse>('/auth/register', data);
      this.setAuthData(response);
      return response;
    } catch (error: any) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    this.clearAuthData();
  }

  async refreshToken(): Promise<AuthResponse> {
    // For MongoDB demo, just return current stored data
    const storedUser = this.getStoredUser();
    if (!storedUser) {
      throw new Error('No user data found');
    }
    
    const authResponse: AuthResponse = {
      user: storedUser,
      token: 'refreshed-token-' + Date.now(),
      refreshToken: 'refreshed-refresh-' + Date.now()
    };
    
    this.setAuthData(authResponse);
    return authResponse;
  }

  async getCurrentUser(): Promise<User> {
    const storedUser = this.getStoredUser();
    if (storedUser) {
      return storedUser;
    }
    throw new Error('No user data found');
  }

  async updateProfile(data: Partial<User>): Promise<User> {
    const currentUser = this.getStoredUser();
    if (!currentUser) {
      throw new Error('No user logged in');
    }

    const updatedUser = { ...currentUser, ...data };
    localStorage.setItem('user', JSON.stringify(updatedUser));
    
    return updatedUser;
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    // Real API call only
    await apiService.post('/auth/change-password', {
      currentPassword,
      newPassword
    });
  }

  async requestPasswordReset(email: string): Promise<void> {
    // Real API call only
    await apiService.post('/auth/forgot-password', { email });
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    // Real API call only
    await apiService.post('/auth/reset-password', {
      token,
      newPassword
    });
  }

  private setAuthData(authResponse: AuthResponse): void {
    localStorage.setItem('token', authResponse.token);
    localStorage.setItem('user', JSON.stringify(authResponse.user));
    localStorage.setItem('userRole', authResponse.user.role || 'user');
    localStorage.setItem('userId', authResponse.user.id || authResponse.user._id);
    if (authResponse.refreshToken) {
      localStorage.setItem('refreshToken', authResponse.refreshToken);
    }
  }

  clearAuthData(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('userRole');
    localStorage.removeItem('userId');
    localStorage.removeItem('refreshToken');
  }

  getStoredUser(): User | null {
    try {
      const userData = localStorage.getItem('user');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error parsing stored user data:', error);
      return null;
    }
  }

  getStoredToken(): string | null {
    return localStorage.getItem('token');
  }

  isAuthenticated(): boolean {
    return !!this.getStoredToken();
  }



  // Remove demo mode - all authentication should go through real API

  private transformMongoUser(mongoUser: any): User {
    return {
      id: mongoUser._id || mongoUser.id,
      email: mongoUser.email,
      firstName: mongoUser.firstName,
      lastName: mongoUser.lastName,
      phone: mongoUser.phone,
      role: mongoUser.role || 'user',
      kycStatus: mongoUser.kycStatus || 'pending',
      balance: mongoUser.balance || 0,
      profile: {
        first_name: mongoUser.firstName,
        last_name: mongoUser.lastName,
        status: mongoUser.status || 'active',
        kyc_status: mongoUser.kycStatus || 'pending'
      },
      createdAt: mongoUser.createdAt,
      updatedAt: mongoUser.updatedAt
    };
  }
}

export const authService = new AuthService();
