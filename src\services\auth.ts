export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: 'user' | 'admin';
  kycStatus: 'pending' | 'verified' | 'rejected';
  profile?: {
    first_name: string;
    last_name: string;
    avatar_url?: string;
    status?: 'active' | 'blocked' | 'suspended';
    kyc_status?: 'pending' | 'verified' | 'rejected';
  };
  balance?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
}

export class AuthService {

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      if (this.isDemoMode()) {
        console.log('Login attempt - using demo mode');
        return this.createDemoResponse(credentials.email);
      }

      // Real API call
      const response = await apiService.post<AuthResponse>('/auth/login', credentials);
      this.setAuthData(response);
      return response;
    } catch (error: any) {
      console.error('Login error:', error);
      throw error;
    }
  }

  async register(data: RegisterData): Promise<AuthResponse> {
    try {
      if (this.isDemoMode()) {
        console.log('Registration attempt - using demo mode');
        return this.createDemoResponse(data.email, data.firstName, data.lastName);
      }

      // Real API call
      const response = await apiService.post<AuthResponse>('/auth/register', data);
      this.setAuthData(response);
      return response;
    } catch (error: any) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    this.clearAuthData();
  }

  async refreshToken(): Promise<AuthResponse> {
    // For MongoDB demo, just return current stored data
    const storedUser = this.getStoredUser();
    if (!storedUser) {
      throw new Error('No user data found');
    }
    
    const authResponse: AuthResponse = {
      user: storedUser,
      token: 'refreshed-token-' + Date.now(),
      refreshToken: 'refreshed-refresh-' + Date.now()
    };
    
    this.setAuthData(authResponse);
    return authResponse;
  }

  async getCurrentUser(): Promise<User> {
    const storedUser = this.getStoredUser();
    if (storedUser) {
      return storedUser;
    }
    throw new Error('No user data found');
  }

  async updateProfile(data: Partial<User>): Promise<User> {
    const currentUser = this.getStoredUser();
    if (!currentUser) {
      throw new Error('No user logged in');
    }

    const updatedUser = { ...currentUser, ...data };
    localStorage.setItem('user', JSON.stringify(updatedUser));
    
    return updatedUser;
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    if (this.isDemoMode()) {
      // For demo, just validate password length
      if (newPassword.length < 6) {
        throw new Error('Password must be at least 6 characters');
      }
      console.log('Password changed successfully (demo mode)');
      return;
    }

    // Real API call
    await apiService.post('/auth/change-password', {
      currentPassword,
      newPassword
    });
  }

  async requestPasswordReset(email: string): Promise<void> {
    if (this.isDemoMode()) {
      console.log('Password reset requested for:', email, '(demo mode)');
      return;
    }

    // Real API call
    await apiService.post('/auth/forgot-password', { email });
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    if (this.isDemoMode()) {
      if (newPassword.length < 6) {
        throw new Error('Password must be at least 6 characters');
      }
      console.log('Password reset successfully (demo mode)');
      return;
    }

    // Real API call
    await apiService.post('/auth/reset-password', {
      token,
      newPassword
    });
  }

  private setAuthData(authResponse: AuthResponse): void {
    localStorage.setItem('token', authResponse.token);
    localStorage.setItem('user', JSON.stringify(authResponse.user));
    if (authResponse.refreshToken) {
      localStorage.setItem('refreshToken', authResponse.refreshToken);
    }
  }

  clearAuthData(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('refreshToken');
  }

  getStoredUser(): User | null {
    try {
      const userData = localStorage.getItem('user');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error parsing stored user data:', error);
      return null;
    }
  }

  getStoredToken(): string | null {
    return localStorage.getItem('token');
  }

  isAuthenticated(): boolean {
    return !!this.getStoredToken();
  }

  private isDemoMode(): boolean {
    // Enable demo mode when backend is not available or explicitly enabled
    const apiUrl = import.meta.env.VITE_API_URL;
    const demoMode = import.meta.env.VITE_DEMO_MODE;
    
    // Demo mode is enabled if:
    // 1. No API URL is set
    // 2. Demo mode is explicitly enabled
    // 3. API URL contains placeholder text
    return !apiUrl || 
           demoMode === 'true' || 
           apiUrl.includes('your-backend-api') ||
           apiUrl.includes('placeholder') ||
           apiUrl.includes('localhost') && !window.location.href.includes('localhost');
  }

  private createDemoResponse(email: string, firstName?: string, lastName?: string): AuthResponse {
    const demoUser: User = {
      id: 'demo-user-' + Date.now(),
      email,
      firstName: firstName || email.split('@')[0],
      lastName: lastName || 'User',
      role: email.includes('admin') ? 'admin' : 'user',
      kycStatus: 'pending',
      balance: 5000,
      profile: {
        first_name: firstName || email.split('@')[0],
        last_name: lastName || 'User',
        status: 'active',
        kyc_status: 'pending',
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const authResponse: AuthResponse = {
      user: demoUser,
      token: 'demo-token-' + Date.now(),
      refreshToken: 'demo-refresh-token-' + Date.now(),
    };

    this.setAuthData(authResponse);
    return authResponse;
  }

  private transformMongoUser(mongoUser: any): User {
    return {
      id: mongoUser._id || mongoUser.id,
      email: mongoUser.email,
      firstName: mongoUser.firstName,
      lastName: mongoUser.lastName,
      phone: mongoUser.phone,
      role: mongoUser.role || 'user',
      kycStatus: mongoUser.kycStatus || 'pending',
      balance: mongoUser.balance || 0,
      profile: {
        first_name: mongoUser.firstName,
        last_name: mongoUser.lastName,
        status: mongoUser.status || 'active',
        kyc_status: mongoUser.kycStatus || 'pending'
      },
      createdAt: mongoUser.createdAt,
      updatedAt: mongoUser.updatedAt
    };
  }
}

export const authService = new AuthService();
