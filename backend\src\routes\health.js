const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const { auth, admin } = require('../middleware/auth');
const databaseHealthService = require('../services/DatabaseHealthService');
const databaseMonitoring = require('../middleware/databaseMonitoring');
const { getDBStatus } = require('../config/database');

/**
 * Basic health check endpoint
 * Public endpoint for load balancers and monitoring services
 */
router.get('/', async (req, res) => {
  try {
    const startTime = Date.now();
    
    // Basic connectivity check
    await mongoose.connection.db.admin().ping();
    
    const responseTime = Date.now() - startTime;
    const dbStatus = getDBStatus();
    
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: {
        connected: dbStatus.isConnected,
        readyState: dbStatus.readyState,
        responseTime: responseTime
      },
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version
    };
    
    res.status(200).json(health);
    
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      database: {
        connected: false,
        error: error.message
      }
    });
  }
});

/**
 * Detailed health status (admin only)
 */
router.get('/detailed', auth, admin, async (req, res) => {
  try {
    const healthStatus = databaseHealthService.getHealthStatus();
    const dbStatus = getDBStatus();
    const monitoringStats = databaseMonitoring.getStats();
    
    const detailedHealth = {
      timestamp: new Date().toISOString(),
      overall: {
        status: healthStatus.connectionStatus,
        uptime: process.uptime(),
        nodeVersion: process.version,
        environment: process.env.NODE_ENV
      },
      database: {
        ...dbStatus,
        ...healthStatus,
        monitoring: monitoringStats
      },
      system: {
        memory: process.memoryUsage(),
        cpu: process.cpuUsage(),
        platform: process.platform,
        arch: process.arch
      }
    };
    
    res.json({
      success: true,
      data: detailedHealth
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to get detailed health status',
      error: error.message
    });
  }
});

/**
 * Database performance metrics (admin only)
 */
router.get('/performance', auth, admin, async (req, res) => {
  try {
    const { timeRange = 3600000 } = req.query; // Default: 1 hour
    
    const performanceReport = databaseHealthService.getPerformanceReport(parseInt(timeRange));
    const healthStatus = databaseHealthService.getHealthStatus();
    
    res.json({
      success: true,
      data: {
        report: performanceReport,
        currentMetrics: {
          responseTime: healthStatus.responseTime,
          activeConnections: healthStatus.activeConnections,
          totalQueries: healthStatus.totalQueries,
          slowQueries: healthStatus.slowQueries,
          errors: healthStatus.errors
        },
        queryPerformance: healthStatus.queryPerformance
      }
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to get performance metrics',
      error: error.message
    });
  }
});

/**
 * Database connection info (admin only)
 */
router.get('/connection', auth, admin, async (req, res) => {
  try {
    const connection = mongoose.connection;
    const dbStatus = getDBStatus();
    
    // Get server status
    let serverInfo = {};
    try {
      const serverStatus = await connection.db.admin().serverStatus();
      serverInfo = {
        version: serverStatus.version,
        uptime: serverStatus.uptime,
        connections: serverStatus.connections,
        network: serverStatus.network,
        opcounters: serverStatus.opcounters
      };
    } catch (error) {
      serverInfo.error = error.message;
    }
    
    const connectionInfo = {
      client: {
        ...dbStatus,
        host: connection.host,
        port: connection.port,
        name: connection.name,
        readyState: connection.readyState,
        readyStateText: getReadyStateText(connection.readyState)
      },
      server: serverInfo
    };
    
    res.json({
      success: true,
      data: connectionInfo
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to get connection info',
      error: error.message
    });
  }
});

/**
 * Collection statistics (admin only)
 */
router.get('/collections', auth, admin, async (req, res) => {
  try {
    const db = mongoose.connection.db;
    const collections = await db.listCollections().toArray();
    
    const collectionStats = [];
    
    for (const collection of collections) {
      try {
        const stats = await db.collection(collection.name).stats();
        const indexInfo = await db.collection(collection.name).indexInformation();
        
        collectionStats.push({
          name: collection.name,
          count: stats.count,
          size: stats.size,
          avgObjSize: stats.avgObjSize,
          storageSize: stats.storageSize,
          indexes: {
            count: stats.nindexes,
            totalSize: stats.totalIndexSize,
            list: Object.keys(indexInfo)
          }
        });
      } catch (error) {
        collectionStats.push({
          name: collection.name,
          error: error.message
        });
      }
    }
    
    res.json({
      success: true,
      data: {
        totalCollections: collections.length,
        collections: collectionStats
      }
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to get collection statistics',
      error: error.message
    });
  }
});

/**
 * Index analysis (admin only)
 */
router.get('/indexes', auth, admin, async (req, res) => {
  try {
    const { collection } = req.query;
    const db = mongoose.connection.db;
    
    if (collection) {
      // Get stats for specific collection
      const indexStats = await db.collection(collection).indexStats().toArray();
      const indexInfo = await db.collection(collection).indexInformation();
      
      res.json({
        success: true,
        data: {
          collection,
          indexes: indexStats.map(stat => ({
            name: stat.name,
            accesses: stat.accesses.ops,
            since: stat.accesses.since,
            spec: indexInfo[stat.name]
          }))
        }
      });
    } else {
      // Get stats for all collections
      const collections = await db.listCollections().toArray();
      const allIndexStats = {};
      
      for (const coll of collections.slice(0, 10)) { // Limit to first 10
        try {
          const indexStats = await db.collection(coll.name).indexStats().toArray();
          allIndexStats[coll.name] = indexStats.map(stat => ({
            name: stat.name,
            accesses: stat.accesses.ops,
            since: stat.accesses.since
          }));
        } catch (error) {
          allIndexStats[coll.name] = { error: error.message };
        }
      }
      
      res.json({
        success: true,
        data: allIndexStats
      });
    }
    
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to get index statistics',
      error: error.message
    });
  }
});

/**
 * Start/stop health monitoring (admin only)
 */
router.post('/monitoring/:action', auth, admin, async (req, res) => {
  try {
    const { action } = req.params;
    const { interval } = req.body;
    
    if (action === 'start') {
      databaseHealthService.startMonitoring(interval);
      res.json({
        success: true,
        message: 'Health monitoring started',
        interval: interval || 30000
      });
    } else if (action === 'stop') {
      databaseHealthService.stopMonitoring();
      res.json({
        success: true,
        message: 'Health monitoring stopped'
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Invalid action. Use "start" or "stop"'
      });
    }
    
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to control monitoring',
      error: error.message
    });
  }
});

/**
 * Reset health metrics (admin only)
 */
router.post('/reset', auth, admin, async (req, res) => {
  try {
    databaseHealthService.resetMetrics();
    
    res.json({
      success: true,
      message: 'Health metrics reset successfully'
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to reset metrics',
      error: error.message
    });
  }
});

/**
 * Force database reconnection (admin only)
 */
router.post('/reconnect', auth, admin, async (req, res) => {
  try {
    const { forceReconnect } = require('../config/database');
    await forceReconnect();
    
    res.json({
      success: true,
      message: 'Database reconnection initiated'
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to reconnect database',
      error: error.message
    });
  }
});

/**
 * Get alert history (admin only)
 */
router.get('/alerts', auth, admin, async (req, res) => {
  try {
    const { limit = 50 } = req.query;
    const healthStatus = databaseHealthService.getHealthStatus();
    
    res.json({
      success: true,
      data: {
        alerts: healthStatus.alertHistory.slice(-parseInt(limit)),
        totalAlerts: healthStatus.alertHistory.length
      }
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to get alert history',
      error: error.message
    });
  }
});

/**
 * Helper function to get readable connection state
 */
function getReadyStateText(readyState) {
  const states = {
    0: 'disconnected',
    1: 'connected',
    2: 'connecting',
    3: 'disconnecting'
  };
  return states[readyState] || 'unknown';
}

module.exports = router;
