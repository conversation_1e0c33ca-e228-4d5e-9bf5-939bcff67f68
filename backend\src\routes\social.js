const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const GroupPost = require('../models/GroupPost');
const GroupMilestone = require('../models/GroupMilestone');
const GroupSavingsPlan = require('../models/GroupSavingsPlan');
const User = require('../models/User');

// Get group feed/posts
router.get('/groups/:groupId/posts', auth, async (req, res) => {
  try {
    const { groupId } = req.params;
    const { page = 1, limit = 20 } = req.query;
    
    // Verify user is member of the group
    const group = await GroupSavingsPlan.findById(groupId);
    if (!group) {
      return res.status(404).json({ success: false, message: 'Group not found' });
    }
    
    const isMember = group.members.some(member => 
      member.userId.toString() === req.user.id.toString()
    );
    
    if (!isMember && group.createdBy.toString() !== req.user.id.toString()) {
      return res.status(403).json({ success: false, message: 'Access denied' });
    }
    
    const posts = await GroupPost.find({ groupId })
      .populate('authorId', 'firstName lastName profile.avatar')
      .populate('likes.userId', 'firstName lastName')
      .populate('comments.userId', 'firstName lastName')
      .sort({ isPinned: -1, createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await GroupPost.countDocuments({ groupId });
    
    res.json({
      success: true,
      data: {
        posts,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching group posts:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch group posts' });
  }
});

// Create new post in group
router.post('/groups/:groupId/posts', auth, async (req, res) => {
  try {
    const { groupId } = req.params;
    const { content, type = 'text' } = req.body;
    
    if (!content || content.trim().length === 0) {
      return res.status(400).json({ 
        success: false, 
        message: 'Post content is required' 
      });
    }
    
    // Verify user is member of the group
    const group = await GroupSavingsPlan.findById(groupId);
    if (!group) {
      return res.status(404).json({ success: false, message: 'Group not found' });
    }
    
    const isMember = group.members.some(member => 
      member.userId.toString() === req.user.id.toString()
    );
    
    if (!isMember && group.createdBy.toString() !== req.user.id.toString()) {
      return res.status(403).json({ success: false, message: 'Access denied' });
    }
    
    const post = new GroupPost({
      groupId,
      authorId: req.user.id,
      content: content.trim(),
      type
    });
    
    await post.save();
    await post.populate('authorId', 'firstName lastName profile.avatar');
    
    res.status(201).json({ 
      success: true, 
      data: post,
      message: 'Post created successfully' 
    });
  } catch (error) {
    console.error('Error creating group post:', error);
    res.status(500).json({ success: false, message: 'Failed to create post' });
  }
});

// Like/unlike a post
router.post('/posts/:postId/like', auth, async (req, res) => {
  try {
    const post = await GroupPost.findById(req.params.postId);
    
    if (!post) {
      return res.status(404).json({ success: false, message: 'Post not found' });
    }
    
    // Verify user has access to the group
    const group = await GroupSavingsPlan.findById(post.groupId);
    const isMember = group.members.some(member => 
      member.userId.toString() === req.user.id.toString()
    );
    
    if (!isMember && group.createdBy.toString() !== req.user.id.toString()) {
      return res.status(403).json({ success: false, message: 'Access denied' });
    }
    
    await post.toggleLike(req.user.id);
    
    res.json({ 
      success: true, 
      data: { 
        likes: post.likeCount,
        isLiked: post.isLikedBy(req.user.id)
      },
      message: 'Post like toggled successfully' 
    });
  } catch (error) {
    console.error('Error toggling post like:', error);
    res.status(500).json({ success: false, message: 'Failed to toggle like' });
  }
});

// Add comment to post
router.post('/posts/:postId/comments', auth, async (req, res) => {
  try {
    const { content } = req.body;
    
    if (!content || content.trim().length === 0) {
      return res.status(400).json({ 
        success: false, 
        message: 'Comment content is required' 
      });
    }
    
    const post = await GroupPost.findById(req.params.postId);
    
    if (!post) {
      return res.status(404).json({ success: false, message: 'Post not found' });
    }
    
    // Verify user has access to the group
    const group = await GroupSavingsPlan.findById(post.groupId);
    const isMember = group.members.some(member => 
      member.userId.toString() === req.user.id.toString()
    );
    
    if (!isMember && group.createdBy.toString() !== req.user.id.toString()) {
      return res.status(403).json({ success: false, message: 'Access denied' });
    }
    
    await post.addComment(req.user.id, content.trim());
    await post.populate('comments.userId', 'firstName lastName');
    
    res.json({ 
      success: true, 
      data: post.comments[post.comments.length - 1],
      message: 'Comment added successfully' 
    });
  } catch (error) {
    console.error('Error adding comment:', error);
    res.status(500).json({ success: false, message: 'Failed to add comment' });
  }
});

// Get group milestones
router.get('/groups/:groupId/milestones', auth, async (req, res) => {
  try {
    const { groupId } = req.params;
    
    // Verify user is member of the group
    const group = await GroupSavingsPlan.findById(groupId);
    if (!group) {
      return res.status(404).json({ success: false, message: 'Group not found' });
    }
    
    const isMember = group.members.some(member => 
      member.userId.toString() === req.user.id.toString()
    );
    
    if (!isMember && group.createdBy.toString() !== req.user.id.toString()) {
      return res.status(403).json({ success: false, message: 'Access denied' });
    }
    
    const milestones = await GroupMilestone.find({ groupId })
      .populate('achievedBy.userId', 'firstName lastName profile.avatar')
      .sort({ priority: -1, createdAt: -1 });
    
    res.json({ success: true, data: milestones });
  } catch (error) {
    console.error('Error fetching group milestones:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch milestones' });
  }
});

// Get group leaderboard
router.get('/groups/:groupId/leaderboard', auth, async (req, res) => {
  try {
    const { groupId } = req.params;
    
    // Verify user is member of the group
    const group = await GroupSavingsPlan.findById(groupId)
      .populate('members.userId', 'firstName lastName profile.avatar reputation');
    
    if (!group) {
      return res.status(404).json({ success: false, message: 'Group not found' });
    }
    
    const isMember = group.members.some(member => 
      member.userId.toString() === req.user.id.toString()
    );
    
    if (!isMember && group.createdBy.toString() !== req.user.id.toString()) {
      return res.status(403).json({ success: false, message: 'Access denied' });
    }
    
    // Calculate member rankings
    const leaderboard = group.members
      .map((member, index) => ({
        userId: member.userId._id,
        name: `${member.userId.firstName} ${member.userId.lastName}`,
        avatar: member.userId.profile?.avatar,
        contributions: member.totalContributions || 0,
        streak: member.streak || 0,
        rank: index + 1,
        reputation: member.userId.reputation?.score || 100,
        joinedAt: member.joinedAt
      }))
      .sort((a, b) => {
        // Sort by contributions, then by streak, then by reputation
        if (b.contributions !== a.contributions) return b.contributions - a.contributions;
        if (b.streak !== a.streak) return b.streak - a.streak;
        return b.reputation - a.reputation;
      })
      .map((member, index) => ({ ...member, rank: index + 1 }));
    
    res.json({ success: true, data: leaderboard });
  } catch (error) {
    console.error('Error fetching group leaderboard:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch leaderboard' });
  }
});

// Update user reputation
router.patch('/users/:userId/reputation', auth, async (req, res) => {
  try {
    const { userId } = req.params;
    const { action, points = 1 } = req.body;
    
    // Only allow users to update their own reputation or admin/staff
    const currentUser = await User.findById(req.user.id);
    if (req.user.id !== userId && !['admin', 'staff'].includes(currentUser.role)) {
      return res.status(403).json({ success: false, message: 'Access denied' });
    }
    
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }
    
    let scoreChange = 0;
    
    switch (action) {
      case 'payment_on_time':
        scoreChange = Math.min(2, points);
        user.reputation.onTimePayments += 1;
        break;
      case 'payment_late':
        scoreChange = Math.max(-5, -points);
        break;
      case 'group_contribution':
        scoreChange = Math.min(3, points);
        user.reputation.groupContributions += 1;
        break;
      case 'milestone_achieved':
        scoreChange = Math.min(5, points);
        break;
      default:
        return res.status(400).json({ success: false, message: 'Invalid action' });
    }
    
    user.reputation.score = Math.max(0, Math.min(100, user.reputation.score + scoreChange));
    user.reputation.totalPayments += 1;
    
    // Update reputation level based on score
    if (user.reputation.score >= 90) user.reputation.level = 'platinum';
    else if (user.reputation.score >= 75) user.reputation.level = 'gold';
    else if (user.reputation.score >= 50) user.reputation.level = 'silver';
    else user.reputation.level = 'bronze';
    
    await user.save();
    
    res.json({ 
      success: true, 
      data: user.reputation,
      message: 'Reputation updated successfully' 
    });
  } catch (error) {
    console.error('Error updating reputation:', error);
    res.status(500).json({ success: false, message: 'Failed to update reputation' });
  }
});

module.exports = router;