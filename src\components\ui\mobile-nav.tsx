import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  BarChart3, 
  CreditCard, 
  Settings, 
  HelpCircle 
} from 'lucide-react';

interface MobileNavProps {
  className?: string;
  isAdmin?: boolean;
}

export function MobileNav({ className, isAdmin = false }: MobileNavProps) {
  const location = useLocation();
  
  const navItems = [
    {
      title: "Home",
      href: isAdmin ? "/admin/dashboard" : "/dashboard",
      icon: Home,
    },
    {
      title: "Savings",
      href: "/savings",
      icon: BarChart3,
      userOnly: true,
    },
    {
      title: "Payments",
      href: isAdmin ? "/admin/payment-management" : "/payments",
      icon: CreditCard,
    },
    {
      title: "Support",
      href: "/help-support",
      icon: HelpCircle,
    },
    {
      title: "Settings",
      href: isAdmin ? "/admin/settings" : "/settings",
      icon: Settings,
    },
  ];

  const filteredItems = navItems.filter(item => 
    !item.userOnly || !isAdmin
  );

  return (
    <nav className={cn(
      "fixed bottom-0 left-0 right-0 z-50 bg-card/95 backdrop-blur-sm border-t border-border px-4 py-2 md:hidden",
      "transform transition-transform duration-300 translate-y-0",
      className
    )}>
      <div className="flex items-center justify-around">
        {filteredItems.map((item) => {
          const isActive = location.pathname === item.href;
          const Icon = item.icon;
          
          return (
            <Link
              key={item.href}
              to={item.href}
              className={cn(
                "flex flex-col items-center gap-1 px-3 py-2 rounded-lg transition-all duration-200",
                isActive 
                  ? "text-primary bg-primary/10" 
                  : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
              )}
            >
              <Icon className="h-5 w-5" />
              <span className="text-xs font-medium">{item.title}</span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
}