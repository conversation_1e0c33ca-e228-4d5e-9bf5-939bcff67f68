# ===========================================
# BETTER INTEREST - BACKEND ENVIRONMENT
# ===========================================

# Application Configuration
NODE_ENV=development
PORT=5000
APP_NAME=Better Interest API
APP_VERSION=1.0.0

# Database Configuration
MONGO_URI=mongodb://localhost:27017/betterinterest
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=your_secure_mongo_password

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_secure_redis_password

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_key_here_minimum_32_characters_long
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Paystack Configuration
PAYSTACK_SECRET_KEY=sk_test_8f19d29fa6205d27a4a254667515c788798842b9
PAYSTACK_PUBLIC_KEY=pk_test_44f6dbb6159b73b84fff2fc441dcdd997e15c10d
PAYSTACK_BASE_URL=https://api.paystack.co

# CORS Configuration
CORS_ORIGIN=http://localhost:5173
FRONTEND_URL=http://localhost:5173

# Email Service Configuration (SendGrid)
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Better Interest

# SMS Service Configuration (Termii)
TERMII_API_KEY=your_termii_api_key
TERMII_SENDER_ID=BetterInt
TERMII_BASE_URL=https://api.ng.termii.com

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# Security Configuration
BCRYPT_ROUNDS=10
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=debug
LOG_FILE_PATH=./logs/app.log

# Interest Calculation Configuration
INTEREST_CALCULATION_CRON=0 0 * * *
MATURITY_CHECK_CRON=0 */1 * * *

# Fixed Deposit Configuration
FD_MINIMUM_AMOUNT=10000
FD_MAXIMUM_AMOUNT=10000000
FD_PENALTY_RATE=25
FD_DEFAULT_INTEREST_RATE=15

# Flex Savings Configuration
FLEX_MINIMUM_AMOUNT=1000
FLEX_DAILY_INTEREST_RATE=0.041
FLEX_WITHDRAWAL_LIMIT=1000000

# SafeLock Configuration
SAFELOCK_MINIMUM_AMOUNT=5000
SAFELOCK_MAXIMUM_AMOUNT=5000000
SAFELOCK_MINIMUM_DURATION=30
SAFELOCK_MAXIMUM_DURATION=365
SAFELOCK_PENALTY_RATE=50

# Investment Configuration
INVESTMENT_MINIMUM_AMOUNT=5000
INVESTMENT_MAXIMUM_AMOUNT=5000000

# Loan Configuration
LOAN_MINIMUM_AMOUNT=5000
LOAN_MAXIMUM_AMOUNT=500000
LOAN_DEFAULT_INTEREST_RATE=15
LOAN_COLLATERAL_PERCENTAGE=80

# Bill Payment Configuration
BILL_PAYMENT_PROVIDER=paystack
BILL_PAYMENT_COMMISSION_RATE=1.5

# Referral Configuration
REFERRAL_REWARD_AMOUNT=1000
REFERRAL_REFEREE_REWARD=500
REFERRAL_MINIMUM_SAVINGS=5000

# AutoSave Configuration
AUTOSAVE_MINIMUM_AMOUNT=100
AUTOSAVE_MAXIMUM_AMOUNT=50000
AUTOSAVE_MAX_RETRIES=3

# Round-Up Configuration
ROUNDUP_MINIMUM_AMOUNT=10
ROUNDUP_MAXIMUM_AMOUNT=100
ROUNDUP_MONTHLY_LIMIT=10000

# Session Configuration
SESSION_SECRET=your_secure_session_secret_key_here
SESSION_MAX_AGE=********

# API Rate Limiting
API_RATE_LIMIT_WINDOW=15
API_RATE_LIMIT_MAX=100

# Webhook Configuration
WEBHOOK_SECRET=your_webhook_secret_key

# Third-party Integrations
# Credit Bureau API
CREDIT_BUREAU_API_KEY=your_credit_bureau_api_key
CREDIT_BUREAU_BASE_URL=https://api.creditbureau.com

# Bank Verification API
BVN_VERIFICATION_API_KEY=your_bvn_api_key
BVN_VERIFICATION_BASE_URL=https://api.bvnverification.com

# Push Notification Configuration
FCM_SERVER_KEY=your_fcm_server_key
FCM_SENDER_ID=your_fcm_sender_id

# Error Tracking
SENTRY_DSN=https://<EMAIL>/project_id

# Feature Flags
ENABLE_INVESTMENTS=true
ENABLE_LOANS=true
ENABLE_BILL_PAYMENTS=true
ENABLE_GROUP_SAVINGS=true
ENABLE_FIXED_DEPOSITS=true
ENABLE_FLEX_SAVINGS=true
ENABLE_SAFELOCK=true
ENABLE_AUTOSAVE=true
ENABLE_ROUNDUP=true
ENABLE_REFERRALS=true

# Development Configuration
DEBUG_MODE=true
ENABLE_SWAGGER=true
ENABLE_CORS_LOGGING=true

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="We are currently performing scheduled maintenance. Please try again later."

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=100

# Performance Configuration
MAX_CONCURRENT_REQUESTS=1000
REQUEST_TIMEOUT=30000
KEEP_ALIVE_TIMEOUT=5000

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=5
