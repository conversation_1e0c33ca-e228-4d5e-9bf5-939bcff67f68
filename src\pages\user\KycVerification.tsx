
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { DojahKycModal } from "@/components/kyc/DojahKycModal";
import { BalanceCard } from "@/components/fintech/balance-card";
import { useAuth } from "@/hooks/use-auth";
import { toast } from "sonner";
import { LoadingScreen } from "@/components/ui/loading-screen";
import { Shield, CheckCircle, Clock, XCircle } from "lucide-react";

const KycVerification = () => {
  const { user, refreshUser } = useAuth();
  const [showKycModal, setShowKycModal] = useState(false);
  
  const handleKycSuccess = async (formData: any) => {
    try {
      // TODO: Update user profile with KYC status via Supabase
      toast.success("KYC verification initiated. You will receive a callback once verification is complete.");
      await refreshUser();
    } catch (error: any) {
      console.error("Error starting KYC verification:", error);
      toast.error(error.message || "Failed to start KYC verification");
    }
  };
  
  const kyc_status = user?.profile?.kyc_status;
  
  if (!user) {
    return <LoadingScreen />;
  }
  
  const getStatusIcon = () => {
    switch (kyc_status) {
      case 'verified':
        return <CheckCircle className="h-6 w-6 text-green-600" />;
      case 'pending':
        return <Clock className="h-6 w-6 text-yellow-600" />;
      case 'rejected':
        return <XCircle className="h-6 w-6 text-red-600" />;
      default:
        return <Shield className="h-6 w-6 text-blue-600" />;
    }
  };

  const getStatusColor = () => {
    switch (kyc_status) {
      case 'verified':
        return "from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20";
      case 'pending':
        return "from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20";
      case 'rejected':
        return "from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20";
      default:
        return "from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20";
    }
  };

  return (
    <div className="space-y-6 p-4 lg:p-6">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h1 className="text-2xl lg:text-3xl font-bold">KYC Verification</h1>
          <p className="text-muted-foreground">Complete your identity verification with Dojah</p>
        </div>
      </div>
      
      <Card className={`border-0 bg-gradient-to-br ${getStatusColor()} shadow-lg`}>
        <CardHeader>
          <div className="flex items-center gap-3">
            {getStatusIcon()}
            <div>
              <CardTitle>Identity Verification Status</CardTitle>
              <CardDescription className="text-base">
                {kyc_status === 'verified' 
                  ? "Your identity has been verified successfully with Dojah."
                  : kyc_status === 'pending'
                  ? "Your verification is being processed by Dojah."
                  : kyc_status === 'rejected'
                  ? "Your previous verification was rejected. Please try again."
                  : "Complete your identity verification to unlock all features."}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {kyc_status === 'verified' ? (
            <div className="p-4 bg-white/70 dark:bg-background/70 rounded-lg">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium text-green-800 dark:text-green-400">Verification Complete</p>
                  <p className="text-sm text-green-700 dark:text-green-500">You have full access to all platform features.</p>
                </div>
              </div>
            </div>
          ) : kyc_status === 'pending' ? (
            <div className="p-4 bg-white/70 dark:bg-background/70 rounded-lg">
              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="font-medium text-yellow-800 dark:text-yellow-400">Verification In Progress</p>
                  <p className="text-sm text-yellow-700 dark:text-yellow-500">Dojah is processing your verification. This usually takes 2-5 minutes.</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="p-4 bg-white/70 dark:bg-background/70 rounded-lg">
                <h4 className="font-semibold mb-2">Verification Process:</h4>
                <ol className="text-sm space-y-1 text-muted-foreground">
                  <li>1. Click "Start Verification" below</li>
                  <li>2. Enter your full name and NIN/BVN</li>
                  <li>3. Complete verification on Dojah's secure platform</li>
                  <li>4. Receive instant verification results</li>
                </ol>
              </div>
              
              <Button 
                onClick={() => setShowKycModal(true)}
                className="w-full gap-2"
                size="lg"
              >
                <Shield className="h-5 w-5" />
                Start Verification with Dojah
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <DojahKycModal
        open={showKycModal}
        onOpenChange={setShowKycModal}
        onSuccess={handleKycSuccess}
      />
    </div>
  );
};

export default KycVerification;
