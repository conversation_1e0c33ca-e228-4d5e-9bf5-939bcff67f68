#!/bin/bash

# Better Interest Test Runner Script
# Comprehensive testing script for the application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
TEST_RESULTS_DIR="./test-results"
COVERAGE_DIR="./coverage"
LOG_FILE="./logs/test.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Setup test environment
setup_test_env() {
    log "Setting up test environment..."
    
    # Create necessary directories
    mkdir -p logs test-results coverage
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        log "Installing dependencies..."
        npm install
    fi
    
    success "Test environment setup completed"
}

# Run unit tests
run_unit_tests() {
    log "Running unit tests..."
    
    npm run test:unit -- --reporter=verbose --reporter=json --outputFile="$TEST_RESULTS_DIR/unit-tests.json"
    
    if [ $? -eq 0 ]; then
        success "Unit tests passed"
    else
        error "Unit tests failed"
    fi
}

# Run integration tests
run_integration_tests() {
    log "Running integration tests..."
    
    npm run test:integration -- --reporter=verbose --reporter=json --outputFile="$TEST_RESULTS_DIR/integration-tests.json"
    
    if [ $? -eq 0 ]; then
        success "Integration tests passed"
    else
        error "Integration tests failed"
    fi
}

# Run performance tests
run_performance_tests() {
    log "Running performance tests..."
    
    npm run test:performance -- --reporter=verbose --reporter=json --outputFile="$TEST_RESULTS_DIR/performance-tests.json"
    
    if [ $? -eq 0 ]; then
        success "Performance tests passed"
    else
        warning "Performance tests failed or had warnings"
    fi
}

# Run end-to-end tests
run_e2e_tests() {
    log "Running end-to-end tests..."
    
    # Start test server if not running
    if ! curl -f http://localhost:3000 > /dev/null 2>&1; then
        log "Starting test server..."
        npm run dev &
        TEST_SERVER_PID=$!
        sleep 10
    fi
    
    # Run E2E tests
    npm run test:e2e -- --reporter=json --outputFile="$TEST_RESULTS_DIR/e2e-tests.json"
    
    # Stop test server if we started it
    if [ ! -z "$TEST_SERVER_PID" ]; then
        kill $TEST_SERVER_PID
    fi
    
    if [ $? -eq 0 ]; then
        success "End-to-end tests passed"
    else
        error "End-to-end tests failed"
    fi
}

# Generate coverage report
generate_coverage() {
    log "Generating coverage report..."
    
    npm run test:coverage
    
    # Check coverage thresholds
    COVERAGE_SUMMARY="$COVERAGE_DIR/coverage-summary.json"
    
    if [ -f "$COVERAGE_SUMMARY" ]; then
        LINES_COVERAGE=$(cat "$COVERAGE_SUMMARY" | jq '.total.lines.pct')
        FUNCTIONS_COVERAGE=$(cat "$COVERAGE_SUMMARY" | jq '.total.functions.pct')
        BRANCHES_COVERAGE=$(cat "$COVERAGE_SUMMARY" | jq '.total.branches.pct')
        STATEMENTS_COVERAGE=$(cat "$COVERAGE_SUMMARY" | jq '.total.statements.pct')
        
        log "Coverage Summary:"
        log "  Lines: ${LINES_COVERAGE}%"
        log "  Functions: ${FUNCTIONS_COVERAGE}%"
        log "  Branches: ${BRANCHES_COVERAGE}%"
        log "  Statements: ${STATEMENTS_COVERAGE}%"
        
        # Check if coverage meets minimum thresholds
        if (( $(echo "$LINES_COVERAGE >= 80" | bc -l) )) && \
           (( $(echo "$FUNCTIONS_COVERAGE >= 80" | bc -l) )) && \
           (( $(echo "$BRANCHES_COVERAGE >= 80" | bc -l) )) && \
           (( $(echo "$STATEMENTS_COVERAGE >= 80" | bc -l) )); then
            success "Coverage thresholds met"
        else
            warning "Coverage thresholds not met (minimum 80%)"
        fi
    fi
    
    success "Coverage report generated"
}

# Run linting
run_linting() {
    log "Running linting checks..."
    
    # ESLint
    npm run lint
    
    if [ $? -eq 0 ]; then
        success "Linting passed"
    else
        error "Linting failed"
    fi
}

# Run type checking
run_type_checking() {
    log "Running TypeScript type checking..."
    
    npm run type-check
    
    if [ $? -eq 0 ]; then
        success "Type checking passed"
    else
        error "Type checking failed"
    fi
}

# Run security audit
run_security_audit() {
    log "Running security audit..."
    
    npm audit --audit-level=moderate
    
    if [ $? -eq 0 ]; then
        success "Security audit passed"
    else
        warning "Security audit found issues"
    fi
}

# Backend tests
run_backend_tests() {
    log "Running backend tests..."
    
    cd backend
    
    # Install backend dependencies if needed
    if [ ! -d "node_modules" ]; then
        log "Installing backend dependencies..."
        npm install
    fi
    
    # Run backend tests
    npm test
    
    if [ $? -eq 0 ]; then
        success "Backend tests passed"
    else
        error "Backend tests failed"
    fi
    
    cd ..
}

# Generate test report
generate_test_report() {
    log "Generating comprehensive test report..."
    
    REPORT_FILE="$TEST_RESULTS_DIR/test-report.html"
    
    cat > "$REPORT_FILE" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Better Interest Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .pass { color: green; }
        .fail { color: red; }
        .warn { color: orange; }
        .summary { background: #e8f5e8; padding: 10px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Better Interest Test Report</h1>
        <p>Generated on: $(date)</p>
    </div>
    
    <div class="section">
        <h2>Test Summary</h2>
        <div class="summary">
            <p>All tests have been executed. Check individual sections for details.</p>
        </div>
    </div>
    
    <div class="section">
        <h2>Coverage Report</h2>
        <p>Detailed coverage report available at: <a href="../coverage/index.html">Coverage Report</a></p>
    </div>
    
    <div class="section">
        <h2>Test Results</h2>
        <ul>
            <li>Unit Tests: Check unit-tests.json</li>
            <li>Integration Tests: Check integration-tests.json</li>
            <li>Performance Tests: Check performance-tests.json</li>
            <li>E2E Tests: Check e2e-tests.json</li>
        </ul>
    </div>
</body>
</html>
EOF
    
    success "Test report generated at $REPORT_FILE"
}

# Main test execution
run_all_tests() {
    log "Starting comprehensive test suite..."
    
    setup_test_env
    run_linting
    run_type_checking
    run_security_audit
    run_unit_tests
    run_integration_tests
    run_performance_tests
    generate_coverage
    run_backend_tests
    generate_test_report
    
    success "🎉 All tests completed successfully!"
    log "Test results available in: $TEST_RESULTS_DIR"
    log "Coverage report available in: $COVERAGE_DIR"
}

# Handle script arguments
case "${1:-all}" in
    "all")
        run_all_tests
        ;;
    "unit")
        setup_test_env
        run_unit_tests
        ;;
    "integration")
        setup_test_env
        run_integration_tests
        ;;
    "performance")
        setup_test_env
        run_performance_tests
        ;;
    "e2e")
        setup_test_env
        run_e2e_tests
        ;;
    "coverage")
        setup_test_env
        generate_coverage
        ;;
    "lint")
        run_linting
        ;;
    "type-check")
        run_type_checking
        ;;
    "security")
        run_security_audit
        ;;
    "backend")
        run_backend_tests
        ;;
    *)
        echo "Usage: $0 {all|unit|integration|performance|e2e|coverage|lint|type-check|security|backend}"
        echo ""
        echo "Commands:"
        echo "  all         - Run all tests (default)"
        echo "  unit        - Run unit tests only"
        echo "  integration - Run integration tests only"
        echo "  performance - Run performance tests only"
        echo "  e2e         - Run end-to-end tests only"
        echo "  coverage    - Generate coverage report"
        echo "  lint        - Run linting checks"
        echo "  type-check  - Run TypeScript type checking"
        echo "  security    - Run security audit"
        echo "  backend     - Run backend tests"
        exit 1
        ;;
esac
