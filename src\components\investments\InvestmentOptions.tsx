import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { TrendingUp, PieChart, BarChart3, Shield, DollarSign, AlertTriangle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface InvestmentProduct {
  id: string;
  name: string;
  type: 'mutual_fund' | 'stock' | 'bond';
  description: string;
  returns: string;
  riskLevel: 'low' | 'medium' | 'high';
  minimumAmount: number;
  duration: string;
  performance: number[];
  ytdReturn: number;
}

interface Portfolio {
  totalValue: number;
  totalInvested: number;
  totalReturns: number;
  investments: {
    productId: string;
    amount: number;
    units: number;
    currentValue: number;
  }[];
}

export const InvestmentOptions = () => {
  const { toast } = useToast();
  const [selectedProduct, setSelectedProduct] = useState<InvestmentProduct | null>(null);
  
  const [portfolio] = useState<Portfolio>({
    totalValue: 125000,
    totalInvested: 100000,
    totalReturns: 25000,
    investments: [
      { productId: '1', amount: 50000, units: 1000, currentValue: 62500 },
      { productId: '2', amount: 30000, units: 150, currentValue: 36000 },
      { productId: '3', amount: 20000, units: 500, currentValue: 26500 }
    ]
  });

  const [investmentProducts] = useState<InvestmentProduct[]>([
    {
      id: '1',
      name: 'Nigeria Equity Fund',
      type: 'mutual_fund',
      description: 'Diversified fund investing in top Nigerian companies',
      returns: '12-15% annually',
      riskLevel: 'medium',
      minimumAmount: 10000,
      duration: '1+ years',
      performance: [100, 105, 108, 112, 118, 125],
      ytdReturn: 14.2
    },
    {
      id: '2',
      name: 'Dangote Cement (DANGCEM)',
      type: 'stock',
      description: 'Leading cement manufacturer in Africa',
      returns: '8-20% annually',
      riskLevel: 'high',
      minimumAmount: 5000,
      duration: 'Flexible',
      performance: [100, 98, 102, 108, 115, 120],
      ytdReturn: 18.5
    },
    {
      id: '3',
      name: 'Nigerian Treasury Bills',
      type: 'bond',
      description: 'Government-backed fixed income securities',
      returns: '8-12% annually',
      riskLevel: 'low',
      minimumAmount: 1000,
      duration: '90-365 days',
      performance: [100, 102, 104, 106, 108, 110],
      ytdReturn: 9.8
    },
    {
      id: '4',
      name: 'Tech Growth Fund',
      type: 'mutual_fund',
      description: 'Fund focused on African tech companies',
      returns: '15-25% annually',
      riskLevel: 'high',
      minimumAmount: 25000,
      duration: '3+ years',
      performance: [100, 112, 108, 125, 135, 142],
      ytdReturn: 22.1
    },
    {
      id: '5',
      name: 'GTBank (GTCO)',
      type: 'stock',
      description: 'Leading Nigerian financial services company',
      returns: '10-18% annually',
      riskLevel: 'medium',
      minimumAmount: 5000,
      duration: 'Flexible',
      performance: [100, 103, 107, 105, 112, 116],
      ytdReturn: 12.8
    },
    {
      id: '6',
      name: 'Bond Index Fund',
      type: 'mutual_fund',
      description: 'Diversified portfolio of government and corporate bonds',
      returns: '10-14% annually',
      riskLevel: 'low',
      minimumAmount: 15000,
      duration: '2+ years',
      performance: [100, 103, 106, 109, 112, 114],
      ytdReturn: 11.5
    }
  ]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'mutual_fund': return <PieChart className="h-4 w-4" />;
      case 'stock': return <TrendingUp className="h-4 w-4" />;
      case 'bond': return <Shield className="h-4 w-4" />;
      default: return <BarChart3 className="h-4 w-4" />;
    }
  };

  const handleInvest = (product: InvestmentProduct) => {
    setSelectedProduct(product);
    toast({
      title: "Investment Selected",
      description: `You selected ${product.name}. Proceed to investment form.`,
    });
  };

  const portfolioReturn = ((portfolio.totalValue - portfolio.totalInvested) / portfolio.totalInvested) * 100;

  return (
    <div className="space-y-6">
      {/* Portfolio Overview */}
      <Card className="bg-gradient-to-br from-primary/5 to-secondary/5">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <PieChart className="h-5 w-5 text-primary" />
            <span>Investment Portfolio</span>
          </CardTitle>
          <CardDescription>Your investment overview and performance</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center space-y-2">
              <p className="text-sm text-muted-foreground">Total Value</p>
              <p className="text-2xl font-bold text-primary">
                {formatCurrency(portfolio.totalValue)}
              </p>
            </div>
            <div className="text-center space-y-2">
              <p className="text-sm text-muted-foreground">Invested</p>
              <p className="text-2xl font-bold">
                {formatCurrency(portfolio.totalInvested)}
              </p>
            </div>
            <div className="text-center space-y-2">
              <p className="text-sm text-muted-foreground">Returns</p>
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(portfolio.totalReturns)}
              </p>
            </div>
            <div className="text-center space-y-2">
              <p className="text-sm text-muted-foreground">Total Return</p>
              <p className={`text-2xl font-bold ${portfolioReturn >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {portfolioReturn >= 0 ? '+' : ''}{portfolioReturn.toFixed(1)}%
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Investment Products */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all">All Products</TabsTrigger>
          <TabsTrigger value="mutual_fund">Mutual Funds</TabsTrigger>
          <TabsTrigger value="stock">Stocks</TabsTrigger>
          <TabsTrigger value="bond">Bonds</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {investmentProducts.map((product) => (
              <Card key={product.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      {getTypeIcon(product.type)}
                      <CardTitle className="text-lg">{product.name}</CardTitle>
                    </div>
                    <Badge className={getRiskColor(product.riskLevel)}>
                      {product.riskLevel} risk
                    </Badge>
                  </div>
                  <CardDescription className="text-sm">
                    {product.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Performance Chart */}
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Performance (6M)</p>
                    <div className="h-16 flex items-end space-x-1">
                      {product.performance.map((value, index) => (
                        <div
                          key={index}
                          className="flex-1 bg-primary/20 rounded-sm"
                          style={{ height: `${(value / Math.max(...product.performance)) * 100}%` }}
                        />
                      ))}
                    </div>
                  </div>

                  {/* Key Metrics */}
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Expected Returns</span>
                      <span className="text-sm font-medium">{product.returns}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">YTD Return</span>
                      <span className={`text-sm font-medium ${product.ytdReturn >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {product.ytdReturn >= 0 ? '+' : ''}{product.ytdReturn}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Minimum</span>
                      <span className="text-sm font-medium">{formatCurrency(product.minimumAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Duration</span>
                      <span className="text-sm font-medium">{product.duration}</span>
                    </div>
                  </div>

                  <Button 
                    className="w-full" 
                    onClick={() => handleInvest(product)}
                  >
                    <DollarSign className="h-4 w-4 mr-2" />
                    Invest Now
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Filter by type */}
        {['mutual_fund', 'stock', 'bond'].map((type) => (
          <TabsContent key={type} value={type} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {investmentProducts
                .filter(product => product.type === type)
                .map((product) => (
                  <Card key={product.id} className="hover:shadow-lg transition-shadow">
                    {/* Same card content as above */}
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-2">
                          {getTypeIcon(product.type)}
                          <CardTitle className="text-lg">{product.name}</CardTitle>
                        </div>
                        <Badge className={getRiskColor(product.riskLevel)}>
                          {product.riskLevel} risk
                        </Badge>
                      </div>
                      <CardDescription className="text-sm">
                        {product.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Performance (6M)</p>
                        <div className="h-16 flex items-end space-x-1">
                          {product.performance.map((value, index) => (
                            <div
                              key={index}
                              className="flex-1 bg-primary/20 rounded-sm"
                              style={{ height: `${(value / Math.max(...product.performance)) * 100}%` }}
                            />
                          ))}
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Expected Returns</span>
                          <span className="text-sm font-medium">{product.returns}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">YTD Return</span>
                          <span className={`text-sm font-medium ${product.ytdReturn >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {product.ytdReturn >= 0 ? '+' : ''}{product.ytdReturn}%
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Minimum</span>
                          <span className="text-sm font-medium">{formatCurrency(product.minimumAmount)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Duration</span>
                          <span className="text-sm font-medium">{product.duration}</span>
                        </div>
                      </div>

                      <Button 
                        className="w-full" 
                        onClick={() => handleInvest(product)}
                      >
                        <DollarSign className="h-4 w-4 mr-2" />
                        Invest Now
                      </Button>
                    </CardContent>
                  </Card>
                ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>

      {/* Risk Disclaimer */}
      <Card className="border-orange-200 bg-orange-50/50">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
            <div className="space-y-2">
              <p className="font-medium text-orange-800">Investment Risk Disclaimer</p>
              <p className="text-sm text-orange-700">
                All investments carry risk and past performance does not guarantee future results. 
                Please read the fund prospectus and consider your risk tolerance before investing. 
                Consult with a financial advisor if needed.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};