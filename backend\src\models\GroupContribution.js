const mongoose = require('mongoose');

const groupContributionSchema = new mongoose.Schema({
  groupId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'GroupSavingsPlan',
    required: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  paymentReference: {
    type: String,
    required: true,
    unique: true
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'cancelled'],
    default: 'pending'
  },
  paymentMethod: {
    type: String,
    enum: ['paystack', 'bank_transfer', 'wallet'],
    default: 'paystack'
  },
  contributionDate: {
    type: Date,
    default: Date.now
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  notes: {
    type: String,
    maxlength: 500
  }
}, {
  timestamps: true
});

// Indexes for better query performance
groupContributionSchema.index({ groupId: 1, userId: 1 });
groupContributionSchema.index({ paymentReference: 1 });
groupContributionSchema.index({ status: 1 });
groupContributionSchema.index({ contributionDate: -1 });

// Virtual for formatted amount
groupContributionSchema.virtual('formattedAmount').get(function() {
  return new Intl.NumberFormat('en-NG', {
    style: 'currency',
    currency: 'NGN'
  }).format(this.amount);
});

// Method to mark contribution as completed
groupContributionSchema.methods.markCompleted = function() {
  this.status = 'completed';
  this.contributionDate = new Date();
  return this.save();
};

// Method to mark contribution as failed
groupContributionSchema.methods.markFailed = function(reason) {
  this.status = 'failed';
  this.metadata.failureReason = reason;
  return this.save();
};

// Static method to find group contributions
groupContributionSchema.statics.findByGroup = function(groupId, options = {}) {
  const { status, userId, limit = 20, skip = 0 } = options;
  
  const query = { groupId };
  if (status) query.status = status;
  if (userId) query.userId = userId;
  
  return this.find(query)
    .sort({ contributionDate: -1 })
    .limit(limit)
    .skip(skip)
    .populate('userId', 'firstName lastName email')
    .populate('groupId', 'name targetAmount');
};

// Static method to get user contributions for a group
groupContributionSchema.statics.getUserContributions = function(userId, groupId) {
  return this.find({ userId, groupId, status: 'completed' })
    .sort({ contributionDate: -1 })
    .populate('groupId', 'name targetAmount');
};

// Static method to get contribution summary
groupContributionSchema.statics.getContributionSummary = function(groupId) {
  return this.aggregate([
    {
      $match: {
        groupId: new mongoose.Types.ObjectId(groupId),
        status: 'completed'
      }
    },
    {
      $group: {
        _id: '$userId',
        totalContributed: { $sum: '$amount' },
        contributionCount: { $sum: 1 },
        lastContribution: { $max: '$contributionDate' }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'user'
      }
    },
    {
      $unwind: '$user'
    },
    {
      $project: {
        userId: '$_id',
        userName: { $concat: ['$user.firstName', ' ', '$user.lastName'] },
        userEmail: '$user.email',
        totalContributed: 1,
        contributionCount: 1,
        lastContribution: 1
      }
    },
    {
      $sort: { totalContributed: -1 }
    }
  ]);
};

module.exports = mongoose.model('GroupContribution', groupContributionSchema);
