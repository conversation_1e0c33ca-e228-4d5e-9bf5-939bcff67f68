import { AdsBanner } from '@/components/ui/ads-banner';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Edit, Eye, Plus, Trash2, Upload } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface Ad {
  id: string;
  image: string;
  title: string;
  description: string;
  link: string;
}

export function AdsBannerManagement() {
  const [ads, setAds] = useState<Ad[]>([]);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingAd, setEditingAd] = useState<Ad | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    image: '',
    title: '',
    description: '',
    link: ''
  });

  useEffect(() => {
    loadAds();
  }, []);

  const loadAds = async () => {
    const savedAds = localStorage.getItem('admin_ads');
    if (savedAds) {
      try {
        setAds(JSON.parse(savedAds));
      } catch (error) {
        console.error('Error loading ads:', error);
      }
    } else {
      // Fetch ads from API if no local storage
      try {
        const response = await fetch('/api/v1/admin/ads', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          setAds(data.data || []);
          localStorage.setItem('admin_ads', JSON.stringify(data.data || []));
        } else {
          setAds([]);
        }
      } catch (error) {
        console.error('Error fetching ads:', error);
        setAds([]);
      }
    }
  };

  const saveAds = (newAds: Ad[]) => {
    localStorage.setItem('admin_ads', JSON.stringify(newAds));
    setAds(newAds);
  };

  const handleAddAd = () => {
    setEditingAd(null);
    setFormData({ image: '', title: '', description: '', link: '' });
    setShowEditDialog(true);
  };

  const handleEditAd = (ad: Ad) => {
    setEditingAd(ad);
    setFormData(ad);
    setShowEditDialog(true);
  };

  const handleDeleteAd = (id: string) => {
    const newAds = ads.filter(ad => ad.id !== id);
    saveAds(newAds);
    toast.success('Ad deleted successfully');
  };

  const handleSaveAd = () => {
    if (!formData.title || !formData.description) {
      toast.error('Please fill in all required fields');
      return;
    }

    let newAds;
    if (editingAd) {
      // Update existing ad
      newAds = ads.map(ad => 
        ad.id === editingAd.id 
          ? { ...ad, ...formData }
          : ad
      );
      toast.success('Ad updated successfully');
    } else {
      // Add new ad
      const newAd = {
        id: Date.now().toString(),
        ...formData
      };
      newAds = [...ads, newAd];
      toast.success('Ad added successfully');
    }

    saveAds(newAds);
    setShowEditDialog(false);
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setFormData({ ...formData, image: e.target?.result as string });
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Ads Banner Management</h2>
          <p className="text-muted-foreground">Manage promotional banners displayed to users</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setShowPreview(true)} variant="outline">
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>
          <Button onClick={handleAddAd}>
            <Plus className="h-4 w-4 mr-2" />
            Add New Ad
          </Button>
        </div>
      </div>

      {/* Preview Banner */}
      {showPreview && (
        <Card>
          <CardHeader>
            <CardTitle>Live Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <AdsBanner />
          </CardContent>
        </Card>
      )}

      {/* Ads List */}
      <div className="grid gap-4">
        {ads.map((ad) => (
          <Card key={ad.id} className="relative">
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 rounded-none overflow-hidden bg-gray-100 border-2 border-orange-300">
                  {ad.image ? (
                    <img src={ad.image} alt={ad.title} className="w-full h-full object-contain" />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                      <Upload className="h-6 w-6" />
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="font-bold text-lg">{ad.title}</h3>
                  <p className="text-sm text-muted-foreground">{ad.description}</p>
                  <p className="text-xs text-blue-600">{ad.link}</p>
                </div>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleEditAd(ad)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleDeleteAd(ad.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Edit Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{editingAd ? 'Edit Ad' : 'Add New Ad'}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="image">Image</Label>
              <div className="mt-2">
                <Input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="mb-2"
                />
                {formData.image && (
                  <div className="w-full h-32 rounded-none overflow-hidden bg-gray-100 border-2 border-orange-300">
                    <img src={formData.image} alt="Preview" className="w-full h-full object-contain" />
                  </div>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                placeholder="Enter ad title"
              />
            </div>

            <div>
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Enter ad description"
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="link">Link</Label>
              <Input
                id="link"
                value={formData.link}
                onChange={(e) => setFormData({ ...formData, link: e.target.value })}
                placeholder="/savings, /premium, etc."
              />
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowEditDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveAd}>
                {editingAd ? 'Update' : 'Add'} Ad
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
