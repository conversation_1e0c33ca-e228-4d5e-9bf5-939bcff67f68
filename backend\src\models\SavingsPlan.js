const mongoose = require('mongoose');

const savingsPlanSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  planType: {
    type: String,
    enum: ['flex', 'fixed', 'safelock', 'target', 'autosave', 'roundup'],
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  targetAmount: {
    type: Number,
    required: true,
    min: 0
  },
  currentAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  frequency: {
    type: String,
    enum: ['daily', 'weekly', 'monthly', 'one-time'],
    default: 'monthly'
  },
  contributionAmount: {
    type: Number,
    required: true,
    min: 0
  },
  interestRate: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  startDate: {
    type: Date,
    default: Date.now
  },
  endDate: {
    type: Date,
    required: true
  },
  maturityDate: {
    type: Date
  },
  status: {
    type: String,
    enum: ['active', 'paused', 'completed', 'cancelled', 'matured'],
    default: 'active'
  },
  autoDebit: {
    enabled: { type: Boolean, default: false },
    accountId: String,
    nextDebitDate: Date
  },
  lockSettings: {
    isLocked: { type: Boolean, default: false },
    lockUntil: Date,
    penaltyRate: { type: Number, default: 0 },
    allowPartialWithdrawal: { type: Boolean, default: true }
  },
  transactions: [{
    type: {
      type: String,
      enum: ['deposit', 'withdrawal', 'interest', 'penalty', 'fee'],
      required: true
    },
    amount: {
      type: Number,
      required: true
    },
    balance: {
      type: Number,
      required: true
    },
    description: String,
    reference: String,
    date: {
      type: Date,
      default: Date.now
    },
    metadata: {
      type: Map,
      of: mongoose.Schema.Types.Mixed
    }
  }],
  analytics: {
    totalDeposits: { type: Number, default: 0 },
    totalWithdrawals: { type: Number, default: 0 },
    totalInterest: { type: Number, default: 0 },
    averageMonthlyContribution: { type: Number, default: 0 },
    streakDays: { type: Number, default: 0 },
    lastContributionDate: Date
  },
  notifications: {
    contributionReminder: { type: Boolean, default: true },
    goalAchievement: { type: Boolean, default: true },
    interestPayment: { type: Boolean, default: true },
    maturityAlert: { type: Boolean, default: true }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for progress percentage
savingsPlanSchema.virtual('progressPercentage').get(function() {
  return this.targetAmount > 0 ? (this.currentAmount / this.targetAmount) * 100 : 0;
});

// Virtual for remaining amount
savingsPlanSchema.virtual('remainingAmount').get(function() {
  return Math.max(0, this.targetAmount - this.currentAmount);
});

// Virtual for days remaining
savingsPlanSchema.virtual('daysRemaining').get(function() {
  const now = new Date();
  const end = new Date(this.endDate);
  const timeDiff = end - now;
  return Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)));
});

// Indexes
savingsPlanSchema.index({ userId: 1, status: 1 });
savingsPlanSchema.index({ planType: 1, status: 1 });
savingsPlanSchema.index({ endDate: 1, status: 1 });
savingsPlanSchema.index({ 'autoDebit.nextDebitDate': 1, 'autoDebit.enabled': 1 });

// Add transaction method
savingsPlanSchema.methods.addTransaction = function(transactionData) {
  const newBalance = this.currentAmount + (transactionData.amount || 0);
  
  const transaction = {
    ...transactionData,
    balance: newBalance,
    date: new Date(),
    reference: transactionData.reference || `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  };
  
  this.transactions.push(transaction);
  this.currentAmount = newBalance;
  
  // Update analytics
  if (transactionData.type === 'deposit') {
    this.analytics.totalDeposits += transactionData.amount;
    this.analytics.lastContributionDate = new Date();
  } else if (transactionData.type === 'withdrawal') {
    this.analytics.totalWithdrawals += transactionData.amount;
  } else if (transactionData.type === 'interest') {
    this.analytics.totalInterest += transactionData.amount;
  }
  
  return this.save();
};

// Calculate interest method
savingsPlanSchema.methods.calculateInterest = function() {
  const dailyRate = this.interestRate / 365 / 100;
  const daysSinceLastInterest = 1; // Assuming daily calculation
  return this.currentAmount * dailyRate * daysSinceLastInterest;
};

// Check if plan should mature
savingsPlanSchema.methods.checkMaturity = function() {
  const now = new Date();
  const shouldMature = this.endDate <= now || this.currentAmount >= this.targetAmount;
  
  if (shouldMature && this.status === 'active') {
    this.status = 'matured';
    this.maturityDate = now;
  }
  
  return shouldMature;
};

// Get plans due for auto-debit
savingsPlanSchema.statics.getDueForAutoDebit = function() {
  const now = new Date();
  return this.find({
    'autoDebit.enabled': true,
    'autoDebit.nextDebitDate': { $lte: now },
    status: 'active'
  });
};

module.exports = mongoose.model('SavingsPlan', savingsPlanSchema);