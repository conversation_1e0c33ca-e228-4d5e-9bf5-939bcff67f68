const express = require('express');
const router = express.Router();
const { auth, admin } = require('../middleware/auth');
const TransactionService = require('../services/TransactionService');
const Transaction = require('../models/Transaction');
const User = require('../models/User');
const { body, query, validationResult } = require('express-validator');

// Validation middleware
const validateTransaction = [
  body('amount').isFloat({ min: 0.01 }).withMessage('Amount must be a positive number'),
  body('type').isIn([
    'deposit', 'withdrawal', 'transfer', 'interest', 'penalty', 
    'bonus', 'refund', 'bill_payment', 'loan_disbursement', 
    'loan_repayment', 'purchase', 'investment'
  ]).withMessage('Invalid transaction type'),
  body('description').isLength({ min: 3, max: 255 }).withMessage('Description must be 3-255 characters'),
  body('category').optional().isIn([
    'savings', 'investment', 'bill', 'transfer', 'loan', 'interest', 
    'penalty', 'bonus', 'food', 'transport', 'utilities', 'entertainment', 
    'shopping', 'healthcare', 'education', 'other'
  ]).withMessage('Invalid category')
];

const validateTransactionQuery = [
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be 1-100'),
  query('skip').optional().isInt({ min: 0 }).withMessage('Skip must be non-negative'),
  query('type').optional().isIn([
    'deposit', 'withdrawal', 'transfer', 'interest', 'penalty', 
    'bonus', 'refund', 'bill_payment', 'loan_disbursement', 
    'loan_repayment', 'purchase', 'investment'
  ]).withMessage('Invalid transaction type'),
  query('status').optional().isIn(['pending', 'completed', 'failed', 'cancelled', 'reversed']).withMessage('Invalid status')
];

// Create a new transaction
router.post('/', auth, validateTransaction, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const transactionData = {
      userId: req.user.id,
      ...req.body,
      originalAmount: req.body.amount
    };

    const result = await TransactionService.createTransaction(transactionData);

    res.status(201).json({
      success: true,
      message: 'Transaction created successfully',
      data: result.transaction
    });
  } catch (error) {
    console.error('Transaction creation error:', error);
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

// Get user transactions with filters
router.get('/', auth, validateTransactionQuery, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const filters = {
      ...req.query,
      userId: req.user.id
    };

    const result = await TransactionService.getTransactionHistory(req.user.id, filters);

    res.json(result);
  } catch (error) {
    console.error('Error fetching transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch transactions'
    });
  }
});

// Get transaction by ID
router.get('/:id', auth, async (req, res) => {
  try {
    const transaction = await Transaction.findOne({
      _id: req.params.id,
      userId: req.user.id
    }).populate('relatedId');

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
    }

    res.json({
      success: true,
      data: transaction
    });
  } catch (error) {
    console.error('Error fetching transaction:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch transaction'
    });
  }
});

// Reverse a transaction (admin only)
router.post('/:id/reverse', auth, admin, async (req, res) => {
  try {
    const { reason } = req.body;

    if (!reason || reason.trim().length < 10) {
      return res.status(400).json({
        success: false,
        message: 'Reversal reason must be at least 10 characters'
      });
    }

    const result = await TransactionService.reverseTransaction(req.params.id, reason);

    res.json({
      success: true,
      message: 'Transaction reversed successfully',
      data: result
    });
  } catch (error) {
    console.error('Transaction reversal error:', error);
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

// Batch create transactions (admin only)
router.post('/batch', auth, admin, async (req, res) => {
  try {
    const { transactions } = req.body;

    if (!Array.isArray(transactions) || transactions.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Transactions array is required'
      });
    }

    if (transactions.length > 100) {
      return res.status(400).json({
        success: false,
        message: 'Maximum 100 transactions per batch'
      });
    }

    const result = await TransactionService.batchProcessTransactions(transactions);

    res.json({
      success: true,
      message: 'Batch processing completed',
      data: result
    });
  } catch (error) {
    console.error('Batch transaction error:', error);
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

// Get transaction statistics
router.get('/stats/summary', auth, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    const filters = {};
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;

    const result = await TransactionService.getTransactionHistory(req.user.id, filters);

    res.json({
      success: true,
      data: {
        summary: result.data.summary,
        totalTransactions: result.data.pagination.total
      }
    });
  } catch (error) {
    console.error('Error fetching transaction stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch transaction statistics'
    });
  }
});

// Get monthly transaction report
router.get('/reports/monthly', auth, async (req, res) => {
  try {
    const { year = new Date().getFullYear(), month = new Date().getMonth() + 1 } = req.query;
    
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0, 23, 59, 59);

    const pipeline = [
      {
        $match: {
          userId: req.user._id,
          createdAt: { $gte: startDate, $lte: endDate },
          status: 'completed'
        }
      },
      {
        $group: {
          _id: {
            day: { $dayOfMonth: '$createdAt' },
            type: '$type'
          },
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      },
      {
        $group: {
          _id: '$_id.day',
          transactions: {
            $push: {
              type: '$_id.type',
              count: '$count',
              totalAmount: '$totalAmount'
            }
          },
          dailyTotal: { $sum: '$totalAmount' }
        }
      },
      { $sort: { '_id': 1 } }
    ];

    const monthlyData = await Transaction.aggregate(pipeline);

    res.json({
      success: true,
      data: {
        year: parseInt(year),
        month: parseInt(month),
        report: monthlyData
      }
    });
  } catch (error) {
    console.error('Error generating monthly report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate monthly report'
    });
  }
});

// Admin: Get all transactions with advanced filters
router.get('/admin/all', auth, admin, async (req, res) => {
  try {
    const {
      userId,
      type,
      status,
      startDate,
      endDate,
      minAmount,
      maxAmount,
      limit = 50,
      skip = 0
    } = req.query;

    const query = {};
    
    if (userId) query.userId = userId;
    if (type) query.type = type;
    if (status) query.status = status;
    
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }
    
    if (minAmount || maxAmount) {
      query.amount = {};
      if (minAmount) query.amount.$gte = parseFloat(minAmount);
      if (maxAmount) query.amount.$lte = parseFloat(maxAmount);
    }

    const transactions = await Transaction.find(query)
      .populate('userId', 'firstName lastName email')
      .populate('relatedId')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip(parseInt(skip));

    const total = await Transaction.countDocuments(query);

    res.json({
      success: true,
      data: {
        transactions,
        pagination: {
          total,
          limit: parseInt(limit),
          skip: parseInt(skip),
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching admin transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch transactions'
    });
  }
});

// Export transaction data (admin only)
router.get('/admin/export', auth, admin, async (req, res) => {
  try {
    const { format = 'json', ...filters } = req.query;
    
    const query = {};
    if (filters.userId) query.userId = filters.userId;
    if (filters.type) query.type = filters.type;
    if (filters.status) query.status = filters.status;
    
    if (filters.startDate || filters.endDate) {
      query.createdAt = {};
      if (filters.startDate) query.createdAt.$gte = new Date(filters.startDate);
      if (filters.endDate) query.createdAt.$lte = new Date(filters.endDate);
    }

    const transactions = await Transaction.find(query)
      .populate('userId', 'firstName lastName email')
      .lean()
      .limit(10000); // Limit for performance

    if (format === 'csv') {
      // Convert to CSV format
      const csv = transactions.map(txn => ({
        id: txn._id,
        userId: txn.userId._id,
        userEmail: txn.userId.email,
        type: txn.type,
        amount: txn.amount,
        description: txn.description,
        status: txn.status,
        reference: txn.reference,
        createdAt: txn.createdAt
      }));

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=transactions.csv');
      
      // Simple CSV conversion (in production, use a proper CSV library)
      const csvContent = [
        Object.keys(csv[0]).join(','),
        ...csv.map(row => Object.values(row).join(','))
      ].join('\n');
      
      res.send(csvContent);
    } else {
      res.json({
        success: true,
        data: transactions,
        count: transactions.length
      });
    }
  } catch (error) {
    console.error('Error exporting transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export transactions'
    });
  }
});

module.exports = router;
