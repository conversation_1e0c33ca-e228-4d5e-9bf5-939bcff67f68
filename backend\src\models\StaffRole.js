const mongoose = require('mongoose');

const staffRoleSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  module: {
    type: String,
    required: true,
    enum: ['bill_payment', 'user_management', 'savings_plans', 'withdrawals', 'kyc', 'notifications', 'analytics', 'all']
  },
  permissions: [{
    action: {
      type: String,
      required: true,
      enum: ['view', 'create', 'edit', 'delete', 'approve', 'reject', 'export', 'import', 'assign']
    },
    resource: {
      type: String,
      required: true,
      enum: ['users', 'staff', 'roles', 'bills', 'providers', 'payments', 'withdrawals', 'savings', 'kyc', 'notifications', 'reports']
    },
    conditions: {
      type: Map,
      of: String,
      default: {}
    }
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  priority: {
    type: Number,
    default: 0,
    min: 0,
    max: 10
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Index for efficient queries
staffRoleSchema.index({ module: 1, isActive: 1 });
staffRoleSchema.index({ name: 1 });

module.exports = mongoose.model('StaffRole', staffRoleSchema);