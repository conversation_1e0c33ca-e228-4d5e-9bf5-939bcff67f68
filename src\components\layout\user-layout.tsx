
import * as React from "react";
import { useState, useEffect } from "react";
import { Outlet, useLocation } from "react-router-dom";
import { Sidebar } from "./sidebar";
import { UserNav } from "../user-nav";
import { Menu, X } from "lucide-react";
import { Button } from "../ui/button";
import { Switch } from "../ui/switch";
import { Label } from "../ui/label";
import { useIsMobile } from "@/hooks/use-mobile";
import { ThemeToggle } from "../ui/theme-toggle";
import { MobileNav } from "../ui/mobile-nav";
import { CustomerSupportWidget } from "../support/CustomerSupportWidget";

interface UserLayoutProps {
  isAdmin?: boolean;
}

export function UserLayout({ isAdmin = false }: UserLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const isMobile = useIsMobile();
  const [compactMode, setCompactMode] = useState(false);
  const [supportWidgetOpen, setSupportWidgetOpen] = useState(false);
  const location = useLocation();

  // Close sidebar by default on mobile
  useEffect(() => {
    setSidebarOpen(!isMobile);
  }, [isMobile]);

  // Close sidebar when route changes on mobile
  useEffect(() => {
    if (isMobile) {
      setSidebarOpen(false);
    }
  }, [location.pathname, isMobile]);

  return (
    <div className="flex min-h-screen w-full bg-background text-foreground">
      {/* Enhanced Sidebar - Hidden on mobile, show desktop nav */}
      {!isMobile && (
        <div className="relative">
          <Sidebar 
            isAdmin={isAdmin} 
            className={compactMode ? "w-16" : ""} 
            collapsed={compactMode} 
          />
        </div>
      )}
      
      {/* Mobile Sidebar Overlay */}
      {isMobile && sidebarOpen && (
        <div className="fixed inset-y-0 left-0 z-50">
          <Sidebar 
            isAdmin={isAdmin} 
            className="w-64" 
            collapsed={false} 
          />
        </div>
      )}
      
      {/* Enhanced Mobile Overlay */}
      {sidebarOpen && isMobile && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 md:hidden backdrop-blur-sm" 
          onClick={() => setSidebarOpen(false)}
        />
      )}
      
      {/* Enhanced Main Content */}
      <div className="flex-1 flex flex-col min-w-0 w-full">
        {/* Enhanced Header */}
        <header className={`h-14 sm:h-16 lg:h-18 border-b bg-card/95 backdrop-blur-sm flex items-center justify-between px-4 sm:px-6 lg:px-8 sticky top-0 z-30 shadow-sm transition-all duration-300 ${isMobile ? 'transform translate-y-0' : ''}`}>
          <div className="flex items-center gap-3">
            <Button 
              variant="ghost" 
              size="icon" 
              className="md:hidden hover:bg-primary/10 rounded-xl"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
            <div className="flex items-center gap-3">
              <div className="h-8 w-8 sm:h-10 sm:w-10 lg:h-12 lg:w-12 rounded-lg overflow-hidden bg-white shadow-lg">
                <img 
                  src="/lovable-uploads/371e0f57-a280-42c0-8e1b-50aee7ebddc6.png" 
                  alt="Better Interest Logo" 
                  className="h-full w-full object-contain p-0.5"
                />
              </div>
              <div className="hidden sm:flex flex-col">
                <h1 className="text-sm sm:text-base lg:text-lg font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent font-bebas tracking-wide leading-tight">
                  {isAdmin ? "Admin Dashboard" : "Better Interest"}
                </h1>
                <span className="text-xs text-muted-foreground font-medium">
                  {isAdmin ? "Management Portal" : "Financial Growth Platform"}
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2 sm:gap-3">
            <ThemeToggle />
            <div className="hidden lg:flex items-center space-x-2">
              <Switch
                id="compact-mode"
                checked={compactMode}
                onCheckedChange={setCompactMode}
              />
              <Label htmlFor="compact-mode" className="text-sm">Compact</Label>
            </div>
            <UserNav isAdmin={isAdmin} />
          </div>
        </header>
        
        {/* Enhanced Main Content Area */}
        <main className={`flex-1 overflow-auto bg-gradient-to-br from-background via-muted/10 to-primary/5 transition-all duration-300 ${isMobile ? 'pb-16' : ''}`}>
          <div className={`min-h-full p-4 sm:p-6 lg:p-8 xl:p-10 transition-all duration-300 ${isMobile ? 'mobile-page-transition' : ''}`}>
            <Outlet />
          </div>
        </main>
        
        {/* Enhanced Footer */}
        <footer className="py-3 sm:py-4 px-4 sm:px-6 lg:px-8 text-center text-xs sm:text-sm text-muted-foreground border-t bg-card/50 backdrop-blur-sm">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-2">
            <span>© {new Date().getFullYear()} Better Interest. All rights reserved.</span>
            <div className="flex gap-4">
              <a href="/legal" className="hover:text-primary transition-colors">Legal</a>
              <a href="/legal" className="hover:text-primary transition-colors">Privacy</a>
              <a href="/legal" className="hover:text-primary transition-colors">Terms</a>
            </div>
          </div>
        </footer>
      </div>
      
      {/* Mobile Navigation */}
      {isMobile && <MobileNav isAdmin={isAdmin} />}
      
      {/* Customer Support Widget */}
      <CustomerSupportWidget 
        isOpen={supportWidgetOpen}
        onToggle={() => setSupportWidgetOpen(!supportWidgetOpen)}
        provider="custom"
      />
    </div>
  );
}

export default UserLayout;
