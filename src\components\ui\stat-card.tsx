
import * as React from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "./card"
import { cn } from "@/lib/utils"

interface StatCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string
  value: string | number
  description?: string
  icon?: React.ReactNode
  trend?: {
    value: number
    isPositive: boolean
  }
  className?: string
}

export function StatCard({
  title,
  value,
  description,
  icon,
  trend,
  className,
  ...props
}: StatCardProps) {
  return (
    <div className={cn(
      "overflow-hidden group transform transition-all duration-500 rounded-none bg-background w-full",
      "shadow-[inset_8px_8px_16px_rgba(0,0,0,0.1),inset_-8px_-8px_16px_rgba(255,255,255,0.1)]",
      "dark:shadow-[inset_8px_8px_16px_rgba(0,0,0,0.3),inset_-8px_-8px_16px_rg<PERSON>(255,255,255,0.02)]",
      "hover:shadow-[inset_12px_12px_24px_rgba(0,0,0,0.15),inset_-12px_-12px_24px_rgba(255,255,255,0.15)]",
      "dark:hover:shadow-[inset_12px_12px_24px_rgba(0,0,0,0.4),inset_-12px_-12px_24px_rgba(255,255,255,0.03)]",
      "active:scale-[0.98] border border-white/10 dark:border-white/5",
      className
    )} {...props}>
      <div className="flex flex-row items-center justify-between pb-2 p-3 sm:p-4">
        <h3 className="text-xs sm:text-sm font-medium text-muted-foreground group-hover:text-primary transition-colors duration-300">
          {title}
        </h3>
        {icon && (
          <div className="h-6 w-6 sm:h-8 sm:w-8 rounded-none bg-background shadow-[inset_2px_2px_4px_rgba(0,0,0,0.1),inset_-2px_-2px_4px_rgba(255,255,255,0.1)] p-1 sm:p-1.5 text-primary group-hover:shadow-[inset_4px_4px_8px_rgba(0,0,0,0.15),inset_-4px_-4px_8px_rgba(255,255,255,0.15)] transition-all duration-300 transform group-hover:scale-110 flex-shrink-0">
            {icon}
          </div>
        )}
      </div>
      <div className="px-3 sm:px-4 pb-3 sm:pb-4">
        <div className="text-xl sm:text-2xl font-bold transition-all duration-500 group-hover:text-primary group-hover:translate-x-1 break-all">
          {value}
        </div>
        {description && <p className="text-xs sm:text-sm text-muted-foreground group-hover:text-muted-foreground/90 transition-colors duration-300">{description}</p>}
        {trend && (
          <div className={cn(
            "mt-2 flex flex-wrap items-center text-xs font-medium transition-all duration-300 transform group-hover:translate-x-1",
            trend.isPositive ? "text-green-500" : "text-red-500"
          )}>
            <span className="transition-transform duration-300 group-hover:scale-125">{trend.isPositive ? "↑" : "↓"}</span>
            <span className="ml-1">{Math.abs(trend.value)}%</span>
            <span className="ml-1 text-muted-foreground transition-colors duration-300 group-hover:text-muted-foreground/80">from last period</span>
          </div>
        )}
      </div>
    </div>
  )
}
