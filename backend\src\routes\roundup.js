const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const RoundUpSaving = require('../models/RoundUpSaving');
const Transaction = require('../models/Transaction');
const User = require('../models/User');

// Get user's round-up settings and stats
router.get('/settings', auth, async (req, res) => {
  try {
    let roundUpSaving = await RoundUpSaving.findOne({ userId: req.user.id });
    
    if (!roundUpSaving) {
      // Create default round-up settings for new user
      roundUpSaving = new RoundUpSaving({
        userId: req.user.id,
        isEnabled: true,
        multiplier: 1,
        goalAmount: 50000
      });
      await roundUpSaving.save();
    }
    
    res.json({ success: true, data: roundUpSaving });
  } catch (error) {
    console.error('Error fetching round-up settings:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch round-up settings' });
  }
});

// Update round-up settings
router.put('/settings', auth, async (req, res) => {
  try {
    const { isEnabled, multiplier, goalAmount, autoInvest, investmentThreshold, preferredInvestmentType } = req.body;
    
    const updateData = {};
    if (typeof isEnabled === 'boolean') updateData.isEnabled = isEnabled;
    if (multiplier && multiplier >= 1 && multiplier <= 5) updateData.multiplier = multiplier;
    if (goalAmount && goalAmount >= 1000) updateData.goalAmount = goalAmount;
    
    if (autoInvest !== undefined) updateData['settings.autoInvest'] = autoInvest;
    if (investmentThreshold) updateData['settings.investmentThreshold'] = investmentThreshold;
    if (preferredInvestmentType) updateData['settings.preferredInvestmentType'] = preferredInvestmentType;
    
    const roundUpSaving = await RoundUpSaving.findOneAndUpdate(
      { userId: req.user.id },
      updateData,
      { new: true, upsert: true }
    );
    
    // Update user's round-up settings
    await User.findByIdAndUpdate(req.user.id, {
      'roundUpSettings.enabled': updateData.isEnabled,
      'roundUpSettings.multiplier': updateData.multiplier,
      'roundUpSettings.goalAmount': updateData.goalAmount
    });
    
    res.json({ 
      success: true, 
      data: roundUpSaving,
      message: 'Round-up settings updated successfully' 
    });
  } catch (error) {
    console.error('Error updating round-up settings:', error);
    res.status(500).json({ success: false, message: 'Failed to update round-up settings' });
  }
});

// Process round-up for a transaction
router.post('/process', auth, async (req, res) => {
  try {
    const { transactionId, originalAmount, description } = req.body;
    
    if (!transactionId || !originalAmount) {
      return res.status(400).json({ 
        success: false, 
        message: 'Transaction ID and original amount are required' 
      });
    }
    
    const roundUpSaving = await RoundUpSaving.findOne({ userId: req.user.id });
    
    if (!roundUpSaving || !roundUpSaving.isEnabled) {
      return res.status(400).json({ 
        success: false, 
        message: 'Round-up is not enabled for this user' 
      });
    }
    
    // Calculate round-up amount
    const roundedUp = Math.ceil(originalAmount / 100) * 100;
    const roundUpAmount = (roundedUp - originalAmount) * roundUpSaving.multiplier;
    
    if (roundUpAmount <= 0) {
      return res.status(400).json({ 
        success: false, 
        message: 'No round-up amount calculated' 
      });
    }
    
    // Create transaction record
    const transaction = new Transaction({
      userId: req.user.id,
      type: 'purchase',
      amount: originalAmount,
      roundUpAmount: roundUpAmount,
      originalAmount: originalAmount,
      description: description || 'Round-up transaction',
      reference: `RU_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      status: 'completed',
      metadata: {
        roundUpEnabled: true,
        roundUpMultiplier: roundUpSaving.multiplier
      }
    });
    
    await transaction.save();
    
    // Add round-up to savings
    await roundUpSaving.addRoundUp({
      transactionId: transaction._id,
      originalAmount: originalAmount,
      roundUpAmount: roundUpAmount,
      description: description || 'Round-up saving',
      date: new Date()
    });
    
    res.json({ 
      success: true, 
      data: {
        transaction,
        roundUpAmount,
        totalSaved: roundUpSaving.totalSaved,
        progressPercentage: roundUpSaving.progressPercentage
      },
      message: 'Round-up processed successfully' 
    });
  } catch (error) {
    console.error('Error processing round-up:', error);
    res.status(500).json({ success: false, message: 'Failed to process round-up' });
  }
});

// Get round-up transaction history
router.get('/history', auth, async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    
    const roundUpSaving = await RoundUpSaving.findOne({ userId: req.user.id });
    
    if (!roundUpSaving) {
      return res.json({ 
        success: true, 
        data: { transactions: [], total: 0 } 
      });
    }
    
    // Get paginated round-up transactions
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    
    const transactions = roundUpSaving.roundUpTransactions
      .sort((a, b) => b.date - a.date)
      .slice(startIndex, endIndex);
    
    res.json({
      success: true,
      data: {
        transactions,
        total: roundUpSaving.roundUpTransactions.length,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: roundUpSaving.roundUpTransactions.length,
          pages: Math.ceil(roundUpSaving.roundUpTransactions.length / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching round-up history:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch round-up history' });
  }
});

// Get round-up statistics
router.get('/stats', auth, async (req, res) => {
  try {
    const roundUpSaving = await RoundUpSaving.findOne({ userId: req.user.id });
    
    if (!roundUpSaving) {
      return res.json({ 
        success: true, 
        data: {
          totalSaved: 0,
          totalTransactions: 0,
          averageRoundUp: 0,
          progressPercentage: 0,
          statistics: {
            thisMonth: { saved: 0, transactions: 0 },
            thisYear: { saved: 0, transactions: 0 },
            allTime: { saved: 0, transactions: 0 }
          }
        }
      });
    }
    
    res.json({ success: true, data: roundUpSaving });
  } catch (error) {
    console.error('Error fetching round-up stats:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch round-up statistics' });
  }
});

// Simulate round-up for amount (without saving)
router.post('/simulate', auth, async (req, res) => {
  try {
    const { amount } = req.body;
    
    if (!amount || amount <= 0) {
      return res.status(400).json({ 
        success: false, 
        message: 'Valid amount is required' 
      });
    }
    
    const user = await User.findById(req.user.id);
    const multiplier = user.roundUpSettings?.multiplier || 1;
    
    const roundedUp = Math.ceil(amount / 100) * 100;
    const roundUpAmount = (roundedUp - amount) * multiplier;
    
    res.json({
      success: true,
      data: {
        originalAmount: amount,
        roundedAmount: roundedUp,
        roundUpAmount: roundUpAmount,
        multiplier: multiplier
      }
    });
  } catch (error) {
    console.error('Error simulating round-up:', error);
    res.status(500).json({ success: false, message: 'Failed to simulate round-up' });
  }
});

// Admin routes
// Get all users' round-up statistics (admin only)
router.get('/admin/stats', [auth, async (req, res, next) => {
  const user = await User.findById(req.user.id);
  if (!user || !['admin', 'staff'].includes(user.role)) {
    return res.status(403).json({ success: false, message: 'Access denied' });
  }
  next();
}], async (req, res) => {
  try {
    const stats = await RoundUpSaving.aggregate([
      {
        $group: {
          _id: null,
          totalUsers: { $sum: 1 },
          activeUsers: { 
            $sum: { $cond: [{ $eq: ['$isEnabled', true] }, 1, 0] } 
          },
          totalSaved: { $sum: '$totalSaved' },
          totalTransactions: { $sum: '$totalTransactions' },
          averageGoalAmount: { $avg: '$goalAmount' }
        }
      }
    ]);
    
    const monthlyStats = await RoundUpSaving.aggregate([
      {
        $group: {
          _id: null,
          monthlyTotal: { $sum: '$statistics.thisMonth.saved' },
          monthlyTransactions: { $sum: '$statistics.thisMonth.transactions' }
        }
      }
    ]);
    
    res.json({
      success: true,
      data: {
        overall: stats[0] || {},
        monthly: monthlyStats[0] || {}
      }
    });
  } catch (error) {
    console.error('Error fetching admin round-up stats:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch round-up statistics' });
  }
});

module.exports = router;