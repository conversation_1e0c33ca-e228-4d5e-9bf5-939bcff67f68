[build]
  # Build command
  command = "npm run build"
  
  # Directory to publish
  publish = "dist"
  
  # Functions directory
  functions = "netlify/functions"

[build.environment]
  # Node version
  NODE_VERSION = "18"
  
  # NPM version
  NPM_VERSION = "9"

# Production context
[context.production]
  command = "npm run build"
  
[context.production.environment]
  NODE_ENV = "production"
  VITE_API_URL = "https://api.kojaonline.store/api/v1"
  VITE_DEMO_MODE = "false"

# Deploy preview context
[context.deploy-preview]
  command = "npm run build:dev"
  
[context.deploy-preview.environment]
  NODE_ENV = "development"
  VITE_API_URL = "https://api.kojaonline.store/api/v1"
  VITE_DEMO_MODE = "true"

# Branch deploy context
[context.branch-deploy]
  command = "npm run build:dev"

# Redirects and rewrites
[[redirects]]
  # SPA fallback
  from = "/*"
  to = "/index.html"
  status = 200

[[redirects]]
  # API proxy
  from = "/api/*"
  to = "https://api.kojaonline.store/api/:splat"
  status = 200
  force = true

[[redirects]]
  # Old URLs redirect
  from = "/old-landing"
  to = "/"
  status = 301

[[redirects]]
  # Enforce HTTPS
  from = "http://demo.kojaonline.store/*"
  to = "https://demo.kojaonline.store/:splat"
  status = 301
  force = true

[[redirects]]
  # Enforce www removal
  from = "https://www.demo.kojaonline.store/*"
  to = "https://demo.kojaonline.store/:splat"
  status = 301
  force = true

[[redirects]]
  # Admin redirect
  from = "/admin"
  to = "/admin/dashboard"
  status = 302

[[redirects]]
  # User dashboard redirect
  from = "/dashboard"
  to = "/user/dashboard"
  status = 302

# Headers
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"
    
    # HSTS
    Strict-Transport-Security = "max-age=31536000; includeSubDomains; preload"
    
    # Content Security Policy
    Content-Security-Policy = '''
      default-src 'self';
      script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.paystack.co https://www.googletagmanager.com https://www.google-analytics.com;
      style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
      font-src 'self' https://fonts.gstatic.com;
      img-src 'self' data: https: blob:;
      connect-src 'self' https://api.kojaonline.store https://api.paystack.co https://www.google-analytics.com;
      frame-src https://js.paystack.co;
      object-src 'none';
      base-uri 'self';
      form-action 'self';
    '''

[[headers]]
  for = "/static/*"
  [headers.values]
    # Cache static assets for 1 year
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.png"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.jpg"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.jpeg"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.gif"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.svg"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.webp"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.woff"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.woff2"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/sitemap.xml"
  [headers.values]
    Cache-Control = "public, max-age=3600"
    Content-Type = "application/xml"

[[headers]]
  for = "/robots.txt"
  [headers.values]
    Cache-Control = "public, max-age=3600"
    Content-Type = "text/plain"

[[headers]]
  for = "/manifest.json"
  [headers.values]
    Cache-Control = "public, max-age=86400"
    Content-Type = "application/json"

# Edge handlers
[[edge_handlers]]
  pattern = "/api/*"
  function = "api-proxy"

# Forms
[forms]
  # Contact form
  [forms.contact]
    name = "contact"
    action = "/thank-you"
    
  # Newsletter signup
  [forms.newsletter]
    name = "newsletter"
    action = "/newsletter-success"

# Functions
[functions]
  directory = "netlify/functions"

# Plugins - Removed for now to avoid installation issues

# Split testing
[split_testing]
  [split_testing.landing_page]
    path = "/"
    branches = [
      { branch = "main", percentage = 80 },
      { branch = "enhanced-landing", percentage = 20 }
    ]

# Analytics
[analytics]
  provider = "netlify"

# Large Media
[large_media]
  # Enable Git LFS
  enabled = true
