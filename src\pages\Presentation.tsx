import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  PiggyBank, 
  TrendingUp, 
  Shield, 
  Users, 
  Smartphone, 
  CreditCard,
  BarChart3,
  CheckCircle,
  Star,
  Lock,
  ChevronLeft,
  ChevronRight,
  Play,
  Eye,
  UserCheck,
  Building,
  Zap,
  Award,
  Globe,
  Settings,
  FileText,
  DollarSign,
  Plus
} from "lucide-react";
import { useNavigate } from "react-router-dom";

const Presentation = () => {
  const navigate = useNavigate();
  const [currentSlide, setCurrentSlide] = useState(0);

  const slides = [
    {
      title: "Better Interest",
      subtitle: "Smart Savings for Nigerians",
      type: "hero",
      content: {
        headline: "The Future of Nigerian Savings",
        description: "Combining traditional Nigerian savings culture with modern fintech innovation. Earn up to 15% annual interest while enjoying the security of digital banking.",
        stats: [
          { label: "Active Users", value: "50,000+", icon: <Users className="h-5 w-5" /> },
          { label: "Total Savings", value: "₦2.5B+", icon: <PiggyBank className="h-5 w-5" /> },
          { label: "Interest Paid", value: "₦150M+", icon: <TrendingUp className="h-5 w-5" /> },
          { label: "Security Rating", value: "99.9%", icon: <Shield className="h-5 w-5" /> }
        ]
      }
    },
    {
      title: "Smart Savings Plans",
      subtitle: "Flexible savings with competitive rates",
      type: "feature",
      content: {
        icon: <PiggyBank className="h-12 w-12 text-primary" />,
        features: [
          { name: "SafeLock (Fixed)", rate: "Up to 15% APR", description: "Lock funds for guaranteed returns" },
          { name: "FlexSave", rate: "10% APR", description: "Flexible access to your savings" },
          { name: "AutoSave Roundup", rate: "12% APR", description: "Automatically save spare change" },
          { name: "Goal-based Savings", rate: "Custom rates", description: "Save towards specific targets" }
        ]
      }
    },
    {
      title: "Group Savings (Ajo)",
      subtitle: "Traditional Nigerian savings, digitally enhanced",
      type: "feature",
      content: {
        icon: <Users className="h-12 w-12 text-primary" />,
        features: [
          { name: "Create Groups", description: "Start your own savings circle with friends" },
          { name: "Join Groups", description: "Join existing trusted savings groups" },
          { name: "Rotational System", description: "Fair and transparent contribution cycles" },
          { name: "Social Features", description: "Milestones, posts, and community engagement" }
        ]
      }
    },
    {
      title: "Investment Options",
      subtitle: "Grow your wealth with diversified investments",
      type: "feature",
      content: {
        icon: <TrendingUp className="h-12 w-12 text-primary" />,
        features: [
          { name: "Money Market Funds", description: "Low-risk, liquid investment options" },
          { name: "Treasury Bills", description: "Government-backed secure investments" },
          { name: "Mutual Funds", description: "Professionally managed portfolios" },
          { name: "Fixed Income", description: "Predictable returns on bond investments" }
        ]
      }
    },
    {
      title: "Security & Payments",
      subtitle: "Bank-grade security with seamless payments",
      type: "feature",
      content: {
        icon: <Shield className="h-12 w-12 text-primary" />,
        features: [
          { name: "256-bit SSL Encryption", description: "Military-grade data protection" },
          { name: "Two-Factor Authentication", description: "Extra layer of account security" },
          { name: "KYC Verification", description: "Dojah-powered identity verification" },
          { name: "Paystack Integration", description: "Secure payment processing" }
        ]
      }
    },
    {
      title: "Analytics & Insights",
      subtitle: "Track your financial progress in real-time",
      type: "feature",
      content: {
        icon: <BarChart3 className="h-12 w-12 text-primary" />,
        features: [
          { name: "Savings Progress", description: "Visual tracking of your savings goals" },
          { name: "Spending Analysis", description: "Detailed breakdown of expenses" },
          { name: "Goal Achievement", description: "Monitor progress towards targets" },
          { name: "Performance Insights", description: "AI-powered financial recommendations" }
        ]
      }
    },
    {
      title: "Technical Architecture",
      subtitle: "Built with modern, scalable technology",
      type: "tech",
      content: {
        icon: <Building className="h-12 w-12 text-primary" />,
        stack: [
          { category: "Frontend", tech: "React + TypeScript + Tailwind CSS", icon: <Globe className="h-5 w-5" /> },
          { category: "Backend", tech: "Node.js + Express + MongoDB", icon: <Settings className="h-5 w-5" /> },
          { category: "Payments", tech: "Paystack Integration", icon: <CreditCard className="h-5 w-5" /> },
          { category: "Security", tech: "JWT + 2FA + SSL Encryption", icon: <Lock className="h-5 w-5" /> },
          { category: "KYC", tech: "Dojah Verification API", icon: <UserCheck className="h-5 w-5" /> },
          { category: "Infrastructure", tech: "Cloud-hosted + Auto-scaling", icon: <Zap className="h-5 w-5" /> }
        ]
      }
    },
    {
      title: "Business Logic & Competition",
      subtitle: "How we stand out in the Nigerian fintech space",
      type: "business",
      content: {
        icon: <Award className="h-12 w-12 text-primary" />,
        advantages: [
          { title: "Cultural Understanding", description: "Built specifically for Nigerian savings culture (Ajo/Esusu)" },
          { title: "Competitive Rates", description: "Up to 15% APR - higher than traditional banks" },
          { title: "Community Focus", description: "Social features that encourage group savings" },
          { title: "Security First", description: "Bank-grade security with local compliance" }
        ],
        competition: [
          "PiggyVest", "Cowrywise", "Kuda Bank", "Traditional Banks"
        ]
      }
    }
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const currentSlideData = slides[currentSlide];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 border-b bg-background/95 backdrop-blur">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-lg bg-primary flex items-center justify-center">
                <PiggyBank className="h-6 w-6 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-xl font-bold">Better Interest</h1>
                <p className="text-sm text-muted-foreground">Smart Savings for Nigerians</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <span className="text-sm text-muted-foreground">
                {currentSlide + 1} / {slides.length}
              </span>
              <Button variant="outline" onClick={() => navigate('/login')}>
                Sign In
              </Button>
              <Button onClick={() => navigate('/signup')}>
                Get Started
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Slide Container */}
      <div className="flex-1 flex flex-col">
        {/* Slide Content */}
        <main className="flex-1 py-12 px-4 min-h-[70vh]">
          <div className="container mx-auto max-w-6xl">
            {currentSlideData.type === "hero" && (
              <div className="text-center space-y-12">
                <div className="space-y-6">
                  <Badge variant="secondary" className="mb-4">
                    🇳🇬 Built for Nigerians, by Nigerians
                  </Badge>
                  <h1 className="text-4xl md:text-6xl font-bold">
                    {currentSlideData.content.headline}
                  </h1>
                  <p className="text-xl text-muted-foreground max-w-4xl mx-auto">
                    {currentSlideData.content.description}
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button size="lg" className="gap-2" onClick={() => navigate('/signup')}>
                      <Play className="h-5 w-5" />
                      Start Saving Today
                    </Button>
                    <Button size="lg" variant="outline" className="gap-2" onClick={() => goToSlide(1)}>
                      <Eye className="h-5 w-5" />
                      View Features
                    </Button>
                  </div>
                </div>
                
                {/* Stats Grid */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
                  {currentSlideData.content.stats.map((stat, index) => (
                    <Card key={index} className="text-center">
                      <CardContent className="pt-6">
                        <div className="flex justify-center mb-2 text-primary">
                          {stat.icon}
                        </div>
                        <div className="text-2xl font-bold">{stat.value}</div>
                        <div className="text-sm text-muted-foreground">{stat.label}</div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {currentSlideData.type === "feature" && (
              <div className="space-y-12">
                {/* Header */}
                <div className="text-center space-y-4">
                  <div className="flex justify-center mb-6">
                    {currentSlideData.content.icon}
                  </div>
                  <h1 className="text-4xl md:text-5xl font-bold">{currentSlideData.title}</h1>
                  <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                    {currentSlideData.subtitle}
                  </p>
                </div>

                {/* Features Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
                  {currentSlideData.content.features.map((feature, index) => (
                    <Card key={index} className="h-full">
                      <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                          {feature.name}
                          {feature.rate && (
                            <Badge variant="secondary">{feature.rate}</Badge>
                          )}
                        </CardTitle>
                        <CardDescription className="text-base">
                          {feature.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center gap-2 text-primary">
                          <CheckCircle className="h-4 w-4" />
                          <span className="text-sm font-medium">Available Now</span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {currentSlideData.type === "tech" && (
              <div className="space-y-12">
                {/* Header */}
                <div className="text-center space-y-4">
                  <div className="flex justify-center mb-6">
                    {currentSlideData.content.icon}
                  </div>
                  <h1 className="text-4xl md:text-5xl font-bold">{currentSlideData.title}</h1>
                  <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                    {currentSlideData.subtitle}
                  </p>
                </div>

                {/* Tech Stack Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
                  {currentSlideData.content.stack.map((item, index) => (
                    <Card key={index} className="text-center">
                      <CardHeader>
                        <div className="flex justify-center mb-2 text-primary">
                          {item.icon}
                        </div>
                        <CardTitle className="text-lg">{item.category}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground">{item.tech}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {currentSlideData.type === "business" && (
              <div className="space-y-12">
                {/* Header */}
                <div className="text-center space-y-4">
                  <div className="flex justify-center mb-6">
                    {currentSlideData.content.icon}
                  </div>
                  <h1 className="text-4xl md:text-5xl font-bold">{currentSlideData.title}</h1>
                  <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                    {currentSlideData.subtitle}
                  </p>
                </div>

                {/* Competitive Advantages */}
                <div className="space-y-8">
                  <h2 className="text-2xl font-bold text-center">Our Competitive Advantages</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
                    {currentSlideData.content.advantages.map((advantage, index) => (
                      <Card key={index}>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Star className="h-5 w-5 text-primary" />
                            {advantage.title}
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-muted-foreground">{advantage.description}</p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>

                {/* Competition */}
                <div className="text-center space-y-6">
                  <h2 className="text-2xl font-bold">Key Competitors</h2>
                  <div className="flex flex-wrap justify-center gap-4">
                    {currentSlideData.content.competition.map((competitor, index) => (
                      <Badge key={index} variant="outline" className="text-lg py-2 px-4">
                        {competitor}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </main>

        {/* Navigation Controls */}
        <div className="border-t bg-background/95 backdrop-blur">
          <div className="container mx-auto px-4 py-6">
            <div className="flex items-center justify-between">
              {/* Previous Button */}
              <Button 
                variant="outline" 
                onClick={prevSlide}
                disabled={currentSlide === 0}
                className="gap-2"
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>

              {/* Slide Indicators */}
              <div className="flex items-center gap-2">
                {slides.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToSlide(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      index === currentSlide ? 'bg-primary' : 'bg-muted'
                    }`}
                    aria-label={`Go to slide ${index + 1}`}
                  />
                ))}
              </div>

              {/* Next Button */}
              <Button 
                variant="outline" 
                onClick={nextSlide}
                disabled={currentSlide === slides.length - 1}
                className="gap-2"
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Slide Title */}
            <div className="text-center mt-6">
              <h2 className="text-sm font-medium text-muted-foreground">
                {currentSlideData.title}
              </h2>
              <p className="text-xs text-muted-foreground">
                {currentSlideData.subtitle}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Presentation;