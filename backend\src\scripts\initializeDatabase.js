const mongoose = require('mongoose');
const { connectDB } = require('../config/database');

/**
 * Database initialization script
 * Sets up indexes, constraints, and optimizations for production
 */
class DatabaseInitializer {
  constructor() {
    this.collections = [
      'users',
      'transactions',
      'savingsplans',
      'groupsavingsplans',
      'fixeddeposits',
      'investments',
      'notifications',
      'referrals',
      'cards',
      'bills'
    ];
  }

  async initialize() {
    try {
      console.log('🔧 Starting database initialization...');
      
      await connectDB();
      
      // Create indexes for all collections
      await this.createIndexes();
      
      // Set up database constraints
      await this.setupConstraints();
      
      // Optimize collections
      await this.optimizeCollections();
      
      // Validate data integrity
      await this.validateDataIntegrity();
      
      console.log('✅ Database initialization completed successfully');
      
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      throw error;
    }
  }

  /**
   * Create optimized indexes for all collections
   */
  async createIndexes() {
    console.log('📋 Creating database indexes...');
    
    const db = mongoose.connection.db;
    
    try {
      // Users collection indexes
      await db.collection('users').createIndexes([
        { key: { email: 1 }, unique: true, name: 'email_unique' },
        { key: { phoneNumber: 1 }, unique: true, sparse: true, name: 'phone_unique' },
        { key: { referralCode: 1 }, unique: true, sparse: true, name: 'referral_code_unique' },
        { key: { createdAt: -1 }, name: 'created_at_desc' },
        { key: { isVerified: 1, isActive: 1 }, name: 'user_status' },
        { key: { 'kyc.status': 1 }, name: 'kyc_status' },
        { key: { lastLoginDate: -1 }, name: 'last_login_desc' }
      ]);
      console.log('✅ Users indexes created');

      // Transactions collection indexes
      await db.collection('transactions').createIndexes([
        { key: { userId: 1, createdAt: -1 }, name: 'user_transactions' },
        { key: { reference: 1 }, unique: true, name: 'reference_unique' },
        { key: { type: 1, status: 1 }, name: 'type_status' },
        { key: { status: 1, createdAt: -1 }, name: 'status_date' },
        { key: { relatedId: 1, relatedModel: 1 }, name: 'related_entity' },
        { key: { amount: 1 }, name: 'amount_index' },
        { key: { category: 1, createdAt: -1 }, name: 'category_date' },
        { key: { 'metadata.isReversal': 1 }, sparse: true, name: 'reversal_flag' }
      ]);
      console.log('✅ Transactions indexes created');

      // Savings plans collection indexes
      await db.collection('savingsplans').createIndexes([
        { key: { userId: 1, status: 1 }, name: 'user_status' },
        { key: { targetDate: 1, status: 1 }, name: 'target_date_status' },
        { key: { planType: 1, isActive: 1 }, name: 'plan_type_active' },
        { key: { createdAt: -1 }, name: 'created_desc' },
        { key: { completedAt: -1 }, sparse: true, name: 'completed_desc' },
        { key: { currentAmount: 1, targetAmount: 1 }, name: 'amount_progress' }
      ]);
      console.log('✅ Savings plans indexes created');

      // Group savings plans collection indexes
      await db.collection('groupsavingsplans').createIndexes([
        { key: { createdBy: 1, status: 1 }, name: 'creator_status' },
        { key: { 'members.userId': 1 }, name: 'member_lookup' },
        { key: { targetDate: 1, status: 1 }, name: 'target_status' },
        { key: { isPublic: 1, status: 1 }, name: 'public_status' },
        { key: { createdAt: -1 }, name: 'created_desc' }
      ]);
      console.log('✅ Group savings plans indexes created');

      // Fixed deposits collection indexes
      await db.collection('fixeddeposits').createIndexes([
        { key: { userId: 1, status: 1 }, name: 'user_status' },
        { key: { maturityDate: 1, status: 1 }, name: 'maturity_status' },
        { key: { interestRate: 1, duration: 1 }, name: 'rate_duration' },
        { key: { createdAt: -1 }, name: 'created_desc' },
        { key: { autoRenewal: 1, maturityDate: 1 }, name: 'auto_renewal' }
      ]);
      console.log('✅ Fixed deposits indexes created');

      // Investments collection indexes
      await db.collection('investments').createIndexes([
        { key: { userId: 1, status: 1 }, name: 'user_status' },
        { key: { productType: 1, status: 1 }, name: 'product_status' },
        { key: { maturityDate: 1 }, sparse: true, name: 'maturity_date' },
        { key: { riskLevel: 1, productType: 1 }, name: 'risk_product' },
        { key: { createdAt: -1 }, name: 'created_desc' }
      ]);
      console.log('✅ Investments indexes created');

      // Notifications collection indexes
      await db.collection('notifications').createIndexes([
        { key: { userId: 1, isRead: 1, createdAt: -1 }, name: 'user_notifications' },
        { key: { type: 1, createdAt: -1 }, name: 'type_date' },
        { key: { isRead: 1, createdAt: -1 }, name: 'read_status' },
        { key: { expiresAt: 1 }, expireAfterSeconds: 0, name: 'auto_expire' }
      ]);
      console.log('✅ Notifications indexes created');

      // Referrals collection indexes
      await db.collection('referrals').createIndexes([
        { key: { referrerId: 1, status: 1 }, name: 'referrer_status' },
        { key: { refereeId: 1 }, name: 'referee_lookup' },
        { key: { referralCode: 1 }, name: 'code_lookup' },
        { key: { status: 1, createdAt: -1 }, name: 'status_date' }
      ]);
      console.log('✅ Referrals indexes created');

      // Cards collection indexes
      await db.collection('cards').createIndexes([
        { key: { userId: 1, isActive: 1 }, name: 'user_active_cards' },
        { key: { cardToken: 1 }, unique: true, name: 'token_unique' },
        { key: { isDefault: 1, userId: 1 }, name: 'default_card' },
        { key: { expiryYear: 1, expiryMonth: 1 }, name: 'expiry_date' }
      ]);
      console.log('✅ Cards indexes created');

      // Bills collection indexes
      await db.collection('bills').createIndexes([
        { key: { userId: 1, status: 1 }, name: 'user_status' },
        { key: { billType: 1, provider: 1 }, name: 'type_provider' },
        { key: { reference: 1 }, unique: true, name: 'reference_unique' },
        { key: { createdAt: -1 }, name: 'created_desc' }
      ]);
      console.log('✅ Bills indexes created');

    } catch (error) {
      console.error('❌ Error creating indexes:', error);
      throw error;
    }
  }

  /**
   * Set up database constraints and validation rules
   */
  async setupConstraints() {
    console.log('🔒 Setting up database constraints...');
    
    try {
      const db = mongoose.connection.db;
      
      // Add validation rules for critical collections
      await db.command({
        collMod: 'transactions',
        validator: {
          $jsonSchema: {
            bsonType: 'object',
            required: ['userId', 'type', 'amount', 'description', 'status'],
            properties: {
              amount: {
                bsonType: 'number',
                minimum: 0,
                description: 'Amount must be a positive number'
              },
              type: {
                enum: [
                  'deposit', 'withdrawal', 'transfer', 'interest', 'penalty',
                  'bonus', 'refund', 'bill_payment', 'loan_disbursement',
                  'loan_repayment', 'purchase', 'investment'
                ],
                description: 'Type must be a valid transaction type'
              },
              status: {
                enum: ['pending', 'completed', 'failed', 'cancelled', 'reversed'],
                description: 'Status must be a valid transaction status'
              }
            }
          }
        },
        validationLevel: 'moderate',
        validationAction: 'warn'
      });
      
      console.log('✅ Database constraints set up');
      
    } catch (error) {
      console.warn('⚠️ Some constraints could not be set up:', error.message);
    }
  }

  /**
   * Optimize collections for performance
   */
  async optimizeCollections() {
    console.log('⚡ Optimizing collections...');
    
    try {
      const db = mongoose.connection.db;
      
      // Compact collections to reclaim space
      for (const collection of this.collections) {
        try {
          await db.command({ compact: collection });
          console.log(`✅ Compacted ${collection} collection`);
        } catch (error) {
          console.warn(`⚠️ Could not compact ${collection}:`, error.message);
        }
      }
      
      // Update collection statistics
      for (const collection of this.collections) {
        try {
          await db.command({ reIndex: collection });
          console.log(`✅ Reindexed ${collection} collection`);
        } catch (error) {
          console.warn(`⚠️ Could not reindex ${collection}:`, error.message);
        }
      }
      
    } catch (error) {
      console.warn('⚠️ Some optimizations could not be completed:', error.message);
    }
  }

  /**
   * Validate data integrity
   */
  async validateDataIntegrity() {
    console.log('🔍 Validating data integrity...');
    
    try {
      const db = mongoose.connection.db;
      
      // Check for orphaned transactions
      const orphanedTransactions = await db.collection('transactions').countDocuments({
        userId: { $exists: true },
        $expr: {
          $eq: [
            { $size: { $ifNull: [{ $lookup: { from: 'users', localField: 'userId', foreignField: '_id', as: 'user' } }, []] } },
            0
          ]
        }
      });
      
      if (orphanedTransactions > 0) {
        console.warn(`⚠️ Found ${orphanedTransactions} orphaned transactions`);
      }
      
      // Check for duplicate references
      const duplicateRefs = await db.collection('transactions').aggregate([
        { $group: { _id: '$reference', count: { $sum: 1 } } },
        { $match: { count: { $gt: 1 } } }
      ]).toArray();
      
      if (duplicateRefs.length > 0) {
        console.warn(`⚠️ Found ${duplicateRefs.length} duplicate transaction references`);
      }
      
      console.log('✅ Data integrity validation completed');
      
    } catch (error) {
      console.warn('⚠️ Data integrity validation failed:', error.message);
    }
  }

  /**
   * Get database statistics
   */
  async getStats() {
    try {
      const db = mongoose.connection.db;
      const stats = await db.stats();
      
      const collectionStats = {};
      for (const collection of this.collections) {
        try {
          const collStats = await db.collection(collection).stats();
          collectionStats[collection] = {
            count: collStats.count,
            size: collStats.size,
            avgObjSize: collStats.avgObjSize,
            storageSize: collStats.storageSize,
            indexes: collStats.nindexes,
            indexSize: collStats.totalIndexSize
          };
        } catch (error) {
          collectionStats[collection] = { error: error.message };
        }
      }
      
      return {
        database: {
          collections: stats.collections,
          objects: stats.objects,
          avgObjSize: stats.avgObjSize,
          dataSize: stats.dataSize,
          storageSize: stats.storageSize,
          indexes: stats.indexes,
          indexSize: stats.indexSize
        },
        collections: collectionStats
      };
      
    } catch (error) {
      console.error('Error getting database stats:', error);
      return { error: error.message };
    }
  }
}

// Export for use in other scripts
module.exports = DatabaseInitializer;

// Run if called directly
if (require.main === module) {
  const initializer = new DatabaseInitializer();
  
  initializer.initialize()
    .then(() => {
      console.log('🎉 Database initialization completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Database initialization failed:', error);
      process.exit(1);
    });
}
