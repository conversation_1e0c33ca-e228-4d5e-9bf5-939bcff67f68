
import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle
} from "@/components/ui/dialog";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import {
    Check,
    Edit,
    MoreVertical,
    Search,
    Shield,
    ShieldAlert,
    UserCog,
    UserPlus,
    X
} from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface StaffMember {
  id: string;
  name: string;
  email: string;
  role: string;
  permissions: string[];
  lastLogin: string;
  status?: string;
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
}

// Define permission descriptions for UI
const permissionDescriptions: Record<string, string> = {
  manage_users: "Full user management capabilities",
  view_users: "View user details",
  edit_users: "Edit user information",
  create_users: "Create new users",
  manage_plans: "Full plan management",
  view_plans: "View savings plans",
  create_plans: "Create new savings plans",
  edit_plans: "Modify existing plans",
  manage_transactions: "Manage financial transactions",
  approve_withdrawals: "Approve withdrawal requests",
  manage_notifications: "Manage system notifications",
  view_reports: "View financial reports",
  manage_staff: "Manage staff accounts"
};

const StaffRoleManagement = () => {
  const [staffMembers, setStaffMembers] = useState<StaffMember[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [selectedStaff, setSelectedStaff] = useState<StaffMember | null>(null);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);

  // Role assignment state
  const [roleAssignment, setRoleAssignment] = useState({
    userId: "",
    roleId: "",
    customPermissions: [] as string[]
  });

  // Fetch staff and roles
  useEffect(() => {
    const fetchStaffAndRoles = async () => {
      setIsLoading(true);
      try {
        // Fetch staff data from API
        const staffResponse = await fetch('/api/v1/admin/staff', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (staffResponse.ok) {
          const staffData = await staffResponse.json();
          setStaffMembers(staffData.data || []);
        }

        // Fetch roles data from API
        const rolesResponse = await fetch('/api/v1/admin/roles', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (rolesResponse.ok) {
          const rolesData = await rolesResponse.json();
          setRoles(rolesData.data || []);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to load staff and roles");
      } finally {
        setIsLoading(false);
      }
    };

    fetchStaffAndRoles();
  }, []);

  // Filter staff based on search term
  const filteredStaff = staffMembers.filter(
    staff =>
      staff.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      staff.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      staff.role.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle assigning a role to staff
  const handleOpenAssignDialog = (staff: StaffMember) => {
    setSelectedStaff(staff);
    setRoleAssignment({
      userId: staff.id,
      roleId: roles.find(r => r.name === staff.role)?.id || "",
      customPermissions: [...staff.permissions]
    });
    setAssignDialogOpen(true);
  };

  // Handle role selection in the assign dialog
  const handleRoleSelection = (roleId: string) => {
    const role = roles.find(r => r.id === roleId);
    if (role) {
      setRoleAssignment({
        ...roleAssignment,
        roleId,
        customPermissions: [...role.permissions]
      });
    }
  };

  // Handle permission toggle
  const handlePermissionToggle = (permission: string) => {
    setRoleAssignment(prev => {
      if (prev.customPermissions.includes(permission)) {
        return {
          ...prev,
          customPermissions: prev.customPermissions.filter(p => p !== permission)
        };
      } else {
        return {
          ...prev,
          customPermissions: [...prev.customPermissions, permission]
        };
      }
    });
  };

  // Submit role assignment
  const handleSubmitAssignment = async () => {
    setIsLoading(true);
    try {
      // In a real app, this would be an API call
      console.log("Assigning role:", roleAssignment);
      
      // Update role assignment via API
      const response = await fetch('/api/v1/admin/staff/assign-role', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(roleAssignment)
      });

      if (!response.ok) {
        throw new Error('Failed to assign role');
      }

      // Refresh staff data
      const staffResponse = await fetch('/api/v1/admin/staff', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (staffResponse.ok) {
        const staffData = await staffResponse.json();
        setStaffMembers(staffData.data || []);
      }
      toast.success("Role assigned successfully");
      setAssignDialogOpen(false);
    } catch (error) {
      console.error("Error assigning role:", error);
      toast.error("Failed to assign role");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Staff Role Management</h2>
          <p className="text-muted-foreground">
            Assign and manage staff roles and permissions
          </p>
        </div>
        <Button className="bg-brand-blue text-white">
          <UserPlus className="mr-2 h-4 w-4" /> Add New Staff
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Staff Members</CardTitle>
          <CardDescription>
            Manage staff roles and permissions
          </CardDescription>
          <div className="flex items-center gap-2 mt-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search staff..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all">
            <TabsList>
              <TabsTrigger value="all">All Staff</TabsTrigger>
              <TabsTrigger value="admin">Admins</TabsTrigger>
              <TabsTrigger value="managers">Managers</TabsTrigger>
              <TabsTrigger value="support">Support</TabsTrigger>
            </TabsList>
            <TabsContent value="all" className="mt-4">
              {renderStaffTable(filteredStaff, isLoading)}
            </TabsContent>
            <TabsContent value="admin" className="mt-4">
              {renderStaffTable(filteredStaff.filter(s => s.role === "Admin"), isLoading)}
            </TabsContent>
            <TabsContent value="managers" className="mt-4">
              {renderStaffTable(filteredStaff.filter(s => s.role.includes("Manager")), isLoading)}
            </TabsContent>
            <TabsContent value="support" className="mt-4">
              {renderStaffTable(filteredStaff.filter(s => s.role.includes("Support")), isLoading)}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Available Roles</CardTitle>
          <CardDescription>
            System roles and their permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Role</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Permissions</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {roles.map((role) => (
                  <TableRow key={role.id}>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Shield className="h-5 w-5 text-brand-blue" />
                        <span className="font-medium">{role.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>{role.description}</TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1 max-w-md">
                        {role.permissions.slice(0, 3).map(permission => (
                          <Badge key={permission} variant="outline" className="text-xs">
                            {permission.replace('_', ' ')}
                          </Badge>
                        ))}
                        {role.permissions.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{role.permissions.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4 mr-2" /> Edit
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Role Assignment Dialog */}
      <Dialog open={assignDialogOpen} onOpenChange={setAssignDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Assign Role to Staff</DialogTitle>
            <DialogDescription>
              {selectedStaff && (
                <>Updating role and permissions for <span className="font-medium">{selectedStaff.name}</span></>
              )}
            </DialogDescription>
          </DialogHeader>
          
          {selectedStaff && (
            <div className="grid gap-6 py-4">
              <div className="flex items-center space-x-3">
                <Avatar className="h-10 w-10">
                  <AvatarFallback className="bg-brand-blue text-white">
                    {selectedStaff.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-medium">{selectedStaff.name}</h3>
                  <p className="text-sm text-muted-foreground">{selectedStaff.email}</p>
                </div>
              </div>
              
              <div>
                <Label htmlFor="role-select">Select Role</Label>
                <select
                  id="role-select"
                  value={roleAssignment.roleId}
                  onChange={(e) => handleRoleSelection(e.target.value)}
                  className="w-full p-2 mt-1 border rounded-md"
                >
                  <option value="">Select a role</option>
                  {roles.map(role => (
                    <option key={role.id} value={role.id}>
                      {role.name} - {role.description}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <Label>Permissions</Label>
                <div className="border rounded-md p-4 mt-1 max-h-[300px] overflow-y-auto">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {Object.entries(permissionDescriptions).map(([perm, desc]) => (
                      <div key={perm} className="flex items-start space-x-2">
                        <Checkbox 
                          id={`perm-${perm}`}
                          checked={roleAssignment.customPermissions.includes(perm)}
                          onCheckedChange={() => handlePermissionToggle(perm)}
                        />
                        <div className="space-y-1 leading-none">
                          <label
                            htmlFor={`perm-${perm}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                          >
                            {perm.replace(/_/g, ' ')}
                          </label>
                          <p className="text-xs text-muted-foreground">{desc}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setAssignDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleSubmitAssignment} 
              disabled={isLoading || !roleAssignment.roleId}
              className="bg-brand-blue text-white"
            >
              {isLoading ? "Saving..." : "Save Changes"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );

  function renderStaffTable(staff: StaffMember[], loading: boolean) {
    if (loading) {
      return (
        <div className="flex justify-center items-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-brand-blue border-solid"></div>
        </div>
      );
    }
    
    if (staff.length === 0) {
      return (
        <div className="text-center py-8 text-muted-foreground">
          No staff members found matching your search.
        </div>
      );
    }
    
    return (
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Staff Member</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Permissions</TableHead>
              <TableHead>Last Login</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {staff.map((member) => (
              <TableRow key={member.id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar className="h-9 w-9">
                      <AvatarFallback className="bg-brand-blue/10 text-brand-blue">
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{member.name}</div>
                      <div className="text-xs text-muted-foreground">{member.email}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge 
                    variant={member.role === "Admin" ? "default" : "outline"}
                    className={member.role === "Admin" ? "bg-brand-blue text-white" : ""}
                  >
                    {member.role}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1 max-w-md">
                    {member.permissions.slice(0, 2).map(permission => (
                      <Badge key={permission} variant="outline" className="text-xs bg-green-50 border-green-200 text-green-700">
                        {permission.replace(/_/g, ' ')}
                      </Badge>
                    ))}
                    {member.permissions.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{member.permissions.length - 2} more
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  {new Date(member.lastLogin).toLocaleString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleOpenAssignDialog(member)}>
                        <UserCog className="mr-2 h-4 w-4" /> Assign Role
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="mr-2 h-4 w-4" /> Edit Details
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <ShieldAlert className="mr-2 h-4 w-4" /> Reset Password
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        {member.status === 'active' ? (
                          <>
                            <X className="mr-2 h-4 w-4" /> Deactivate
                          </>
                        ) : (
                          <>
                            <Check className="mr-2 h-4 w-4" /> Activate
                          </>
                        )}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }
};

export default StaffRoleManagement;
