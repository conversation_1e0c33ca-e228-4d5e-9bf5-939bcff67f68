import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  MessageCircle, 
  X, 
  Send, 
  Phone, 
  Mail, 
  Clock,
  Minimize2,
  Maximize2,
  Bot,
  User,
  Headphones
} from 'lucide-react';
import { useAuth } from '@/hooks/use-auth';
import { toast } from 'sonner';

interface Message {
  id: string;
  type: 'user' | 'agent' | 'bot';
  message: string;
  timestamp: Date;
  status?: 'sent' | 'delivered' | 'read';
}

interface CustomerSupportWidgetProps {
  isOpen: boolean;
  onToggle: () => void;
  provider?: 'intercom' | 'tidio' | 'jivo' | 'custom';
}

export const CustomerSupportWidget = ({ 
  isOpen, 
  onToggle, 
  provider = 'custom' 
}: CustomerSupportWidgetProps) => {
  const { user } = useAuth();
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'bot',
      message: 'Hello! Welcome to Better Interest Support. How can I help you today?',
      timestamp: new Date(),
      status: 'read'
    }
  ]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);

  // Simulate agent responses
  const agentResponses = [
    "Thank you for contacting us! I'm reviewing your request now.",
    "I understand your concern. Let me help you with that.",
    "Could you please provide more details about this issue?",
    "I've escalated this to our technical team. You'll receive an update within 24 hours.",
    "Is there anything else I can help you with today?",
    "Thank you for choosing Better Interest. Have a great day!"
  ];

  const quickActions = [
    { label: 'Account Help', message: 'I need help with my account' },
    { label: 'Payment Issue', message: 'I have a payment or withdrawal issue' },
    { label: 'KYC Support', message: 'I need help with KYC verification' },
    { label: 'Technical Support', message: 'I am experiencing technical difficulties' }
  ];

  const sendMessage = () => {
    if (!newMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      message: newMessage,
      timestamp: new Date(),
      status: 'sent'
    };

    setMessages(prev => [...prev, userMessage]);
    setNewMessage('');
    setIsTyping(true);

    // Simulate agent response
    setTimeout(() => {
      const agentMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        message: agentResponses[Math.floor(Math.random() * agentResponses.length)],
        timestamp: new Date(),
        status: 'read'
      };
      
      setMessages(prev => [...prev, agentMessage]);
      setIsTyping(false);
      
      if (!isOpen) {
        setUnreadCount(prev => prev + 1);
      }
    }, 1000 + Math.random() * 2000);
  };

  const sendQuickAction = (message: string) => {
    setNewMessage(message);
    setTimeout(() => sendMessage(), 100);
  };

  useEffect(() => {
    if (isOpen) {
      setUnreadCount(0);
    }
  }, [isOpen]);

  // Widget trigger button - positioned above mobile navigation
  if (!isOpen) {
    return (
      <div className="fixed bottom-20 md:bottom-4 right-4 z-50">
        <Button
          onClick={onToggle}
          className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 bg-primary hover:bg-primary/90 relative"
          size="lg"
        >
          <MessageCircle className="h-6 w-6" />
          {unreadCount > 0 && (
            <Badge className="absolute -top-1 -right-1 h-6 w-6 rounded-full p-0 flex items-center justify-center text-xs bg-red-500">
              {unreadCount}
            </Badge>
          )}
        </Button>
      </div>
    );
  }

  // Full chat widget - responsive positioning above mobile nav
  return (
    <div className="fixed bottom-20 md:bottom-4 right-4 z-50 w-80 sm:w-96 h-[500px] bg-background border border-border rounded-lg shadow-xl overflow-hidden animate-scale-in">
      {/* Header */}
      <div className="bg-primary text-primary-foreground p-4 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="h-8 w-8 rounded-full bg-primary-foreground/20 flex items-center justify-center">
            <Headphones className="h-4 w-4" />
          </div>
          <div>
            <h3 className="font-semibold text-sm">Support Chat</h3>
            <div className="flex items-center gap-1 text-xs">
              <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-green-400' : 'bg-gray-400'}`} />
              {isOnline ? 'Online' : 'Offline'}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsMinimized(!isMinimized)}
            className="h-8 w-8 p-0 hover:bg-primary-foreground/20"
          >
            {isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
            className="h-8 w-8 p-0 hover:bg-primary-foreground/20"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {!isMinimized && (
        <>
          {/* Messages */}
          <ScrollArea className="flex-1 p-4 h-[320px]">
            <div className="space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className="flex items-start gap-2 max-w-[80%]">
                    {message.type !== 'user' && (
                      <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0 mt-1">
                        {message.type === 'bot' ? 
                          <Bot className="h-3 w-3" /> : 
                          <User className="h-3 w-3" />
                        }
                      </div>
                    )}
                    <div>
                      <div
                        className={`rounded-lg p-3 text-sm ${
                          message.type === 'user'
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-muted'
                        }`}
                      >
                        {message.message}
                      </div>
                      <div className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
                        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        {message.type === 'user' && message.status && (
                          <span className="text-primary">✓</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              {isTyping && (
                <div className="flex justify-start">
                  <div className="flex items-center gap-2">
                    <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center">
                      <User className="h-3 w-3" />
                    </div>
                    <div className="bg-muted rounded-lg p-3">
                      <div className="flex gap-1">
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" />
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce delay-100" />
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce delay-200" />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>

          {/* Quick Actions */}
          {messages.length === 1 && (
            <div className="p-4 border-t border-border">
              <p className="text-xs text-muted-foreground mb-2">Quick actions:</p>
              <div className="grid grid-cols-2 gap-2">
                {quickActions.map((action, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => sendQuickAction(action.message)}
                    className="text-xs h-8 hover:bg-primary/10"
                  >
                    {action.label}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Input with proper spacing */}
          <div className="p-4 border-t border-border">
            <div className="flex gap-3">
              <Input
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Type your message..."
                className="flex-1 rounded-none"
                onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
              />
              <Button 
                onClick={sendMessage}
                disabled={!newMessage.trim()}
                size="sm"
                className="px-4 rounded-none"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex justify-between items-center mt-2 text-xs text-muted-foreground">
              <span>Powered by Better Interest</span>
              <div className="flex gap-2">
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={() => window.open('tel:+2348002238837')}>
                  <Phone className="h-3 w-3" />
                </Button>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={() => window.open('mailto:<EMAIL>')}>
                  <Mail className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};