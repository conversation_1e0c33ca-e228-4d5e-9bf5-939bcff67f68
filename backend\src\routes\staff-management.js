const express = require('express');
const router = express.Router();
const { auth, admin } = require('../middleware/auth');
const User = require('../models/User');
const StaffRole = require('../models/StaffRole');
const StaffAssignment = require('../models/StaffAssignment');
const BillProvider = require('../models/BillProvider');
const BillPayment = require('../models/BillPayment');

// Permission check middleware
const checkPermission = (action, resource) => {
  return async (req, res, next) => {
    try {
      const userId = req.user.id;
      
      // Admin users have all permissions
      if (req.user.role === 'admin') {
        return next();
      }

      // Check staff assignments and permissions
      const assignments = await StaffAssignment.find({
        userId,
        isActive: true,
        $or: [
          { endDate: { $gte: new Date() } },
          { endDate: null }
        ]
      }).populate('roleId');

      let hasPermission = false;

      for (const assignment of assignments) {
        // Check role permissions
        for (const permission of assignment.roleId.permissions) {
          if (permission.action === action && permission.resource === resource) {
            hasPermission = true;
            break;
          }
        }

        // Check custom permissions
        for (const customPerm of assignment.customPermissions) {
          if (customPerm.action === action && customPerm.resource === resource && customPerm.granted) {
            hasPermission = true;
            break;
          }
        }

        if (hasPermission) break;
      }

      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      res.status(500).json({
        success: false,
        message: 'Permission check failed'
      });
    }
  };
};

// Get all staff roles
router.get('/roles', [auth, admin], async (req, res) => {
  try {
    const { module, isActive } = req.query;
    
    const filter = {};
    if (module) filter.module = module;
    if (isActive !== undefined) filter.isActive = isActive === 'true';

    const roles = await StaffRole.find(filter)
      .populate('createdBy', 'firstName lastName email')
      .sort({ priority: -1, createdAt: -1 });

    res.json({
      success: true,
      data: roles
    });
  } catch (error) {
    console.error('Error fetching roles:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch roles'
    });
  }
});

// Create new staff role
router.post('/roles', [auth, admin], async (req, res) => {
  try {
    const { name, description, module, permissions, priority } = req.body;

    const existingRole = await StaffRole.findOne({ name, module });
    if (existingRole) {
      return res.status(400).json({
        success: false,
        message: 'Role with this name already exists for this module'
      });
    }

    const role = new StaffRole({
      name,
      description,
      module,
      permissions,
      priority: priority || 0,
      createdBy: req.user.id
    });

    await role.save();
    await role.populate('createdBy', 'firstName lastName email');

    res.status(201).json({
      success: true,
      data: role,
      message: 'Role created successfully'
    });
  } catch (error) {
    console.error('Error creating role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create role'
    });
  }
});

// Update staff role
router.put('/roles/:roleId', [auth, admin], async (req, res) => {
  try {
    const { name, description, permissions, priority, isActive } = req.body;

    const role = await StaffRole.findByIdAndUpdate(
      req.params.roleId,
      {
        name,
        description,
        permissions,
        priority,
        isActive
      },
      { new: true }
    ).populate('createdBy', 'firstName lastName email');

    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    res.json({
      success: true,
      data: role,
      message: 'Role updated successfully'
    });
  } catch (error) {
    console.error('Error updating role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update role'
    });
  }
});

// Delete staff role
router.delete('/roles/:roleId', [auth, admin], async (req, res) => {
  try {
    // Check if role is assigned to any staff
    const assignmentCount = await StaffAssignment.countDocuments({
      roleId: req.params.roleId,
      isActive: true
    });

    if (assignmentCount > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete role that is currently assigned to staff members'
      });
    }

    const role = await StaffRole.findByIdAndDelete(req.params.roleId);
    
    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    res.json({
      success: true,
      message: 'Role deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete role'
    });
  }
});

// Get all staff members with their assignments
router.get('/staff', [auth, admin], async (req, res) => {
  try {
    const { page = 1, limit = 20, module, search } = req.query;

    const filter = { role: { $in: ['staff', 'admin'] } };
    
    if (search) {
      filter.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    const staff = await User.find(filter)
      .select('-password -pin')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await User.countDocuments(filter);

    // Get assignments for each staff member
    const staffWithAssignments = await Promise.all(
      staff.map(async (member) => {
        let assignmentFilter = { userId: member._id, isActive: true };
        if (module) assignmentFilter.module = module;

        const assignments = await StaffAssignment.find(assignmentFilter)
          .populate('roleId')
          .populate('assignedBy', 'firstName lastName email');

        return {
          ...member.toObject(),
          assignments
        };
      })
    );

    res.json({
      success: true,
      data: staffWithAssignments,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching staff:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch staff'
    });
  }
});

// Assign role to staff member
router.post('/assign', [auth, admin], async (req, res) => {
  try {
    const { userId, roleId, module, customPermissions, notes, endDate } = req.body;

    // Verify user exists and is staff/admin
    const user = await User.findById(userId);
    if (!user || !['staff', 'admin'].includes(user.role)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid user or user is not staff/admin'
      });
    }

    // Verify role exists
    const role = await StaffRole.findById(roleId);
    if (!role) {
      return res.status(400).json({
        success: false,
        message: 'Role not found'
      });
    }

    // Check if user already has an active assignment for this module
    const existingAssignment = await StaffAssignment.findOne({
      userId,
      module,
      isActive: true
    });

    if (existingAssignment) {
      // Deactivate existing assignment
      existingAssignment.isActive = false;
      await existingAssignment.save();
    }

    // Create new assignment
    const assignment = new StaffAssignment({
      userId,
      roleId,
      assignedBy: req.user.id,
      module,
      customPermissions: customPermissions || [],
      notes,
      endDate: endDate ? new Date(endDate) : null
    });

    await assignment.save();
    await assignment.populate(['userId', 'roleId', 'assignedBy']);

    res.status(201).json({
      success: true,
      data: assignment,
      message: 'Role assigned successfully'
    });
  } catch (error) {
    console.error('Error assigning role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assign role'
    });
  }
});

// Remove staff assignment
router.delete('/assign/:assignmentId', [auth, admin], async (req, res) => {
  try {
    const assignment = await StaffAssignment.findByIdAndUpdate(
      req.params.assignmentId,
      { isActive: false },
      { new: true }
    );

    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Assignment not found'
      });
    }

    res.json({
      success: true,
      message: 'Assignment removed successfully'
    });
  } catch (error) {
    console.error('Error removing assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove assignment'
    });
  }
});

// Bill payment management routes with permission checks

// Get bill providers (staff with view permissions)
router.get('/bill-providers', [auth, checkPermission('view', 'providers')], async (req, res) => {
  try {
    const providers = await BillProvider.find({ isActive: true })
      .select('-apiKey -secretKey');

    res.json({
      success: true,
      data: providers
    });
  } catch (error) {
    console.error('Error fetching providers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch providers'
    });
  }
});

// Manage bill providers (staff with edit permissions)
router.post('/bill-providers', [auth, checkPermission('create', 'providers')], async (req, res) => {
  try {
    const provider = new BillProvider(req.body);
    await provider.save();

    res.status(201).json({
      success: true,
      data: provider,
      message: 'Provider created successfully'
    });
  } catch (error) {
    console.error('Error creating provider:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create provider'
    });
  }
});

// Get bill payments for management
router.get('/bill-payments', [auth, checkPermission('view', 'payments')], async (req, res) => {
  try {
    const { page = 1, limit = 20, status, category, startDate, endDate } = req.query;

    const filter = {};
    if (status) filter.status = status;
    if (category) filter.category = category;
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = new Date(startDate);
      if (endDate) filter.createdAt.$lte = new Date(endDate);
    }

    const payments = await BillPayment.find(filter)
      .populate('userId', 'firstName lastName email phone')
      .populate('providerId', 'name category')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await BillPayment.countDocuments(filter);

    res.json({
      success: true,
      data: payments,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching payments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payments'
    });
  }
});

// Get staff permissions for current user
router.get('/my-permissions', auth, async (req, res) => {
  try {
    if (req.user.role === 'admin') {
      return res.json({
        success: true,
        data: {
          isAdmin: true,
          permissions: ['all'],
          modules: ['all']
        }
      });
    }

    const assignments = await StaffAssignment.find({
      userId: req.user.id,
      isActive: true,
      $or: [
        { endDate: { $gte: new Date() } },
        { endDate: null }
      ]
    }).populate('roleId');

    const permissions = [];
    const modules = [];

    assignments.forEach(assignment => {
      modules.push(assignment.module);
      
      // Add role permissions
      assignment.roleId.permissions.forEach(perm => {
        permissions.push(`${perm.action}:${perm.resource}`);
      });

      // Add custom permissions
      assignment.customPermissions.forEach(perm => {
        if (perm.granted) {
          permissions.push(`${perm.action}:${perm.resource}`);
        }
      });
    });

    res.json({
      success: true,
      data: {
        isAdmin: false,
        permissions: [...new Set(permissions)],
        modules: [...new Set(modules)],
        assignments
      }
    });
  } catch (error) {
    console.error('Error fetching permissions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch permissions'
    });
  }
});

module.exports = router;