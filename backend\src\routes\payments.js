const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const Payment = require('../models/Payment');
const Transaction = require('../models/Transaction');
const GroupSavingsPlan = require('../models/GroupSavingsPlan');
const GroupContribution = require('../models/GroupContribution');
const User = require('../models/User');
const Referral = require('../models/Referral');
const axios = require('axios');
const crypto = require('crypto');

// Paystack webhook for payment verification
router.post('/paystack/webhook', async (req, res) => {
  try {
    const event = req.body;
    const signature = req.headers['x-paystack-signature'];

    // Verify Paystack signature
    const crypto = require('crypto');
    const hash = crypto.createHmac('sha512', process.env.PAYSTACK_SECRET_KEY).update(JSON.stringify(req.body)).digest('hex');

    if (hash !== signature) {
      return res.status(400).send('Invalid signature');
    }

    if (event.event === 'charge.success') {
      const { reference, amount, customer, metadata } = event.data;

      // Find and update payment record
      const payment = await Payment.findOne({ reference });
      if (payment) {
        payment.status = 'completed';
        payment.paystackData = event.data;
        await payment.save();

        // Update user balance for wallet funding
        if (metadata && metadata.purpose === 'wallet_funding') {
          const user = await User.findById(metadata.userId);
          if (user) {
            user.balance += amount / 100; // Convert from kobo
            await user.save();

            // Create transaction record
            await Transaction.create({
              userId: user._id,
              type: 'deposit',
              amount: amount / 100,
              description: 'Wallet funding via Paystack',
              reference,
              status: 'completed'
            });
          }
        }

        // Handle group contributions
        if (metadata && metadata.purpose === 'group_contribution') {
          const group = await GroupSavingsPlan.findById(metadata.groupId);
          if (group) {
            await group.updateOne({
              $inc: { currentAmount: amount / 100 }
            });

            // Record contribution
            await GroupContribution.create({
              groupId: metadata.groupId,
              userId: metadata.userId,
              amount: amount / 100,
              paymentReference: reference,
              status: 'completed'
            });
          }
        }
      }

      console.log('Payment processed successfully:', { reference, amount, customer });
    }

    res.status(200).send('OK');
  } catch (error) {
    console.error('Webhook error:', error);
    res.status(500).send('Webhook error');
  }
});

// Initialize Paystack payment
router.post('/initialize', auth, async (req, res) => {
  try {
    const { amount, purpose, metadata = {} } = req.body;
    const user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Generate unique reference
    const reference = `BI_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

    // Prepare Paystack payload
    const paystackPayload = {
      email: user.email,
      amount: amount * 100, // Convert to kobo
      reference,
      callback_url: `${process.env.FRONTEND_URL}/payment/callback`,
      metadata: {
        userId: user._id.toString(),
        purpose,
        ...metadata
      }
    };

    // Initialize payment with Paystack
    const paystackResponse = await axios.post(
      'https://api.paystack.co/transaction/initialize',
      paystackPayload,
      {
        headers: {
          Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (paystackResponse.data.status) {
      // Create payment record
      const payment = new Payment({
        userId: user._id,
        amount,
        reference,
        purpose,
        metadata,
        status: 'pending',
        paystackData: paystackResponse.data.data
      });

      await payment.save();

      res.json({
        success: true,
        data: {
          authorization_url: paystackResponse.data.data.authorization_url,
          access_code: paystackResponse.data.data.access_code,
          reference
        }
      });
    } else {
      throw new Error('Failed to initialize payment with Paystack');
    }
  } catch (error) {
    console.error('Payment initialization error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initialize payment',
      error: error.message
    });
  }
});

// Verify payment
router.get('/verify/:reference', auth, async (req, res) => {
  try {
    const { reference } = req.params;

    // Verify with Paystack
    const paystackResponse = await axios.get(
      `https://api.paystack.co/transaction/verify/${reference}`,
      {
        headers: {
          Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`
        }
      }
    );

    if (paystackResponse.data.status && paystackResponse.data.data.status === 'success') {
      // Update payment record
      const payment = await Payment.findOne({ reference });
      if (payment && payment.status === 'pending') {
        payment.status = 'completed';
        payment.paystackData = paystackResponse.data.data;
        await payment.save();

        // Process the payment based on purpose
        const { amount, metadata } = paystackResponse.data.data;

        if (metadata.purpose === 'wallet_funding') {
          const user = await User.findById(metadata.userId);
          if (user) {
            user.balance += amount / 100;
            await user.save();

            await Transaction.create({
              userId: user._id,
              type: 'deposit',
              amount: amount / 100,
              description: 'Wallet funding',
              reference,
              status: 'completed'
            });
          }
        }
      }

      res.json({
        success: true,
        data: paystackResponse.data.data
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Payment verification failed'
      });
    }
  } catch (error) {
    console.error('Payment verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify payment'
    });
  }
});

// Process payment (integrated with Paystack)
router.post('/process', auth, async (req, res) => {
  try {
    const { amount, purpose, groupId, reference } = req.body;
    
    // Validate payment data
    if (!amount || !purpose || !reference) {
      return res.status(400).json({
        success: false,
        message: 'Missing required payment data'
      });
    }
    
    // Process based on purpose
    if (purpose === 'group_contribution' && groupId) {
      // Handle group contribution
      const group = await GroupSavingsPlan.findById(groupId);
      
      if (!group) {
        return res.status(404).json({
          success: false,
          message: 'Group not found'
        });
      }
      
      // Update group amount
      await GroupSavingsPlan.findByIdAndUpdate(groupId, {
        $inc: { currentAmount: amount }
      });
    }
    
    // Check if this is user's first payment (for referral completion)
    const user = await User.findById(req.user.id);
    const isFirstPayment = !user.hasCompletedFirstPayment;
    
    if (isFirstPayment && user.referredBy) {
      // Complete referral
      const referral = await Referral.findOne({
        referrerId: user.referredBy,
        referredUserId: user._id,
        status: 'pending'
      });
      
      if (referral) {
        await referral.complete();
      }
      
      // Mark user as having completed first payment
      await User.findByIdAndUpdate(user._id, {
        hasCompletedFirstPayment: true
      });
    }
    
    res.json({
      success: true,
      message: 'Payment processed successfully',
      data: {
        reference,
        amount,
        purpose
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error processing payment',
      error: error.message
    });
  }
});

// Get payment history
router.get('/history', auth, async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    
    // This would typically come from a payments collection
    // For now, returning mock data
    const payments = [
      {
        id: '1',
        amount: 5000,
        purpose: 'group_contribution',
        status: 'completed',
        reference: 'PAY_123456789',
        createdAt: new Date()
      }
    ];
    
    res.json({
      success: true,
      data: {
        payments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: payments.length,
          pages: 1
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching payment history',
      error: error.message
    });
  }
});

// Withdraw funds via Paystack Transfer
router.post('/withdraw', auth, async (req, res) => {
  try {
    const { amount, bankCode, accountNumber, accountName, reason } = req.body;
    
    // Validate withdrawal data
    if (!amount || !bankCode || !accountNumber || !accountName) {
      return res.status(400).json({
        success: false,
        message: 'Missing required withdrawal data'
      });
    }

    // Convert amount to kobo (Paystack uses kobo)
    const amountInKobo = Math.round(amount * 100);

    // Get user balance (in real implementation, this would come from your database)
    const user = await User.findById(req.user.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if user has sufficient balance
    if (!user.balance || user.balance < amount) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient balance'
      });
    }

    // Create recipient on Paystack
    const recipientResponse = await axios.post(
      `${process.env.PAYSTACK_BASE_URL || 'https://api.paystack.co'}/transferrecipient`,
      {
        type: 'nuban',
        name: accountName,
        account_number: accountNumber,
        bank_code: bankCode,
        currency: 'NGN'
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!recipientResponse.data.status) {
      return res.status(400).json({
        success: false,
        message: 'Failed to create transfer recipient',
        error: recipientResponse.data.message
      });
    }

    const recipientCode = recipientResponse.data.data.recipient_code;

    // Generate unique reference
    const reference = `WIT_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

    // Initiate transfer
    const transferResponse = await axios.post(
      `${process.env.PAYSTACK_BASE_URL || 'https://api.paystack.co'}/transfer`,
      {
        source: 'balance',
        amount: amountInKobo,
        recipient: recipientCode,
        reference: reference,
        reason: reason || 'Withdrawal request'
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!transferResponse.data.status) {
      return res.status(400).json({
        success: false,
        message: 'Transfer failed',
        error: transferResponse.data.message
      });
    }

    // Update user balance
    await User.findByIdAndUpdate(req.user.id, {
      $inc: { balance: -amount }
    });

    // Store withdrawal record (you might want to create a Withdrawal model)
    const withdrawalData = {
      userId: req.user.id,
      amount: amount,
      bankCode: bankCode,
      accountNumber: accountNumber,
      accountName: accountName,
      reference: reference,
      paystackReference: transferResponse.data.data.reference,
      status: transferResponse.data.data.status,
      reason: reason,
      createdAt: new Date()
    };

    res.json({
      success: true,
      message: 'Withdrawal initiated successfully',
      data: {
        reference: reference,
        paystackReference: transferResponse.data.data.reference,
        amount: amount,
        status: transferResponse.data.data.status,
        transferCode: transferResponse.data.data.transfer_code
      }
    });

  } catch (error) {
    console.error('Withdrawal error:', error);
    res.status(500).json({
      success: false,
      message: 'Error processing withdrawal',
      error: error.response?.data?.message || error.message
    });
  }
});

// Get list of supported banks
router.get('/banks', async (req, res) => {
  try {
    const banksResponse = await axios.get(
      `${process.env.PAYSTACK_BASE_URL || 'https://api.paystack.co'}/bank`,
      {
        headers: {
          'Authorization': `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!banksResponse.data.status) {
      return res.status(400).json({
        success: false,
        message: 'Failed to fetch banks'
      });
    }

    res.json({
      success: true,
      data: banksResponse.data.data
    });

  } catch (error) {
    console.error('Banks fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching banks',
      error: error.message
    });
  }
});

// Verify account number
router.post('/verify-account', async (req, res) => {
  try {
    const { accountNumber, bankCode } = req.body;

    if (!accountNumber || !bankCode) {
      return res.status(400).json({
        success: false,
        message: 'Account number and bank code are required'
      });
    }

    const verifyResponse = await axios.get(
      `${process.env.PAYSTACK_BASE_URL || 'https://api.paystack.co'}/bank/resolve?account_number=${accountNumber}&bank_code=${bankCode}`,
      {
        headers: {
          'Authorization': `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!verifyResponse.data.status) {
      return res.status(400).json({
        success: false,
        message: 'Account verification failed',
        error: verifyResponse.data.message
      });
    }

    res.json({
      success: true,
      data: {
        accountName: verifyResponse.data.data.account_name,
        accountNumber: verifyResponse.data.data.account_number
      }
    });

  } catch (error) {
    console.error('Account verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Error verifying account',
      error: error.response?.data?.message || error.message
    });
  }
});

// Check withdrawal status
router.get('/withdrawal-status/:reference', auth, async (req, res) => {
  try {
    const { reference } = req.params;

    const transferResponse = await axios.get(
      `${process.env.PAYSTACK_BASE_URL || 'https://api.paystack.co'}/transfer/verify/${reference}`,
      {
        headers: {
          'Authorization': `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!transferResponse.data.status) {
      return res.status(400).json({
        success: false,
        message: 'Failed to check withdrawal status'
      });
    }

    res.json({
      success: true,
      data: transferResponse.data.data
    });

  } catch (error) {
    console.error('Withdrawal status check error:', error);
    res.status(500).json({
      success: false,
      message: 'Error checking withdrawal status',
      error: error.response?.data?.message || error.message
    });
  }
});

module.exports = router;
