
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Landing Page Dark Theme */
    --background: 0 0% 6.7%; /* #111111 */
    --foreground: 0 0% 98%;

    --card: 0 0% 8%; /* Darker cards */
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 8%;
    --popover-foreground: 0 0% 98%;

    --primary: 142 76% 46%; /* Logo green */
    --primary-foreground: 0 0% 98%;
    --primary-glow: 142 76% 60%;

    /* Accent green for highlights */
    --accent-green: 142 76% 62%; /* #39E59E */
    --accent-green-glow: 142 76% 70%;

    --secondary: 240 8% 18%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 8% 18%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 142 76% 46%;
    --accent-foreground: 240 15% 8%;
    
    --success: 142 76% 46%;
    --success-foreground: 240 15% 8%;

    /* Logo Brand Colors - Green & Yellow-Orange */
    --logo-green: 142 76% 46%;
    --logo-green-light: 142 76% 56%;
    --logo-green-dark: 142 76% 36%;
    --logo-yellow-orange: 45 96% 54%;
    --logo-yellow-orange-light: 45 96% 64%;
    --logo-yellow-orange-dark: 45 96% 44%;

    /* Brand colors using logo colors */
    --brand-emerald: 142 76% 46%;
    --brand-dark: 240 15% 8%;
    --brand-navy: 240 20% 12%;
    --brand-charcoal: 240 10% 15%;
    --brand-accent: 45 96% 54%;
    --brand-glow: 142 76% 60%;

    /* Glass morphism colors */
    --glass-bg: 240 15% 8% / 0.8;
    --glass-border: 142 76% 46% / 0.2;
    --glass-highlight: 0 0% 100% / 0.1;
    
    /* Enhanced Gradients */
    --gradient-hero: linear-gradient(135deg, hsl(240, 20%, 12%) 0%, hsl(240, 15%, 8%) 50%, hsl(240, 10%, 15%) 100%);
    --gradient-primary: linear-gradient(135deg, hsl(143, 49%, 29%), hsl(142, 69%, 58%));
    --gradient-accent: linear-gradient(135deg, hsl(142, 76%, 46%), hsl(142, 69%, 58%));
    --gradient-glow: radial-gradient(circle at 50% 50%, hsl(142, 76%, 46% / 0.3) 0%, transparent 70%);

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 8% 20%;
    --input: 240 8% 18%;
    --ring: 142 76% 46%;
    
    /* Glass morphism shadows */
    --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --shadow-glow: 0 0 40px hsl(142, 76%, 46% / 0.4);
    --shadow-elegant: 0 25px 50px -12px hsl(142, 76%, 46% / 0.25);

    --radius: 0.75rem;
  }

  .dark {
    --background: 240 15% 6%;
    --foreground: 0 0% 98%;

    --card: 240 12% 10%;
    --card-foreground: 0 0% 98%;

    --popover: 240 12% 10%;
    --popover-foreground: 0 0% 98%;

    --primary: 142 76% 50%;
    --primary-foreground: 240 15% 6%;
    --primary-glow: 142 76% 65%;

    --secondary: 240 8% 16%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 8% 16%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 142 76% 50%;
    --accent-foreground: 240 15% 6%;
    
    --success: 142 76% 50%;
    --success-foreground: 240 15% 6%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 8% 18%;
    --input: 240 8% 16%;
    --ring: 142 76% 50%;
    
    /* Enhanced Glass morphism for dark mode */
    --glass-bg: 240 15% 6% / 0.9;
    --glass-border: 142 76% 50% / 0.3;
    --glass-highlight: 0 0% 100% / 0.15;
    
    /* Dark theme borders */
    --card-border: 240 8% 18%;
    --button-border: 240 8% 18%;
  }

  /* Light theme */
  .light {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 142 76% 36%;
    --primary-foreground: 355.7 100% 97.3%;
    --primary-glow: 142 76% 46%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;

    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 142 76% 36%;
    --accent-foreground: 355.7 100% 97.3%;

    --success: 142 76% 36%;
    --success-foreground: 355.7 100% 97.3%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 142 76% 36%;

    /* Light theme glass morphism */
    --glass-bg: 0 0% 100% / 0.8;
    --glass-border: 142 76% 36% / 0.2;
    --glass-highlight: 0 0% 100% / 0.9;

    /* Light theme borders */
    --card-border: 214.3 31.8% 91.4%;
    --button-border: 214.3 31.8% 91.4%;

    /* Brand colors for light mode */
    --brand-emerald: 142 76% 36%;
    --brand-dark: 222.2 84% 4.9%;
    --brand-navy: 210 40% 20%;
    --brand-charcoal: 215.4 16.3% 25%;
    --brand-accent: 142 69% 45%;
    --brand-glow: 142 76% 46%;

    /* Light theme gradients */
    --gradient-hero: linear-gradient(135deg, hsl(0, 0%, 100%) 0%, hsl(210, 40%, 98%) 50%, hsl(210, 40%, 96%) 100%);
    --gradient-primary: linear-gradient(135deg, hsl(142, 76%, 36%), hsl(142, 69%, 45%));
    --gradient-accent: linear-gradient(135deg, hsl(142, 76%, 36%), hsl(142, 69%, 45%));
    --gradient-glow: radial-gradient(circle at 50% 50%, hsl(142, 76%, 36% / 0.1) 0%, transparent 70%);

    /* Chart colors for light mode */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Yellow-orange for light mode */
    --yellow-orange: 45 96% 54%;
    --yellow-orange-light: 45 96% 64%;
    --yellow-orange-dark: 45 96% 44%;
  }
  
  body {
    @apply font-inter;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-inter font-semibold tracking-tight;
  }

  nav a, .nav-link, .sidebar a {
    @apply font-inter font-medium;
  }

  button, .btn {
    @apply font-inter font-medium;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
  }

  html {
    @apply scroll-smooth;
  }

  /* Force all buttons and form fields to have 0px border radius */
  button, .btn, [role="button"], input, textarea, select, .form-field {
    border-radius: 0px !important;
  }

  /* Ads banner flash animation */
  @keyframes flash {
    0%, 100% { 
      opacity: 1; 
      transform: scale(1);
    }
    50% { 
      opacity: 0.8; 
      transform: scale(1.02);
    }
  }

  .ads-flash {
    animation: flash 2s ease-in-out infinite;
  }

  /* Light theme balance card green override */
  .light .balance-card-green {
    background: linear-gradient(135deg, #16a34a, #15803d) !important;
  }
}

@layer utilities {
  /* Neumorphism Design System */
  .neumorphism-base {
    @apply rounded-2xl bg-background/95 backdrop-blur-sm;
    @apply shadow-[8px_8px_16px_rgba(0,0,0,0.1),-8px_-8px_16px_rgba(255,255,255,0.1)];
    @apply dark:shadow-[8px_8px_16px_rgba(0,0,0,0.3),-8px_-8px_16px_rgba(255,255,255,0.02)];
    @apply border border-white/20 dark:border-white/5;
  }

  .neumorphism-hover {
    @apply hover:shadow-[12px_12px_24px_rgba(0,0,0,0.15),-12px_-12px_24px_rgba(255,255,255,0.15)];
    @apply dark:hover:shadow-[12px_12px_24px_rgba(0,0,0,0.4),-12px_-12px_24px_rgba(255,255,255,0.03)];
  }

  .neumorphism-pressed {
    @apply active:shadow-[4px_4px_8px_rgba(0,0,0,0.2),-4px_-4px_8px_rgba(255,255,255,0.05)];
    @apply active:scale-[0.98];
  }

  /* Logo Color Neumorphic Buttons */
  .btn-logo-green {
    background: linear-gradient(145deg, hsl(142, 76%, 46%), hsl(142, 76%, 40%));
    border: 1px solid hsl(45, 96%, 54%);
    color: white;
    box-shadow:
      inset 4px 4px 8px rgba(255, 255, 255, 0.1),
      inset -4px -4px 8px rgba(0, 0, 0, 0.2),
      0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
  }

  .btn-logo-green:hover {
    background: linear-gradient(145deg, hsl(142, 76%, 50%), hsl(142, 76%, 44%));
    box-shadow:
      inset 6px 6px 12px rgba(255, 255, 255, 0.15),
      inset -6px -6px 12px rgba(0, 0, 0, 0.25),
      0 6px 16px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
  }

  .btn-logo-green:active {
    background: linear-gradient(145deg, hsl(142, 76%, 40%), hsl(142, 76%, 36%));
    box-shadow:
      inset 8px 8px 16px rgba(0, 0, 0, 0.3),
      inset -8px -8px 16px rgba(255, 255, 255, 0.05),
      0 2px 8px rgba(0, 0, 0, 0.25);
    transform: translateY(1px) scale(0.98);
  }

  /* Neumorphic Inward Effect Button */
  .btn-neumorphic-inward {
    background: linear-gradient(145deg, hsl(142, 76%, 46%), hsl(142, 76%, 40%));
    border: 1px solid hsl(45, 96%, 54%);
    color: white;
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
    font-size: 14px;
    position: relative;
    overflow: hidden;

    /* 3D Inward Effect */
    box-shadow:
      inset 4px 4px 8px rgba(0, 0, 0, 0.15),
      inset -4px -4px 8px rgba(255, 255, 255, 0.1),
      0 2px 8px rgba(0, 0, 0, 0.1);

    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Large Neumorphic Button for Hero */
  .btn-neumorphic-large {
    background: linear-gradient(145deg, hsl(142, 76%, 46%), hsl(142, 76%, 40%));
    border: 1px solid hsl(45, 96%, 54%);
    color: white;
    border-radius: 12px;
    padding: 16px 32px;
    font-weight: 600;
    font-size: 16px;
    position: relative;
    overflow: hidden;

    /* 3D Inward Effect */
    box-shadow:
      inset 6px 6px 12px rgba(0, 0, 0, 0.2),
      inset -6px -6px 12px rgba(255, 255, 255, 0.1),
      0 4px 12px rgba(0, 0, 0, 0.15);

    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .btn-neumorphic-inward:hover {
    background: linear-gradient(145deg, hsl(142, 76%, 50%), hsl(142, 76%, 44%));
    box-shadow:
      inset 8px 8px 16px rgba(0, 0, 0, 0.25),
      inset -8px -8px 16px rgba(255, 255, 255, 0.15),
      0 6px 16px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
  }

  .btn-neumorphic-inward:active {
    background: linear-gradient(145deg, hsl(142, 76%, 40%), hsl(142, 76%, 36%));
    box-shadow:
      inset 12px 12px 24px rgba(0, 0, 0, 0.35),
      inset -12px -12px 24px rgba(255, 255, 255, 0.05),
      0 2px 8px rgba(0, 0, 0, 0.3);
    transform: translateY(2px) scale(0.97);
  }

  /* Yellow-Orange Accent Elements */
  .accent-yellow-orange {
    color: hsl(45, 96%, 54%);
  }

  .bg-yellow-orange {
    background-color: hsl(45, 96%, 54%);
  }

  .border-yellow-orange {
    border-color: hsl(45, 96%, 54%);
  }

  /* Landing Page Specific Styles */
  .landing-bg {
    background: #39E59E;
    position: relative;
  }

  .landing-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    pointer-events: none;
    z-index: 0;
  }

  /* Animated Hub Styles */
  .hub-glow {
    box-shadow: 0 0 20px rgba(57, 229, 158, 0.5);
    animation: pulse-glow 2s ease-in-out infinite alternate;
  }

  .feature-node {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
  }

  .feature-node:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.4);
  }

  .connecting-line {
    stroke: #ffffff;
    stroke-width: 2;
    stroke-dasharray: 5, 5;
    filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.5));
    animation: dash-flow 3s linear infinite;
  }

  @keyframes pulse-glow {
    0% { box-shadow: 0 0 20px rgba(57, 229, 158, 0.5); }
    100% { box-shadow: 0 0 30px rgba(57, 229, 158, 0.8); }
  }

  @keyframes dash-flow {
    0% { stroke-dashoffset: 0; }
    100% { stroke-dashoffset: 20; }
  }

  /* Package Cards */
  .package-card {
    background: rgba(0, 0, 0, 0.6);
    border: 1px solid rgba(57, 229, 158, 0.2);
    position: relative;
  }

  .package-card::before {
    content: '';
    position: absolute;
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    border: 1px dotted rgba(57, 229, 158, 0.4);
    border-radius: inherit;
    pointer-events: none;
  }

  .package-card.starter {
    border-top: 3px solid #39E59E;
  }

  .package-card.plus {
    border-top: 3px solid #3B82F6;
  }

  .package-card.premium {
    border-top: 3px solid #8B5CF6;
  }

  .glass-card {
    @apply neumorphism-base neumorphism-hover neumorphism-pressed transition-all duration-300;
  }
  
  .text-balance {
    text-wrap: balance;
  }
  
  .dark-transition {
    @apply transition-colors duration-300;
  }
  
  .fintech-gradient {
    @apply bg-gradient-to-br from-fintech-dark via-fintech-card to-fintech-dark;
  }
  
  .fintech-card-gradient {
    @apply bg-gradient-to-br from-fintech-card to-fintech-card/50 backdrop-blur-md border border-white/10;
  }
  
  .savings-goal-card {
    @apply p-4 rounded-lg border transition-all duration-300;
    @apply bg-gradient-to-br from-card to-accent/5;
    @apply dark:from-card dark:to-primary/5;
    @apply hover:shadow-lg hover:scale-[1.02];
    @apply border-border dark:border-border;
  }
  
  .savings-goal-text {
    @apply text-card-foreground dark:text-card-foreground;
  }
  
  .savings-goal-muted {
    @apply text-muted-foreground dark:text-muted-foreground;
  }
  
  /* Enhanced 3D Button Styles */
  .btn-3d {
    @apply relative overflow-hidden transition-all duration-300 transform-gpu will-change-transform;
    @apply shadow-[0_8px_32px_rgba(0,0,0,0.12),inset_0_1px_0_rgba(255,255,255,0.15)];
    @apply hover:shadow-[0_12px_40px_rgba(0,0,0,0.18),inset_0_2px_0_rgba(255,255,255,0.2)];
    @apply active:shadow-[0_4px_16px_rgba(0,0,0,0.2),inset_0_4px_8px_rgba(0,0,0,0.15)];
    border: 2px solid hsl(var(--primary));
    border-radius: 24px;
    @apply bg-gradient-to-b from-primary via-primary/95 to-primary/90;
    @apply hover:translate-y-[-2px] hover:scale-[1.02];
    @apply active:translate-y-[1px] active:scale-[0.98];
  }
  
  /* Enhanced Mobile Utilities */
  .mobile-safe-area {
    @apply px-3 sm:px-4 md:px-6 lg:px-8 xl:px-12;
    @apply py-3 sm:py-4 md:py-6 lg:py-8;
  }
  
  .mobile-grid {
    @apply grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5;
    @apply gap-2 sm:gap-3 md:gap-4 lg:gap-6 xl:gap-8;
  }
  
  .mobile-button {
    @apply w-full sm:w-auto min-h-[44px] sm:min-h-[48px] md:min-h-[52px] lg:min-h-[56px];
    @apply text-sm sm:text-base md:text-sm lg:text-base;
    @apply touch-manipulation select-none;
    @apply rounded-xl sm:rounded-lg;
  }

  /* Enhanced Mobile Cards */
  .mobile-card {
    @apply p-3 sm:p-4 md:p-5 lg:p-6;
    @apply rounded-lg sm:rounded-xl md:rounded-2xl;
    @apply text-sm sm:text-base;
  }

  .mobile-card-compact {
    @apply p-2 sm:p-3 md:p-4;
    @apply rounded-md sm:rounded-lg md:rounded-xl;
    @apply text-xs sm:text-sm md:text-base;
  }
  
  /* Mobile Navigation Bar for Dashboard Actions */
  .mobile-nav-bar {
    @apply fixed bottom-0 left-0 right-0 z-50 md:hidden;
    @apply bg-background/95 backdrop-blur-md border-t border-border;
    @apply pb-6 pt-2;
  }

  .mobile-nav-grid {
    @apply grid grid-cols-4 gap-1 px-2 pt-2;
  }

  .mobile-nav-item {
    @apply flex flex-col items-center justify-center p-2 rounded-xl;
    @apply text-xs font-medium transition-all duration-200;
    @apply neumorphism-base neumorphism-hover neumorphism-pressed;
    @apply min-h-[60px] active:scale-95;
  }

  .mobile-nav-icon {
    @apply w-5 h-5 mb-1;
  }

  /* Enhanced Responsive Utilities */
  .responsive-container {
  }
  
  .responsive-text {
    @apply text-sm sm:text-base lg:text-lg xl:text-xl;
  }
  
  .responsive-heading {
    @apply text-xl sm:text-2xl lg:text-3xl xl:text-4xl 2xl:text-5xl;
  }
  
  .responsive-card {
    @apply p-4 sm:p-6 lg:p-8 xl:p-10;
    @apply rounded-xl sm:rounded-2xl lg:rounded-3xl;
  }
  
  /* Enhanced Animation Utilities */
  .animate-scale-in {
    animation: scale-in 0.3s ease-out;
  }
  
  .animate-fade-in {
    animation: fade-in 0.5s ease-out;
  }
  
  .animate-slide-up {
    animation: slide-up 0.4s ease-out;
  }
  
  @keyframes scale-in {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes slide-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

@layer components {
  .nav-link {
    @apply relative px-4 py-2 flex items-center gap-2 rounded-none transition-all duration-300;
    @apply hover:bg-primary/10 hover:shadow-[0_4px_16px_rgba(0,0,0,0.1)];
    @apply active:scale-95;
  }
  
  .nav-link.active {
    @apply bg-primary/15 text-primary shadow-[0_4px_16px_rgba(var(--primary),0.2)];
  }

  .dashboard-card {
    @apply neumorphism-base neumorphism-hover neumorphism-pressed;
    @apply p-4 sm:p-6 transition-all duration-300;
    @apply hover:translate-y-[-2px];
  }
  
  .stat-card {
    @apply neumorphism-base neumorphism-hover neumorphism-pressed;
    @apply p-4 sm:p-5 text-card-foreground flex flex-col transition-all duration-300;
    @apply hover:translate-y-[-2px];
  }
  
  .btn-brand {
    @apply bg-primary text-white hover:bg-primary/90 transition-all duration-200;
    @apply shadow-[0_6px_20px_rgba(34,197,94,0.3)] hover:shadow-[0_8px_28px_rgba(34,197,94,0.4)];
    @apply hover:translate-y-[-1px] active:translate-y-[0.5px];
  }
  
  .btn-accent {
    @apply bg-brand-yellow text-foreground hover:bg-brand-yellow/90 transition-all duration-200;
    @apply shadow-[0_6px_20px_rgba(245,158,11,0.3)] hover:shadow-[0_8px_28px_rgba(245,158,11,0.4)];
    @apply hover:translate-y-[-1px] active:translate-y-[0.5px];
  }

  .kola-card {
    @apply rounded-2xl border border-border bg-gradient-to-br from-white to-green-50;
    @apply shadow-[0_8px_32px_rgba(0,0,0,0.08)] transition-all duration-300;
    @apply hover:shadow-[0_12px_40px_rgba(0,0,0,0.12)] hover:translate-y-[-4px];
    @apply dark:from-gray-900 dark:to-gray-800;
  }
  
  .asusu-gradient {
    @apply bg-gradient-to-br from-primary to-green-700 text-white;
  }
  
  .title-font {
    @apply font-sans tracking-wider uppercase;
  }
  
  .currency-symbol {
    @apply font-medium;
  }
  
  /* Enhanced Focus States */
  .focus-enhanced {
    @apply focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2;
    @apply focus:shadow-[0_0_0_3px_rgba(var(--primary),0.1)];
  }

  /* Light theme 3D accent borders */
  .light .card, .light .btn, .light button {
    border: 1px solid hsl(var(--card-border));
    box-shadow: var(--shadow-3d);
    transition: all 0.2s ease;
  }

  .light .card:hover, .light .btn:hover, .light button:hover {
    box-shadow: var(--shadow-3d-hover);
    transform: translateY(-1px);
  }

  /* Enhanced Page Backgrounds without Circle Fades */
  .page-with-circles {
    background: transparent;
  }

  /* Enhanced Card Backgrounds without Yellow-Orange */
  .card-with-circles {
    background: transparent;
    position: relative;
  }

  /* Yellow-Orange Border Enhancement */
  .yellow-orange-border {
    border: 1px solid hsl(var(--yellow-orange)) !important;
    box-shadow: 0 0 10px hsl(var(--yellow-orange) / 0.3);
  }

  /* Black-Green Gradient */
  .black-green-gradient {
    background: var(--gradient-black-green) !important;
  }

  /* Mobile page transitions */
  @media (max-width: 640px) {
    .mobile-page-transition {
      animation: slideUpFromBase 0.3s ease-out;
    }
  }

  @keyframes slideUpFromBase {
    from {
      opacity: 0;
      transform: translateY(100%);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Custom animations for glass morphism */
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
  }

  @keyframes bounce-gentle {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-8px) scale(1.1); }
  }

  @keyframes glow-pulse {
    0%, 100% { 
      box-shadow: 0 0 20px hsl(142, 76%, 46% / 0.4), 0 0 40px hsl(142, 76%, 46% / 0.2);
    }
    50% { 
      box-shadow: 0 0 30px hsl(142, 76%, 46% / 0.6), 0 0 60px hsl(142, 76%, 46% / 0.3);
    }
  }

  .animate-float {
    animation: float 4s ease-in-out infinite;
  }

  .animate-bounce-gentle {
    animation: bounce-gentle 3s ease-in-out infinite;
  }

  .animate-glow-pulse {
    animation: glow-pulse 3s ease-in-out infinite;
  }

  /* Glass morphism utilities */
  .glass-morphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .glass-button {
    background: rgba(34, 197, 94, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(34, 197, 94, 0.3);
    transition: all 0.3s ease;
  }

  .glass-button:hover {
    background: rgba(34, 197, 94, 0.3);
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.4);
  }
}

/* Enhanced Mobile Breakpoints */
@media (max-width: 640px) {
  .mobile-stack {
    @apply flex-col space-y-4 space-x-0;
  }
  
  .mobile-full {
    @apply w-full;
  }
  
  .mobile-center {
    @apply text-center;
  }
}

/* Enhanced Tablet Breakpoints */
@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-grid {
    @apply grid-cols-2;
  }
  
  .tablet-spacing {
    @apply gap-6;
  }
}

/* Enhanced Desktop Breakpoints */
@media (min-width: 1025px) {
  .desktop-grid {
    @apply grid-cols-3;
  }
  
  .desktop-spacing {
    @apply gap-8;
  }
}
