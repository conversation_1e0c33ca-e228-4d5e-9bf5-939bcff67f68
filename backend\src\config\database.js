const mongoose = require('mongoose');

class DatabaseManager {
  constructor() {
    this.isConnected = false;
    this.connectionAttempts = 0;
    this.maxRetries = 5;
    this.retryDelay = 5000; // 5 seconds
    this.healthCheckInterval = null;
  }

  async connectDB() {
    try {
      // Enhanced connection options for production
      const options = {
        useNewUrlParser: true,
        useUnifiedTopology: true,

        // Connection pool settings
        maxPoolSize: 10, // Maximum number of connections
        minPoolSize: 2,  // Minimum number of connections
        maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
        serverSelectionTimeoutMS: 5000, // How long to try selecting a server
        socketTimeoutMS: 45000, // How long a send or receive on a socket can take

        // Buffering settings (deprecated options removed)
        bufferCommands: false, // Disable mongoose buffering

        // Write concern
        writeConcern: {
          w: 'majority',
          j: true, // Request acknowledgment that write operations have been written to the journal
          wtimeout: 10000 // Time limit for write concern
        },

        // Read preference
        readPreference: 'primary',
        readConcern: { level: 'local' },

        // Heartbeat settings
        heartbeatFrequencyMS: 10000, // How often to check server status

        // Compression
        compressors: ['zlib'],

        // Authentication
        authSource: 'admin',

        // SSL/TLS (enable in production)
        tls: process.env.NODE_ENV === 'production',
        tlsAllowInvalidCertificates: process.env.NODE_ENV !== 'production'
      };

      const conn = await mongoose.connect(process.env.MONGO_URI, options);

      this.isConnected = true;
      this.connectionAttempts = 0;

      console.log(`✅ MongoDB Connected: ${conn.connection.host}`);
      console.log(`📊 Connection Pool Size: ${options.maxPoolSize}`);
      console.log(`🔒 TLS Enabled: ${options.tls ? 'Yes' : 'No'}`);

      // Setup connection event listeners
      this.setupEventListeners();

      // Start health monitoring
      this.startHealthMonitoring();

      // Setup graceful shutdown
      this.setupGracefulShutdown();

    } catch (error) {
      this.isConnected = false;
      this.connectionAttempts++;

      console.error(`❌ Database connection failed (attempt ${this.connectionAttempts}/${this.maxRetries}):`, error.message);

      if (this.connectionAttempts < this.maxRetries) {
        console.log(`🔄 Retrying connection in ${this.retryDelay / 1000} seconds...`);
        setTimeout(() => this.connectDB(), this.retryDelay);
      } else {
        console.error('❌ Max connection attempts reached. Exiting...');
        process.exit(1);
      }
    }
  }

  setupEventListeners() {
    const connection = mongoose.connection;

    // Connection opened
    connection.on('connected', () => {
      console.log('🔗 MongoDB connection established');
      this.isConnected = true;
    });

    // Connection error
    connection.on('error', (err) => {
      console.error('❌ MongoDB connection error:', err);
      this.isConnected = false;
    });

    // Connection disconnected
    connection.on('disconnected', () => {
      console.log('📡 MongoDB disconnected');
      this.isConnected = false;

      // Attempt to reconnect
      if (this.connectionAttempts < this.maxRetries) {
        setTimeout(() => this.connectDB(), this.retryDelay);
      }
    });

    // Connection reconnected
    connection.on('reconnected', () => {
      console.log('🔄 MongoDB reconnected');
      this.isConnected = true;
      this.connectionAttempts = 0;
    });

    // Connection timeout
    connection.on('timeout', () => {
      console.warn('⏰ MongoDB connection timeout');
    });

    // Connection close
    connection.on('close', () => {
      console.log('🔒 MongoDB connection closed');
      this.isConnected = false;
    });

    // Full setup
    connection.on('fullsetup', () => {
      console.log('🎯 MongoDB replica set fully connected');
    });

    // All connections closed
    connection.on('all', () => {
      console.log('📋 All MongoDB connections established');
    });
  }

  startHealthMonitoring() {
    // Health check every 30 seconds
    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        console.error('❌ Database health check failed:', error.message);
      }
    }, 30000);
  }

  async performHealthCheck() {
    if (!this.isConnected) return;

    try {
      // Simple ping to check connection
      await mongoose.connection.db.admin().ping();

      // Get connection stats
      const stats = await this.getConnectionStats();

      // Log stats periodically (every 5 minutes)
      if (Date.now() % 300000 < 30000) {
        console.log('📊 Database Health:', {
          connected: this.isConnected,
          readyState: mongoose.connection.readyState,
          ...stats
        });
      }
    } catch (error) {
      console.error('❌ Health check ping failed:', error.message);
      this.isConnected = false;
    }
  }

  async getConnectionStats() {
    try {
      const db = mongoose.connection.db;
      const admin = db.admin();

      // Get server status
      const serverStatus = await admin.serverStatus();

      return {
        connections: serverStatus.connections,
        uptime: serverStatus.uptime,
        version: serverStatus.version,
        host: serverStatus.host
      };
    } catch (error) {
      return { error: error.message };
    }
  }

  setupGracefulShutdown() {
    const gracefulShutdown = async (signal) => {
      console.log(`\n🔄 Received ${signal}. Starting graceful shutdown...`);

      try {
        // Clear health check interval
        if (this.healthCheckInterval) {
          clearInterval(this.healthCheckInterval);
        }

        // Close database connection
        await this.disconnectDB();

        console.log('✅ Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        console.error('❌ Error during graceful shutdown:', error);
        process.exit(1);
      }
    };

    // Handle different termination signals
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // Nodemon restart

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('❌ Uncaught Exception:', error);
      gracefulShutdown('uncaughtException');
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
      gracefulShutdown('unhandledRejection');
    });
  }

  async disconnectDB() {
    try {
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }

      await mongoose.connection.close();
      this.isConnected = false;
      console.log('🔒 MongoDB Disconnected');
    } catch (error) {
      console.error('❌ Database disconnection error:', error);
      throw error;
    }
  }

  // Get connection status
  getStatus() {
    return {
      isConnected: this.isConnected,
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      name: mongoose.connection.name,
      connectionAttempts: this.connectionAttempts
    };
  }

  // Force reconnection
  async forceReconnect() {
    console.log('🔄 Forcing database reconnection...');
    await this.disconnectDB();
    await this.connectDB();
  }
}

// Create singleton instance
const databaseManager = new DatabaseManager();

// Export methods for backward compatibility
const connectDB = () => databaseManager.connectDB();
const disconnectDB = () => databaseManager.disconnectDB();
const getDBStatus = () => databaseManager.getStatus();
const forceReconnect = () => databaseManager.forceReconnect();

module.exports = {
  connectDB,
  disconnectDB,
  getDBStatus,
  forceReconnect,
  databaseManager
};
