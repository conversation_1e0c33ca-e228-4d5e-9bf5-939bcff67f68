import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { FileText, Shield, Scale, ExternalLink, Eye, Download } from 'lucide-react';
import { PrivacyPolicyModal } from '@/components/legal/PrivacyPolicyModal';
import { TermsOfServiceModal } from '@/components/legal/TermsOfServiceModal';
import { PolicyModal } from '@/components/modals/PolicyModal';

export const LegalPage = () => {
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [showPolicyModal, setShowPolicyModal] = useState(false);

  const legalDocuments = [
    {
      id: 'privacy',
      title: 'Privacy Policy',
      description: 'Learn how we collect, use, and protect your personal information.',
      icon: <Shield className="h-6 w-6 text-blue-500" />,
      lastUpdated: '2024-01-15',
      summary: 'Our privacy policy outlines how Better Interest handles your personal data, including financial information, usage analytics, and communication preferences.',
      keyPoints: [
        'Data collection and usage practices',
        'Information sharing and third-party services',
        'Security measures and data protection',
        'Your rights and control over personal data',
        'Cookie policy and tracking technologies'
      ],
      onClick: () => setShowPrivacyModal(true)
    },
    {
      id: 'terms',
      title: 'Terms of Service',
      description: 'Understand the terms and conditions for using Better Interest.',
      icon: <FileText className="h-6 w-6 text-green-500" />,
      lastUpdated: '2024-01-10',
      summary: 'Our terms of service define the legal agreement between you and Better Interest, covering service usage, user responsibilities, and platform policies.',
      keyPoints: [
        'User account responsibilities and obligations',
        'Service availability and limitations',
        'Financial services terms and conditions',
        'Prohibited activities and enforcement',
        'Liability limitations and dispute resolution'
      ],
      onClick: () => setShowTermsModal(true)
    },
    {
      id: 'policy',
      title: 'User Agreement & Policies',
      description: 'Comprehensive policies covering savings plans, group activities, and user conduct.',
      icon: <Scale className="h-6 w-6 text-purple-500" />,
      lastUpdated: '2024-01-20',
      summary: 'Detailed policies covering specific features like group savings, referral programs, and financial product terms.',
      keyPoints: [
        'Group savings participation rules',
        'Referral program terms and rewards',
        'KYC verification requirements',
        'Payment processing and refund policies',
        'Account suspension and termination'
      ],
      onClick: () => setShowPolicyModal(true)
    }
  ];

  const complianceInfo = [
    {
      title: 'Regulatory Compliance',
      description: 'Better Interest operates in compliance with Nigerian financial regulations and international standards.',
      items: [
        'Central Bank of Nigeria (CBN) guidelines',
        'Nigeria Data Protection Regulation (NDPR)',
        'Anti-Money Laundering (AML) compliance',
        'Know Your Customer (KYC) requirements'
      ]
    },
    {
      title: 'Security Standards',
      description: 'We implement industry-standard security measures to protect your data and funds.',
      items: [
        'SSL/TLS encryption for all communications',
        'Multi-factor authentication support',
        'Regular security audits and penetration testing',
        'Compliance with PCI DSS standards'
      ]
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl sm:text-4xl font-bold mb-4">Legal Information</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Transparency and compliance are core to our operations. Find all legal documents and policies here.
        </p>
      </div>

      <Tabs defaultValue="documents" className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-8">
          <TabsTrigger value="documents" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Legal Documents
          </TabsTrigger>
          <TabsTrigger value="compliance" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Compliance & Security
          </TabsTrigger>
        </TabsList>

        <TabsContent value="documents" className="space-y-6">
          {/* Main Legal Documents */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {legalDocuments.map((doc) => (
              <Card key={doc.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {doc.icon}
                      <div>
                        <CardTitle className="text-lg">{doc.title}</CardTitle>
                        <Badge variant="outline" className="text-xs">
                          Updated {doc.lastUpdated}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <CardDescription className="mt-2">
                    {doc.description}
                  </CardDescription>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    {doc.summary}
                  </p>
                  
                  <div>
                    <h4 className="font-medium text-sm mb-2">Key Topics:</h4>
                    <ul className="text-xs text-muted-foreground space-y-1">
                      {doc.keyPoints.map((point, index) => (
                        <li key={index}>• {point}</li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="flex gap-2 pt-2">
                    <Button 
                      onClick={doc.onClick}
                      size="sm"
                      className="flex items-center gap-2 flex-1"
                    >
                      <Eye className="h-3 w-3" />
                      View Document
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      <Download className="h-3 w-3" />
                      PDF
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Quick Links */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ExternalLink className="h-5 w-5" />
                Quick Links & Resources
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <Button variant="outline" className="justify-start">
                  Cookie Policy
                </Button>
                <Button variant="outline" className="justify-start">
                  DMCA Notice
                </Button>
                <Button variant="outline" className="justify-start">
                  Accessibility Statement
                </Button>
                <Button variant="outline" className="justify-start">
                  Contact Legal Team
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-6">
          {/* Compliance Information */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {complianceInfo.map((info, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle>{info.title}</CardTitle>
                  <CardDescription>{info.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {info.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-center gap-2 text-sm">
                        <div className="h-2 w-2 bg-primary rounded-full" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle>Legal & Compliance Contact</CardTitle>
              <CardDescription>
                For legal inquiries, compliance questions, or data protection concerns
              </CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <h4 className="font-medium text-sm mb-2">Legal Team</h4>
                <p className="text-sm text-muted-foreground"><EMAIL></p>
              </div>
              <div>
                <h4 className="font-medium text-sm mb-2">Data Protection Officer</h4>
                <p className="text-sm text-muted-foreground"><EMAIL></p>
              </div>
              <div>
                <h4 className="font-medium text-sm mb-2">Compliance</h4>
                <p className="text-sm text-muted-foreground"><EMAIL></p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Modals */}
      <PrivacyPolicyModal 
        open={showPrivacyModal} 
        onOpenChange={setShowPrivacyModal}
      />
      <TermsOfServiceModal 
        open={showTermsModal} 
        onOpenChange={setShowTermsModal}
      />
      <PolicyModal
        open={showPolicyModal}
        onOpenChange={setShowPolicyModal}
        onAccept={() => setShowPolicyModal(false)}
        title="User Agreement & Policies"
        type="general"
      />
    </div>
  );
};

export default LegalPage;