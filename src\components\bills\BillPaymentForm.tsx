import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Phone, Zap, Tv, Wifi, Plane, GraduationCap } from 'lucide-react';
import axios from 'axios';

interface BillProvider {
  _id: string;
  name: string;
  category: string;
  fee: number;
  feeType: 'fixed' | 'percentage';
  minAmount: number;
  maxAmount: number;
}

interface CustomerInfo {
  identifier: string;
  name: string;
  address?: string;
  isValid: boolean;
}

const categoryIcons = {
  airtime: Phone,
  data: Wifi,
  electricity: Zap,
  cable_tv: Tv,
  internet: Wifi,
  flight: Plane,
  education: GraduationCap,
  betting: Phone
};

export const BillPaymentForm = () => {
  const [providers, setProviders] = useState<BillProvider[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedProvider, setSelectedProvider] = useState<BillProvider | null>(null);
  const [amount, setAmount] = useState('');
  const [customerIdentifier, setCustomerIdentifier] = useState('');
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo | null>(null);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchProviders();
  }, []);

  const fetchProviders = async () => {
    try {
      const response = await axios.get('/api/v1/bills/providers');
      setProviders(response.data.data);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch bill providers',
        variant: 'destructive'
      });
    }
  };

  const verifyCustomer = async () => {
    if (!selectedProvider || !customerIdentifier.trim()) return;

    setIsVerifying(true);
    try {
      const response = await axios.post('/api/v1/bills/verify-customer', {
        providerId: selectedProvider._id,
        customerIdentifier: customerIdentifier.trim(),
        category: selectedCategory
      });

      setCustomerInfo(response.data.data);
      toast({
        title: 'Customer Verified',
        description: `Customer: ${response.data.data.name}`,
      });
    } catch (error: any) {
      toast({
        title: 'Verification Failed',
        description: error.response?.data?.message || 'Failed to verify customer',
        variant: 'destructive'
      });
      setCustomerInfo(null);
    } finally {
      setIsVerifying(false);
    }
  };

  const calculateFee = () => {
    if (!selectedProvider || !amount) return 0;
    return selectedProvider.feeType === 'percentage'
      ? (parseFloat(amount) * selectedProvider.fee) / 100
      : selectedProvider.fee;
  };

  const getTotalAmount = () => {
    const amountValue = parseFloat(amount) || 0;
    return amountValue + calculateFee();
  };

  const handlePayment = async () => {
    if (!selectedProvider || !amount || !customerInfo) return;

    setIsProcessing(true);
    try {
      const response = await axios.post('/api/v1/bills/pay', {
        providerId: selectedProvider._id,
        amount: parseFloat(amount),
        customerIdentifier: customerIdentifier.trim(),
        category: selectedCategory,
        metadata: {
          customerName: customerInfo.name
        }
      });

      toast({
        title: 'Payment Successful',
        description: `Bill payment completed. Reference: ${response.data.data.reference}`,
      });

      // Reset form
      setAmount('');
      setCustomerIdentifier('');
      setCustomerInfo(null);
      setSelectedProvider(null);
      setSelectedCategory('');
    } catch (error: any) {
      toast({
        title: 'Payment Failed',
        description: error.response?.data?.message || 'Failed to process payment',
        variant: 'destructive'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const filteredProviders = providers.filter(p => p.category === selectedCategory);
  const categories = [...new Set(providers.map(p => p.category))];

  const getPlaceholderText = () => {
    switch (selectedCategory) {
      case 'airtime':
      case 'data':
        return 'Enter phone number (e.g., 08012345678)';
      case 'electricity':
        return 'Enter meter number';
      case 'cable_tv':
        return 'Enter decoder number';
      default:
        return 'Enter customer identifier';
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          Bill Payment
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Category Selection */}
        <div className="space-y-2">
          <Label htmlFor="category">Service Category</Label>
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger>
              <SelectValue placeholder="Select service category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => {
                const Icon = categoryIcons[category as keyof typeof categoryIcons];
                return (
                  <SelectItem key={category} value={category}>
                    <div className="flex items-center gap-2">
                      <Icon className="h-4 w-4" />
                      <span className="capitalize">{category.replace('_', ' ')}</span>
                    </div>
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>

        {/* Provider Selection */}
        {selectedCategory && (
          <div className="space-y-2">
            <Label htmlFor="provider">Service Provider</Label>
            <Select 
              value={selectedProvider?._id || ''} 
              onValueChange={(id) => {
                const provider = filteredProviders.find(p => p._id === id);
                setSelectedProvider(provider || null);
                setCustomerInfo(null);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select service provider" />
              </SelectTrigger>
              <SelectContent>
                {filteredProviders.map((provider) => (
                  <SelectItem key={provider._id} value={provider._id}>
                    {provider.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Customer Identifier */}
        {selectedProvider && (
          <div className="space-y-2">
            <Label htmlFor="customer">Customer Details</Label>
            <div className="flex gap-2">
              <Input
                id="customer"
                placeholder={getPlaceholderText()}
                value={customerIdentifier}
                onChange={(e) => setCustomerIdentifier(e.target.value)}
                className="flex-1"
              />
              <Button 
                onClick={verifyCustomer}
                disabled={!customerIdentifier.trim() || isVerifying}
                size="sm"
              >
                {isVerifying && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                Verify
              </Button>
            </div>
            {customerInfo && (
              <div className="p-3 bg-muted rounded-lg">
                <p className="text-sm font-medium">{customerInfo.name}</p>
                {customerInfo.address && (
                  <p className="text-xs text-muted-foreground">{customerInfo.address}</p>
                )}
              </div>
            )}
          </div>
        )}

        {/* Amount */}
        {customerInfo && (
          <div className="space-y-2">
            <Label htmlFor="amount">Amount (₦)</Label>
            <Input
              id="amount"
              type="number"
              placeholder="Enter amount"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              min={selectedProvider?.minAmount}
              max={selectedProvider?.maxAmount}
            />
            {selectedProvider && amount && (
              <div className="text-sm space-y-1">
                <div className="flex justify-between">
                  <span>Amount:</span>
                  <span>₦{parseFloat(amount).toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Fee:</span>
                  <span>₦{calculateFee().toLocaleString()}</span>
                </div>
                <div className="flex justify-between font-medium border-t pt-1">
                  <span>Total:</span>
                  <span>₦{getTotalAmount().toLocaleString()}</span>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Payment Button */}
        {customerInfo && amount && (
          <Button 
            onClick={handlePayment}
            disabled={isProcessing || !amount || parseFloat(amount) < (selectedProvider?.minAmount || 0)}
            className="w-full"
          >
            {isProcessing && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
            Pay ₦{getTotalAmount().toLocaleString()}
          </Button>
        )}
      </CardContent>
    </Card>
  );
};