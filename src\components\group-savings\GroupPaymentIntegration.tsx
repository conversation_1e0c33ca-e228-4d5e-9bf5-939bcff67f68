import React from 'react';
import { Button } from '@/components/ui/button';
import { FintechCard } from '@/components/ui/fintech-card';
import { PaystackIntegration } from '@/components/payments/PaystackIntegration';
import { useToast } from '@/hooks/use-toast';
import { ArrowLeft, Users, Target, Calendar } from 'lucide-react';
import { GroupSavingsPlan } from '@/services/group-savings-simple';

interface GroupPaymentIntegrationProps {
  group: GroupSavingsPlan;
  onBack: () => void;
  onPaymentSuccess: (amount: number) => Promise<void>;
}

export const GroupPaymentIntegration: React.FC<GroupPaymentIntegrationProps> = ({
  group,
  onBack,
  onPaymentSuccess
}) => {
  const { toast } = useToast();

  const handlePaymentSuccess = async (amount: number) => {
    try {
      await onPaymentSuccess(amount);
      toast({
        title: "Contribution Successful!",
        description: `₦${amount.toLocaleString()} contributed to ${group.name}.`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to record contribution",
        variant: "destructive",
      });
    }
  };

  const handlePaymentError = (error: string) => {
    toast({
      title: "Payment Failed",
      description: error,
      variant: "destructive",
    });
  };

  return (
    <div className="space-y-6">
      <FintechCard className="p-4 sm:p-6">
        <div className="mb-4">
          <Button 
            variant="outline" 
            onClick={onBack}
            className="mb-4 text-xs sm:text-sm"
          >
            <ArrowLeft className="mr-2 h-3 w-3 sm:h-4 sm:w-4" />
            Back to Groups
          </Button>
          
          <div className="text-center mb-6">
            <h3 className="text-lg sm:text-xl font-semibold mb-2">{group.name}</h3>
            <p className="text-xs sm:text-sm text-muted-foreground mb-4">
              {group.description}
            </p>
            
            {/* Group Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 mb-6">
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <Target className="h-4 w-4 sm:h-5 sm:w-5 mx-auto mb-1 text-primary" />
                <p className="text-xs text-muted-foreground">Target</p>
                <p className="text-sm sm:text-base font-semibold">₦{group.targetAmount.toLocaleString()}</p>
              </div>
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <Users className="h-4 w-4 sm:h-5 sm:w-5 mx-auto mb-1 text-primary" />
                <p className="text-xs text-muted-foreground">Members</p>
                <p className="text-sm sm:text-base font-semibold">{group.currentMembers}/{group.maxMembers}</p>
              </div>
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <Calendar className="h-4 w-4 sm:h-5 sm:w-5 mx-auto mb-1 text-primary" />
                <p className="text-xs text-muted-foreground">Frequency</p>
                <p className="text-sm sm:text-base font-semibold capitalize">{group.rules?.frequency || 'Monthly'}</p>
              </div>
            </div>
            
            <div className="p-4 bg-primary/10 rounded-lg">
              <h4 className="text-sm sm:text-base font-semibold mb-2 text-primary">
                Your Contribution Amount
              </h4>
              <p className="text-xl sm:text-2xl font-bold">
                ₦{group.contributionAmount.toLocaleString()}
              </p>
            </div>
          </div>
        </div>
        
        <PaystackIntegration
          amount={group.contributionAmount}
          title="Make Your Contribution"
          onSuccess={handlePaymentSuccess}
          onError={handlePaymentError}
          purpose="group_contribution"
        />
      </FintechCard>
    </div>
  );
};