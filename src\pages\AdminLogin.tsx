import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Mail, Lock, ShieldCheck } from "lucide-react";
import { Link } from "react-router-dom";
import { useAuth } from "@/hooks/use-auth";
import { ThemeToggle } from "@/components/ui/theme-toggle";

const AdminLogin = () => {
  const navigate = useNavigate();
  const { signIn } = useAuth();
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      await signIn(email, password);
      
      // Check if user has admin privileges by email pattern
      if (email.includes("admin")) {
        navigate('/admin/dashboard');
        toast.success('Welcome back, Admin!');
      } else {
        toast.error('Access denied. Admin credentials required.');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      toast.error(error.message || 'Failed to sign in');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 sm:p-6 lg:p-8" style={{ background: 'var(--gradient-hero)' }}>
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>
      
      <div className="w-full max-w-sm sm:max-w-md lg:max-w-lg xl:max-w-xl">
        <div className="w-full p-6 sm:p-8 lg:p-10 animate-fade-in">
          <div className="text-center mb-6 sm:mb-8 lg:mb-10">
            <div className="flex justify-center mb-4">
              <div className="h-12 sm:h-16 lg:h-20 w-auto">
                <img 
                  src="/lovable-uploads/90059237-949e-409d-ac39-61e95dd045cf.png" 
                  alt="Better Interest Logo" 
                  className="h-full w-auto object-contain"
                />
              </div>
            </div>
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-2 text-white font-alumni">
              Admin Portal
            </h1>
            <p className="text-sm sm:text-base text-gray-300">
              Access the administrative dashboard
            </p>
          </div>

          <form onSubmit={handleLogin} className="space-y-4 sm:space-y-6">
            <div className="space-y-2">
              <label htmlFor="email" className="flex items-center gap-2 text-sm font-medium text-white">
                <Mail className="h-4 w-4" />
                Email Address
              </label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="w-full bg-white/10 border-white/20 text-white placeholder:text-gray-400 focus:border-emerald-400 focus:ring-emerald-400"
              />
            </div>
            
            <div className="space-y-2">
              <label htmlFor="password" className="flex items-center gap-2 text-sm font-medium text-white">
                <Lock className="h-4 w-4" />
                Password
              </label>
              <Input
                id="password"
                type="password"
                placeholder="Enter your admin password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                className="w-full bg-white/10 border-white/20 text-white placeholder:text-gray-400 focus:border-emerald-400 focus:ring-emerald-400"
              />
            </div>
            
            <Button
              type="submit"
              className="w-full bg-gradient-to-r from-emerald-500 to-emerald-400 hover:from-emerald-600 hover:to-emerald-500 text-white border-0 shadow-lg text-base py-3"
              disabled={loading}
            >
              <ShieldCheck className="mr-2 h-5 w-5" />
              {loading ? "Signing In..." : "Access Admin Portal"}
            </Button>
          </form>
          
          <div className="mt-6 text-center">
            <Link 
              to="/login" 
              className="text-emerald-300 hover:text-emerald-200 text-sm transition-colors duration-300"
            >
              ← Return to User Login
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminLogin;