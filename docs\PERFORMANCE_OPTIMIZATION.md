# Performance Optimization Guide

## Overview
This guide provides strategies for optimizing the performance of the Better Interest application.

## Frontend Optimization

### 1. Code Splitting and Lazy Loading
```javascript
// Implement route-based code splitting
const Dashboard = lazy(() => import('@/pages/user/Dashboard'));
const Savings = lazy(() => import('@/pages/user/Savings'));

// Component lazy loading
const HeavyComponent = lazy(() => import('@/components/HeavyComponent'));

function App() {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <Routes>
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/savings" element={<Savings />} />
      </Routes>
    </Suspense>
  );
}
```

### 2. Image Optimization
```javascript
// Use WebP format with fallback
<picture>
  <source srcSet="image.webp" type="image/webp" />
  <img src="image.jpg" alt="Description" loading="lazy" />
</picture>

// Implement responsive images
<img 
  srcSet="
    image-320w.jpg 320w,
    image-640w.jpg 640w,
    image-1280w.jpg 1280w
  "
  sizes="(max-width: 320px) 280px, (max-width: 640px) 600px, 1200px"
  src="image-640w.jpg"
  alt="Description"
  loading="lazy"
/>
```

### 3. Bundle Optimization
```javascript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu'],
          charts: ['recharts', 'chart.js'],
          utils: ['date-fns', 'lodash']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  },
  optimizeDeps: {
    include: ['react', 'react-dom', 'react-router-dom']
  }
});
```

### 4. Caching Strategies
```javascript
// Service Worker for caching
// sw.js
const CACHE_NAME = 'betterinterest-v1';
const urlsToCache = [
  '/',
  '/static/css/main.css',
  '/static/js/main.js'
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

// API response caching
const cache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

async function cachedFetch(url, options = {}) {
  const cacheKey = `${url}-${JSON.stringify(options)}`;
  const cached = cache.get(cacheKey);
  
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  
  const response = await fetch(url, options);
  const data = await response.json();
  
  cache.set(cacheKey, {
    data,
    timestamp: Date.now()
  });
  
  return data;
}
```

### 5. React Performance
```javascript
// Memoization
const ExpensiveComponent = memo(({ data, onUpdate }) => {
  const processedData = useMemo(() => {
    return data.map(item => ({
      ...item,
      calculated: heavyCalculation(item)
    }));
  }, [data]);

  const handleUpdate = useCallback((id, value) => {
    onUpdate(id, value);
  }, [onUpdate]);

  return (
    <div>
      {processedData.map(item => (
        <Item 
          key={item.id} 
          data={item} 
          onUpdate={handleUpdate}
        />
      ))}
    </div>
  );
});

// Virtual scrolling for large lists
import { FixedSizeList as List } from 'react-window';

const VirtualizedList = ({ items }) => (
  <List
    height={600}
    itemCount={items.length}
    itemSize={50}
    itemData={items}
  >
    {({ index, style, data }) => (
      <div style={style}>
        {data[index].name}
      </div>
    )}
  </List>
);
```

## Backend Optimization

### 1. Database Optimization
```javascript
// MongoDB indexing
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ phone: 1 }, { unique: true });
db.transactions.createIndex({ userId: 1, createdAt: -1 });
db.savingsPlans.createIndex({ userId: 1, status: 1 });
db.fixedDeposits.createIndex({ userId: 1, maturityDate: 1 });

// Compound indexes for complex queries
db.transactions.createIndex({ 
  userId: 1, 
  type: 1, 
  status: 1, 
  createdAt: -1 
});

// Aggregation pipeline optimization
const pipeline = [
  { $match: { userId: ObjectId(userId), status: 'active' } },
  { $lookup: {
      from: 'savingsPlans',
      localField: 'planId',
      foreignField: '_id',
      as: 'plan'
    }
  },
  { $unwind: '$plan' },
  { $project: {
      amount: 1,
      planName: '$plan.name',
      interestRate: '$plan.interestRate'
    }
  }
];

// Use explain() to analyze query performance
db.transactions.find({ userId: ObjectId(userId) }).explain('executionStats');
```

### 2. Caching with Redis
```javascript
const redis = require('redis');
const client = redis.createClient();

// Cache frequently accessed data
async function getUserProfile(userId) {
  const cacheKey = `user:${userId}`;
  
  // Try cache first
  const cached = await client.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Fetch from database
  const user = await User.findById(userId);
  
  // Cache for 1 hour
  await client.setex(cacheKey, 3600, JSON.stringify(user));
  
  return user;
}

// Cache invalidation
async function updateUserProfile(userId, updates) {
  const user = await User.findByIdAndUpdate(userId, updates, { new: true });
  
  // Invalidate cache
  await client.del(`user:${userId}`);
  
  return user;
}

// Cache warming for popular data
async function warmCache() {
  const popularPlans = await SavingsPlan.find({ isActive: true }).limit(10);
  
  for (const plan of popularPlans) {
    await client.setex(
      `plan:${plan._id}`, 
      7200, // 2 hours
      JSON.stringify(plan)
    );
  }
}
```

### 3. API Response Optimization
```javascript
// Pagination
async function getTransactions(userId, page = 1, limit = 20) {
  const skip = (page - 1) * limit;
  
  const [transactions, total] = await Promise.all([
    Transaction.find({ userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean(), // Use lean() for read-only operations
    Transaction.countDocuments({ userId })
  ]);
  
  return {
    transactions,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  };
}

// Field selection
async function getUserSummary(userId) {
  return User.findById(userId)
    .select('firstName lastName email balance kycStatus')
    .lean();
}

// Populate optimization
async function getSavingsWithPlans(userId) {
  return UserSavings.find({ userId })
    .populate('planId', 'name interestRate duration') // Only needed fields
    .lean();
}
```

### 4. Connection Pooling
```javascript
// MongoDB connection optimization
const mongoose = require('mongoose');

mongoose.connect(process.env.MONGO_URI, {
  maxPoolSize: 10, // Maintain up to 10 socket connections
  serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
  socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
  bufferCommands: false, // Disable mongoose buffering
  bufferMaxEntries: 0 // Disable mongoose buffering
});

// Redis connection pooling
const redis = require('redis');
const client = redis.createClient({
  host: process.env.REDIS_HOST,
  port: process.env.REDIS_PORT,
  password: process.env.REDIS_PASSWORD,
  retry_strategy: (options) => {
    if (options.error && options.error.code === 'ECONNREFUSED') {
      return new Error('The server refused the connection');
    }
    if (options.total_retry_time > 1000 * 60 * 60) {
      return new Error('Retry time exhausted');
    }
    if (options.attempt > 10) {
      return undefined;
    }
    return Math.min(options.attempt * 100, 3000);
  }
});
```

### 5. Background Job Processing
```javascript
const Bull = require('bull');
const interestQueue = new Bull('interest calculation');

// Process interest calculations in background
interestQueue.process('calculate-daily-interest', async (job) => {
  const { userId } = job.data;
  
  const user = await User.findById(userId);
  const flexSavings = await FlexSavings.findOne({ userId });
  
  if (flexSavings && flexSavings.balance > 0) {
    const dailyInterest = flexSavings.balance * (flexSavings.dailyInterestRate / 100);
    
    await FlexSavings.findByIdAndUpdate(flexSavings._id, {
      $inc: { 
        balance: dailyInterest,
        totalInterestEarned: dailyInterest
      },
      lastInterestCalculation: new Date()
    });
    
    // Create transaction record
    await Transaction.create({
      userId,
      type: 'interest',
      amount: dailyInterest,
      description: 'Daily interest earned',
      status: 'completed'
    });
  }
});

// Schedule daily interest calculation
const cron = require('node-cron');

cron.schedule('0 0 * * *', async () => {
  const activeUsers = await User.find({ 
    status: 'active',
    'flexSavings.balance': { $gt: 0 }
  }).select('_id');
  
  for (const user of activeUsers) {
    await interestQueue.add('calculate-daily-interest', {
      userId: user._id
    });
  }
});
```

## Performance Monitoring

### 1. Application Performance Monitoring
```javascript
const prometheus = require('prom-client');

// Create metrics
const httpRequestDuration = new prometheus.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code']
});

const activeConnections = new prometheus.Gauge({
  name: 'active_connections',
  help: 'Number of active connections'
});

// Middleware to track metrics
app.use((req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    httpRequestDuration
      .labels(req.method, req.route?.path || req.path, res.statusCode)
      .observe(duration);
  });
  
  next();
});

// Expose metrics endpoint
app.get('/metrics', (req, res) => {
  res.set('Content-Type', prometheus.register.contentType);
  res.end(prometheus.register.metrics());
});
```

### 2. Database Performance Monitoring
```javascript
// MongoDB slow query logging
mongoose.set('debug', (collectionName, method, query, doc) => {
  const start = Date.now();
  
  return function() {
    const duration = Date.now() - start;
    if (duration > 100) { // Log queries taking more than 100ms
      console.log(`Slow query: ${collectionName}.${method}`, {
        query,
        duration: `${duration}ms`
      });
    }
  };
});

// Connection monitoring
mongoose.connection.on('connected', () => {
  console.log('MongoDB connected');
});

mongoose.connection.on('error', (err) => {
  console.error('MongoDB connection error:', err);
});

mongoose.connection.on('disconnected', () => {
  console.log('MongoDB disconnected');
});
```

### 3. Load Testing
```yaml
# artillery-load-test.yml
config:
  target: 'https://api.betterinterest.com'
  phases:
    - duration: 60
      arrivalRate: 10
    - duration: 120
      arrivalRate: 50
    - duration: 60
      arrivalRate: 100
  processor: "./test-functions.js"

scenarios:
  - name: "Authentication Flow"
    weight: 30
    flow:
      - post:
          url: "/api/v1/auth/login"
          json:
            email: "{{ $randomEmail() }}"
            password: "password123"
          capture:
            - json: "$.data.token"
              as: "token"
      - get:
          url: "/api/v1/user/profile"
          headers:
            Authorization: "Bearer {{ token }}"

  - name: "Savings Operations"
    weight: 70
    flow:
      - function: "authenticateUser"
      - get:
          url: "/api/v1/savings-plans"
          headers:
            Authorization: "Bearer {{ token }}"
      - post:
          url: "/api/v1/flex-savings/deposit"
          headers:
            Authorization: "Bearer {{ token }}"
          json:
            amount: "{{ $randomInt(1000, 10000) }}"
```

## Performance Benchmarks

### Target Metrics
- API response time: < 200ms (95th percentile)
- Database query time: < 100ms (95th percentile)
- Page load time: < 3 seconds
- Time to interactive: < 5 seconds
- Memory usage: < 512MB per process
- CPU usage: < 70% under normal load

### Monitoring Tools
- **Frontend**: Lighthouse, Web Vitals, Sentry
- **Backend**: Prometheus + Grafana, New Relic, DataDog
- **Database**: MongoDB Compass, Redis CLI
- **Infrastructure**: CloudWatch, Pingdom, UptimeRobot

## Optimization Checklist

### Frontend
- [ ] Code splitting implemented
- [ ] Images optimized (WebP, lazy loading)
- [ ] Bundle size optimized
- [ ] Caching strategies in place
- [ ] React performance optimizations
- [ ] Service worker for offline support

### Backend
- [ ] Database indexes created
- [ ] Redis caching implemented
- [ ] API pagination implemented
- [ ] Connection pooling configured
- [ ] Background jobs for heavy operations
- [ ] Query optimization completed

### Infrastructure
- [ ] CDN configured
- [ ] Gzip compression enabled
- [ ] HTTP/2 enabled
- [ ] SSL/TLS optimized
- [ ] Load balancing configured
- [ ] Auto-scaling policies set

### Monitoring
- [ ] Performance metrics tracked
- [ ] Alerts configured
- [ ] Load testing automated
- [ ] Error tracking implemented
- [ ] User experience monitoring
