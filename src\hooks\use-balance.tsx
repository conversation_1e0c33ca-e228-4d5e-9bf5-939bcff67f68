
import { useAuth } from '@/hooks/use-auth';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export function useBalance() {
  const { user } = useAuth();
  const [balance, setBalance] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch balance from backend API
  useEffect(() => {
    const fetchBalance = async () => {
      try {
        setIsLoading(true);

        const token = localStorage.getItem('token');
        if (!token) {
          setBalance(0);
          return;
        }

        const response = await fetch('/api/v1/user/profile', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to fetch user profile');
        }

        const result = await response.json();
        const userBalance = result.data?.balance || 0;
        setBalance(userBalance);

      } catch (error) {
        console.error('Error fetching balance:', error);
        toast.error('Failed to fetch your current balance');
        setBalance(0);
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      fetchBalance();
    }
  }, [user]);

  // Update balance function - this will be handled by backend transactions
  const updateBalance = async (amount: number, operation: 'add' | 'subtract') => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('Authentication required');
        return false;
      }

      // Check for sufficient funds for subtract operations
      if (operation === 'subtract' && balance < amount) {
        toast.error('Insufficient funds for this operation');
        return false;
      }

      // The balance update will be handled by the backend through transactions
      // This function now just optimistically updates the UI
      let newBalance = balance;

      if (operation === 'add') {
        newBalance += amount;
      } else {
        newBalance -= amount;
      }

      // Optimistically update the UI
      setBalance(newBalance);

      return true;
    } catch (error) {
      console.error('Error updating balance:', error);
      toast.error('Failed to update your balance');
      return false;
    }
  };

  // Format balance as Naira
  const formatBalance = (value: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  return {
    balance,
    formattedBalance: formatBalance(balance),
    isLoading,
    updateBalance,
    formatBalance,
  };
}
