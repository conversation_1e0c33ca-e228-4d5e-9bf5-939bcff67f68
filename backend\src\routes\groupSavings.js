const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const GroupSavingsPlan = require('../models/GroupSavingsPlan');
const Notification = require('../models/Notification');
const User = require('../models/User');

// Create new group savings plan
router.post('/', auth, async (req, res) => {
  try {
    const {
      name,
      description,
      category,
      targetAmount,
      contributionAmount,
      startDate,
      endDate,
      maxMembers,
      rules
    } = req.body;

    // Validate required fields
    if (!name || !description || !targetAmount || !contributionAmount) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
    }

    // Create group
    const group = new GroupSavingsPlan({
      name,
      description,
      category,
      targetAmount,
      contributionAmount,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      maxMembers,
      createdBy: req.user.id,
      rules: rules || {}
    });

    await group.save();

    // Send notification to creator
    await Notification.create({
      userId: req.user.id,
      type: 'group_update',
      title: 'Group Created Successfully',
      message: `Your group "${name}" has been created with invite code: ${group.inviteCode}`,
      data: { groupId: group._id, inviteCode: group.inviteCode }
    });

    res.status(201).json({
      success: true,
      message: 'Group savings plan created successfully',
      data: group
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error creating group',
      error: error.message
    });
  }
});

// Get user's groups
router.get('/my-groups', auth, async (req, res) => {
  try {
    const groups = await GroupSavingsPlan.find({
      $or: [
        { createdBy: req.user.id },
        { members: req.user.id }
      ]
    })
    .populate('createdBy', 'firstName lastName email')
    .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: groups
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching user groups',
      error: error.message
    });
  }
});

// Get all active groups (discover)
router.get('/discover', async (req, res) => {
  try {
    const { category, search, limit = 20 } = req.query;
    
    const query = { status: 'active' };
    
    if (category && category !== 'all') {
      query.category = category;
    }
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const groups = await GroupSavingsPlan.find(query)
      .populate('createdBy', 'firstName lastName')
      .limit(parseInt(limit))
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: groups
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching groups',
      error: error.message
    });
  }
});

// Join group by invite code
router.post('/join', auth, async (req, res) => {
  try {
    const { inviteCode } = req.body;

    if (!inviteCode) {
      return res.status(400).json({
        success: false,
        message: 'Invite code is required'
      });
    }

    const group = await GroupSavingsPlan.findByInviteCode(inviteCode);

    if (!group) {
      return res.status(404).json({
        success: false,
        message: 'Invalid invite code'
      });
    }

    if (group.isFull()) {
      return res.status(400).json({
        success: false,
        message: 'Group is full'
      });
    }

    if (!group.isActive()) {
      return res.status(400).json({
        success: false,
        message: 'Group is not active'
      });
    }

    // Check if user is already a member
    if (group.createdBy.toString() === req.user.id) {
      return res.status(400).json({
        success: false,
        message: 'You are the creator of this group'
      });
    }

    // Add user to group (simplified - you might want a separate members collection)
    await GroupSavingsPlan.findByIdAndUpdate(group._id, {
      $inc: { currentMembers: 1 }
    });

    // Send notifications
    await Promise.all([
      // Notification to new member
      Notification.create({
        userId: req.user.id,
        type: 'group_update',
        title: 'Joined Group Successfully',
        message: `You have successfully joined "${group.name}"`,
        data: { groupId: group._id }
      }),
      // Notification to group creator
      Notification.create({
        userId: group.createdBy,
        type: 'group_update',
        title: 'New Member Joined',
        message: `A new member has joined your group "${group.name}"`,
        data: { groupId: group._id, newMemberId: req.user.id }
      })
    ]);

    res.json({
      success: true,
      message: 'Successfully joined group',
      data: group
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error joining group',
      error: error.message
    });
  }
});

// Get group details
router.get('/:id', auth, async (req, res) => {
  try {
    const group = await GroupSavingsPlan.findById(req.params.id)
      .populate('createdBy', 'firstName lastName email');

    if (!group) {
      return res.status(404).json({
        success: false,
        message: 'Group not found'
      });
    }

    res.json({
      success: true,
      data: group
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching group details',
      error: error.message
    });
  }
});

// Record contribution
router.post('/:id/contribute', auth, async (req, res) => {
  try {
    const { amount, paymentReference } = req.body;
    const groupId = req.params.id;

    const group = await GroupSavingsPlan.findById(groupId);

    if (!group) {
      return res.status(404).json({
        success: false,
        message: 'Group not found'
      });
    }

    // Validate contribution amount
    if (amount < group.rules.minContribution || amount > group.rules.maxContribution) {
      return res.status(400).json({
        success: false,
        message: `Contribution must be between ₦${group.rules.minContribution} and ₦${group.rules.maxContribution}`
      });
    }

    // Update group amount
    await GroupSavingsPlan.findByIdAndUpdate(groupId, {
      $inc: { 
        currentAmount: amount,
        'metadata.totalContributions': 1
      },
      $set: {
        'metadata.lastContributionDate': new Date()
      }
    });

    // Send notification to group members
    await Notification.create({
      userId: group.createdBy,
      type: 'group_update',
      title: 'New Contribution Received',
      message: `₦${amount.toLocaleString()} was contributed to "${group.name}"`,
      data: { 
        groupId: group._id,
        amount,
        contributorId: req.user.id,
        paymentReference
      }
    });

    res.json({
      success: true,
      message: 'Contribution recorded successfully',
      data: {
        amount,
        groupId,
        paymentReference
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error recording contribution',
      error: error.message
    });
  }
});

module.exports = router;