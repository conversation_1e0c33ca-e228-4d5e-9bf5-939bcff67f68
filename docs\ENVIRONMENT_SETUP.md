# Environment Configuration Guide

## Overview
This guide explains how to set up environment variables for both frontend and backend components of the Better Interest application.

## Frontend Environment Variables

### Development (.env)
```bash
# Copy .env to your project root and update values
cp .env.example .env
```

### Production (.env.production)
```bash
# Update production values before deployment
VITE_API_URL=https://api.betterinterest.com/api/v1
VITE_PAYSTACK_PUBLIC_KEY=pk_live_your_live_key
```

## Backend Environment Variables

### Development (.env.backend)
```bash
# Copy .env.backend and rename to .env in your backend directory
cp .env.backend backend/.env
```

### Required Environment Variables

#### Database
- `MONGO_URI`: MongoDB connection string
- `REDIS_URL`: Redis connection string for caching

#### Authentication
- `JWT_SECRET`: Secret key for JWT tokens (minimum 32 characters)
- `JWT_EXPIRES_IN`: Token expiration time

#### Payment Integration
- `PAYSTACK_SECRET_KEY`: Paystack secret key
- `PAYSTACK_PUBLIC_KEY`: Paystack public key

#### Email Service
- `SENDGRID_API_KEY`: SendGrid API key for emails
- `SENDGRID_FROM_EMAIL`: From email address

#### SMS Service
- `TERMII_API_KEY`: Termii API key for SMS
- `TERMII_SENDER_ID`: SMS sender ID

## Security Best Practices

1. **Never commit .env files to version control**
2. **Use different keys for development and production**
3. **Rotate secrets regularly**
4. **Use strong, random passwords**
5. **Enable 2FA for all external services**

## Deployment Checklist

### Frontend Deployment
- [ ] Update VITE_API_URL to production backend
- [ ] Set VITE_PAYSTACK_PUBLIC_KEY to live key
- [ ] Disable debug mode (VITE_DEBUG_MODE=false)
- [ ] Set proper CORS origins

### Backend Deployment
- [ ] Update MONGO_URI to production database
- [ ] Set strong JWT_SECRET
- [ ] Configure production email service
- [ ] Set up SSL certificates
- [ ] Configure monitoring and logging
- [ ] Set up backup strategies

## Environment Validation

The application includes environment validation to ensure all required variables are set:

```javascript
// Frontend validation
if (!import.meta.env.VITE_API_URL) {
  throw new Error('VITE_API_URL is required');
}

// Backend validation
if (!process.env.JWT_SECRET) {
  throw new Error('JWT_SECRET is required');
}
```

## Troubleshooting

### Common Issues

1. **CORS Errors**: Check CORS_ORIGIN matches frontend URL
2. **Database Connection**: Verify MONGO_URI format and credentials
3. **Payment Failures**: Ensure Paystack keys match environment
4. **Email Not Sending**: Verify SendGrid API key and from address

### Debug Mode

Enable debug mode for troubleshooting:
```bash
# Frontend
VITE_DEBUG_MODE=true

# Backend
DEBUG_MODE=true
LOG_LEVEL=debug
```

## External Services Setup

### Paystack
1. Create account at https://paystack.com
2. Get API keys from dashboard
3. Set up webhooks for payment notifications

### SendGrid
1. Create account at https://sendgrid.com
2. Generate API key
3. Verify sender email address

### Termii
1. Create account at https://termii.com
2. Get API key from dashboard
3. Set up sender ID

### MongoDB
1. Set up MongoDB Atlas or local instance
2. Create database user with appropriate permissions
3. Whitelist application IP addresses

### Redis
1. Set up Redis instance (local or cloud)
2. Configure password authentication
3. Set up persistence if needed

## Monitoring and Alerts

Set up monitoring for:
- Database connections
- API response times
- Error rates
- Payment processing
- Email delivery
- SMS delivery

## Backup and Recovery

Ensure you have backups of:
- Environment configurations
- Database data
- File uploads
- SSL certificates
- API keys and secrets
