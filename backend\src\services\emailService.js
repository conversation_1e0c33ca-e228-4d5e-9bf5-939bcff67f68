const nodemailer = require('nodemailer');
const fs = require('fs');
const path = require('path');

// Email templates
const templates = {
  'new-ticket': {
    subject: 'New Support Ticket: {{ticketId}}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>New Support Ticket Created</h2>
        <p><strong>Ticket ID:</strong> {{ticketId}}</p>
        <p><strong>Subject:</strong> {{subject}}</p>
        <p><strong>Category:</strong> {{category}}</p>
        <p><strong>Priority:</strong> {{priority}}</p>
        <p><strong>Customer:</strong> {{userEmail}}</p>
        <hr>
        <p><strong>Message:</strong></p>
        <div style="background: #f5f5f5; padding: 15px; border-radius: 5px;">
          {{message}}
        </div>
        <p>Please login to the admin dashboard to respond to this ticket.</p>
      </div>
    `
  },
  'ticket-reply': {
    subject: 'New message in ticket: {{ticketId}}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>New Message in Support Ticket</h2>
        <p><strong>Ticket ID:</strong> {{ticketId}}</p>
        <p><strong>Subject:</strong> {{subject}}</p>
        <p><strong>Customer:</strong> {{userEmail}}</p>
        <hr>
        <p><strong>New Message:</strong></p>
        <div style="background: #f5f5f5; padding: 15px; border-radius: 5px;">
          {{message}}
        </div>
        <p>Please login to the admin dashboard to respond.</p>
      </div>
    `
  },
  'ticket-assigned': {
    subject: 'Ticket assigned: {{ticketId}}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Support Ticket Assigned to You</h2>
        <p><strong>Ticket ID:</strong> {{ticketId}}</p>
        <p><strong>Subject:</strong> {{subject}}</p>
        <p><strong>Priority:</strong> {{priority}}</p>
        <p>This ticket has been assigned to you. Please review and respond as soon as possible.</p>
        <p>Login to the admin dashboard to view the full ticket details.</p>
      </div>
    `
  },
  'ticket-escalated': {
    subject: 'URGENT: Ticket escalated: {{ticketId}}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #dc3545;">Ticket Escalated</h2>
        <p><strong>Ticket ID:</strong> {{ticketId}}</p>
        <p><strong>Subject:</strong> {{subject}}</p>
        <p><strong>Priority:</strong> {{priority}}</p>
        <p><strong>Escalation Level:</strong> {{escalationLevel}}</p>
        <p style="color: #dc3545;"><strong>This ticket requires immediate attention!</strong></p>
        <p>Please review and take action immediately.</p>
      </div>
    `
  }
};

// Create transporter
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: process.env.SMTP_PORT || 587,
    secure: false,
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    }
  });
};

// Render template
const renderTemplate = (templateName, data) => {
  const template = templates[templateName];
  if (!template) {
    throw new Error(`Template ${templateName} not found`);
  }

  let html = template.html;
  let subject = template.subject;

  // Replace placeholders
  Object.keys(data).forEach(key => {
    const regex = new RegExp(`{{${key}}}`, 'g');
    html = html.replace(regex, data[key]);
    subject = subject.replace(regex, data[key]);
  });

  return { html, subject };
};

// Send email
const sendEmail = async ({ to, subject, template, data, html, text }) => {
  try {
    const transporter = createTransporter();
    
    let emailSubject = subject;
    let emailHtml = html;
    
    if (template && data) {
      const rendered = renderTemplate(template, data);
      emailSubject = rendered.subject;
      emailHtml = rendered.html;
    }

    const mailOptions = {
      from: `"Better Interest Support" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
      to,
      subject: emailSubject,
      html: emailHtml,
      text
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', result.messageId);
    return result;
  } catch (error) {
    console.error('Email send error:', error);
    throw error;
  }
};

// Send bulk emails
const sendBulkEmails = async (emails) => {
  const results = [];
  
  for (const emailData of emails) {
    try {
      const result = await sendEmail(emailData);
      results.push({ success: true, messageId: result.messageId });
    } catch (error) {
      results.push({ success: false, error: error.message });
    }
  }
  
  return results;
};

module.exports = {
  sendEmail,
  sendBulkEmails,
  renderTemplate
};