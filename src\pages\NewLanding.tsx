import Footer from '@/components/landing/Footer';
import Header from '@/components/landing/Header';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import AOS from 'aos';
import 'aos/dist/aos.css';
import { motion } from 'framer-motion';
import {
    ArrowRight,
    BarChart3,
    CheckCircle,
    Headphones,
    Lock,
    PiggyBank,
    Play,
    Shield,
    Target,
    TrendingUp
} from 'lucide-react';
import { useEffect, useRef } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';

const NewLanding = () => {
  const navigate = useNavigate();
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: 'ease-out-cubic',
    });
  }, []);

  const features = [
    { icon: PiggyBank, title: 'Automated Savings', angle: 0 },
    { icon: Target, title: 'Goal Setting', angle: 60 },
    { icon: Lock, title: 'High-Yield Vaults', angle: 120 },
    { icon: TrendingUp, title: 'Secure Investments', angle: 180 },
    { icon: Headphones, title: '24/7 Support', angle: 240 },
    { icon: BarChart3, title: 'Portfolio Tracking', angle: 300 }
  ];

  const packages = [
    {
      name: 'Starter',
      price: '₦0',
      period: '/month',
      features: [
        'Basic savings account',
        'Up to 10% annual interest',
        'Mobile app access',
        'Email support'
      ],
      type: 'starter'
    },
    {
      name: 'Plus',
      price: '₦2,500',
      period: '/month',
      features: [
        'Everything in Starter',
        'Up to 12% annual interest',
        'Goal-based savings',
        'Priority support',
        'Investment options'
      ],
      type: 'plus'
    },
    {
      name: 'Premium',
      price: '₦5,000',
      period: '/month',
      features: [
        'Everything in Plus',
        'Up to 15% annual interest',
        'Personal financial advisor',
        'Advanced analytics',
        'Premium investments',
        'VIP support'
      ],
      type: 'premium'
    }
  ];

  const AnimatedHub = () => {
    const centerX = 200;
    const centerY = 200;
    const radius = 120;

    return (
      <div className="relative w-[400px] h-[400px] mx-auto">
        <svg
          ref={svgRef}
          width="400"
          height="400"
          className="absolute inset-0"
          viewBox="0 0 400 400"
        >
          {features.map((feature, index) => {
            const angle = (feature.angle * Math.PI) / 180;
            const x = centerX + radius * Math.cos(angle);
            const y = centerY + radius * Math.sin(angle);
            
            return (
              <line
                key={index}
                x1={centerX}
                y1={centerY}
                x2={x}
                y2={y}
                className="connecting-line"
              />
            );
          })}
        </svg>

        {/* Central Logo */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="w-20 h-20 bg-[#39E59E] rounded-full flex items-center justify-center hub-glow">
            <img
              src="/lovable-uploads/371e0f57-a280-42c0-8e1b-50aee7ebddc6.png"
              alt="Better Interest"
              className="w-12 h-12 object-contain"
            />
          </div>
        </div>

        {/* Feature Nodes */}
        {features.map((feature, index) => {
          const angle = (feature.angle * Math.PI) / 180;
          const x = centerX + radius * Math.cos(angle);
          const y = centerY + radius * Math.sin(angle);
          
          return (
            <div
              key={index}
              className="absolute w-16 h-16 feature-node rounded-full flex items-center justify-center cursor-pointer group"
              style={{
                left: `${x - 32}px`,
                top: `${y - 32}px`
              }}
              title={feature.title}
            >
              <feature.icon className="w-8 h-8 text-[#39E59E]" />
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-black/80 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                {feature.title}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <>
      <Helmet>
        <title>Better Interest | Nigeria's #1 Smart Savings Platform</title>
        <meta name="description" content="Join over 50,000 Nigerians building wealth through our intelligent savings platform. Earn up to 15% annual interest with Better Interest." />
      </Helmet>

      <div className="min-h-screen landing-bg text-white">
        <Header />
        
        {/* Hero Section */}
        <section className="relative pt-20 pb-16 overflow-hidden">
          <div className="container mx-auto px-4 relative z-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
              {/* Left Column - Content */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="space-y-8"
              >
                <h1 className="text-5xl md:text-7xl font-bold leading-tight">
                  <span className="text-white">Better Interest,</span>
                  <br />
                  <span className="text-[#39E59E]">Better Future</span>
                </h1>
                
                <h2 className="text-2xl md:text-3xl font-semibold text-[#39E59E]">
                  Nigeria's #1 Smart Savings Platform
                </h2>
                
                <p className="text-xl text-gray-300 leading-relaxed max-w-lg">
                  Join over 50,000 Nigerians building wealth through our intelligent savings platform. 
                  Earn up to <span className="font-bold text-[#39E59E]">15% annual interest</span>.
                </p>

                <div className="flex flex-col sm:flex-row gap-4">
                  <button 
                    className="btn-neumorphic-inward text-lg px-8 py-6 group bg-[#39E59E] hover:bg-[#2dd489]"
                    onClick={() => navigate('/signup')}
                  >
                    Start Saving Today
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                  </button>
                  
                  <Button 
                    variant="outline" 
                    size="lg" 
                    className="text-lg px-8 py-6 rounded-xl group border-[#39E59E] text-[#39E59E] hover:bg-[#39E59E]/10"
                  >
                    <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                    Learn How It Works
                  </Button>
                </div>

                {/* Trust Indicators */}
                <div className="flex flex-wrap gap-6 pt-8">
                  <div className="flex items-center gap-2">
                    <Shield className="w-5 h-5 text-[#39E59E]" />
                    <span className="text-sm text-gray-300">NDIC Insured</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Lock className="w-5 h-5 text-[#39E59E]" />
                    <span className="text-sm text-gray-300">Bank-Grade Security</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Headphones className="w-5 h-5 text-[#39E59E]" />
                    <span className="text-sm text-gray-300">24/7 Support</span>
                  </div>
                </div>
              </motion.div>

              {/* Right Column - Animated Hub */}
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="flex justify-center"
              >
                <AnimatedHub />
              </motion.div>
            </div>
          </div>
        </section>

        {/* Packages Section */}
        <section className="py-24 relative">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
                Packages Tailored For You
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Choose the perfect savings plan that matches your financial goals and lifestyle.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {packages.map((pkg, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <Card className={`h-full package-card ${pkg.type} hover:scale-105 transition-transform duration-300`}>
                    <CardContent className="p-8 text-center">
                      <h3 className="text-2xl font-bold mb-4 text-white">{pkg.name}</h3>
                      <div className="mb-6">
                        <span className="text-4xl font-bold text-[#39E59E]">{pkg.price}</span>
                        <span className="text-gray-400">{pkg.period}</span>
                      </div>
                      <ul className="space-y-3 mb-8">
                        {pkg.features.map((feature, idx) => (
                          <li key={idx} className="flex items-center gap-2 text-gray-300">
                            <CheckCircle className="w-4 h-4 text-[#39E59E] flex-shrink-0" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                      <button className="w-full btn-neumorphic-inward py-3 bg-[#39E59E] hover:bg-[#2dd489] text-white font-semibold">
                        Get Started
                      </button>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
};

export default NewLanding;
