import Footer from "@/components/landing/Footer";
import Header from "@/components/landing/Header";
import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import {
    ArrowRight,
    Award,
    BarChart3,
    CheckCircle,
    Headphones,
    Lock,
    PiggyBank,
    Play,
    Shield,
    Smartphone,
    Star,
    Target,
    TrendingUp,
    Users
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';

const NewLanding = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  // Feature nodes for animated hub
  const featureNodes = [
    {
      title: 'Automated Savings',
      icon: PiggyBank,
      angle: 0,
      description: 'Set up automatic transfers to build your savings effortlessly.'
    },
    {
      title: 'High-Yield Vaults',
      icon: TrendingUp,
      angle: 60,
      description: 'Earn up to 15% annual interest on your savings.'
    },
    {
      title: 'Goal Setting',
      icon: Target,
      angle: 120,
      description: 'Set and track your financial goals with smart insights.'
    },
    {
      title: 'Portfolio Tracking',
      icon: BarChart3,
      angle: 180,
      description: 'Monitor your investments and savings performance.'
    },
    {
      title: '24/7 Support',
      icon: Headphones,
      angle: 240,
      description: 'Get help whenever you need it with our dedicated support.'
    },
    {
      title: 'Secure Platform',
      icon: Shield,
      angle: 300,
      description: 'Bank-grade security to keep your money safe.'
    }
  ];

  // Animated Hub Component
  const AnimatedHub = () => {
    const radius = 140;
    const centerX = 160;
    const centerY = 160;

    return (
      <div className="relative w-[320px] h-[320px] mx-auto">
        {/* Background glow effects */}
        <div className="absolute inset-0 bg-gradient-radial from-primary/20 via-primary/10 to-transparent rounded-full animate-pulse"></div>
        <div className="absolute inset-4 bg-gradient-radial from-primary/15 via-primary/5 to-transparent rounded-full animate-pulse delay-1000"></div>

        {/* SVG for connecting lines */}
        <svg
          width="320"
          height="320"
          className="absolute inset-0"
          viewBox="0 0 320 320"
        >
          <defs>
            <filter id="glow">
              <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
              <feMerge>
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
          </defs>

          {featureNodes.map((node, index) => {
            const angle = (node.angle * Math.PI) / 180;
            const x = centerX + radius * Math.cos(angle);
            const y = centerY + radius * Math.sin(angle);

            return (
              <motion.line
                key={index}
                x1={centerX}
                y1={centerY}
                x2={x}
                y2={y}
                stroke="url(#gradient)"
                strokeWidth="2"
                strokeDasharray="8,4"
                filter="url(#glow)"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ duration: 2, delay: index * 0.2 }}
                className="animate-pulse"
              />
            );
          })}

          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="hsl(var(--primary))" stopOpacity="0.8" />
              <stop offset="100%" stopColor="hsl(var(--primary))" stopOpacity="0.3" />
            </linearGradient>
          </defs>
        </svg>

        {/* Feature Nodes */}
        {featureNodes.map((node, index) => {
          const angle = (node.angle * Math.PI) / 180;
          const x = centerX + radius * Math.cos(angle);
          const y = centerY + radius * Math.sin(angle);

          return (
            <motion.div
              key={index}
              className="absolute w-16 h-16 -translate-x-1/2 -translate-y-1/2 group cursor-pointer"
              style={{ left: `${x}px`, top: `${y}px` }}
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{ scale: 1.1 }}
            >
              <div className="w-full h-full bg-background/80 backdrop-blur-md border border-primary/30 rounded-full flex items-center justify-center shadow-lg hover:shadow-primary/25 transition-all duration-300">
                <node.icon className="w-7 h-7 text-primary" />
              </div>

              {/* Tooltip */}
              <div className="absolute -bottom-10 left-1/2 -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-background/90 backdrop-blur-sm text-foreground text-xs px-3 py-1 rounded-lg whitespace-nowrap border border-border shadow-lg z-10">
                {node.title}
              </div>
            </motion.div>
          );
        })}

        {/* Central Hub */}
        <motion.div
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-24 h-24"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          <div className="w-full h-full bg-gradient-to-br from-primary to-primary/80 rounded-full flex items-center justify-center shadow-2xl relative overflow-hidden">
            {/* Animated glow */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary/50 to-transparent rounded-full animate-ping"></div>

            {/* Logo/Brand */}
            <div className="relative z-10 text-primary-foreground font-bold text-xs text-center leading-tight">
              Better<br/>Interest
            </div>
          </div>
        </motion.div>
      </div>
    );
  };

  return (
    <>
      <Helmet>
        <title>Better Interest - Smart Savings Platform | Earn Up to 15% Interest</title>
        <meta name="description" content="Join Nigeria's #1 smart savings platform. Automated savings, high-yield vaults, and secure investments. Start with ₦500 and earn up to 15% annual interest." />
        <meta name="keywords" content="savings, investment, fintech, Nigeria, high interest, automated savings, financial goals" />
      </Helmet>

      <div className="min-h-screen bg-background">
        {/* Background patterns */}
        <div className="fixed inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-muted/20"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,hsl(var(--primary)/0.1),transparent_50%)]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,hsl(var(--primary)/0.05),transparent_50%)]"></div>

          {/* Hexagonal pattern */}
          <div
            className="absolute inset-0 opacity-[0.02]"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='hsl(var(--primary))' fill-opacity='1'%3E%3Cpolygon points='30 15 45 30 30 45 15 30'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
              backgroundSize: '60px 60px'
            }}
          ></div>
        </div>

        <Header />

        {/* Hero Section */}
        <section className="relative pt-20 pb-16 overflow-hidden">
          <div className="container mx-auto px-4">
            <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
              {/* Left Column - Content */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="space-y-8"
              >
                <div className="space-y-4">
                  <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight">
                    <span className="text-foreground">Better Interest,</span>
                    <br />
                    <span className="bg-gradient-to-r from-primary via-primary/80 to-primary bg-clip-text text-transparent">
                      Better Future
                    </span>
                  </h1>

                  <p className="text-xl text-muted-foreground leading-relaxed max-w-lg">
                    Join over 50,000 Nigerians building wealth through our intelligent savings platform.
                    Earn up to <span className="font-bold text-primary">15% annual interest</span>.
                  </p>
                </div>

                {/* CTA Buttons */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button
                    size="lg"
                    className="text-lg px-8 py-6 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 shadow-lg hover:shadow-primary/25 transition-all duration-300"
                    onClick={() => navigate('/signup')}
                  >
                    Start Saving Today
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>

                  <Button
                    variant="outline"
                    size="lg"
                    className="text-lg px-8 py-6 border-primary/30 text-primary hover:bg-primary/5"
                  >
                    <Play className="mr-2 h-5 w-5" />
                    Learn How It Works
                  </Button>
                </div>

                {/* Trust Badges */}
                <div className="flex flex-wrap gap-6 pt-4">
                  <div className="flex items-center gap-2">
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                    <span className="text-sm text-muted-foreground">4.9/5 Rating</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Shield className="w-5 h-5 text-primary" />
                    <span className="text-sm text-muted-foreground">NDIC Insured</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Smartphone className="w-5 h-5 text-primary" />
                    <span className="text-sm text-muted-foreground">Mobile First</span>
                  </div>
                </div>
              </motion.div>

              {/* Right Column - Animated Hub */}
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="flex justify-center lg:justify-end"
              >
                <AnimatedHub />
              </motion.div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-24 relative">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Why Choose <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">Better Interest</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Experience the perfect blend of traditional Nigerian savings culture and cutting-edge technology
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: Shield,
                  title: "Bank-Level Security",
                  description: "Your money is protected with military-grade encryption and NDIC insurance coverage up to ₦500,000",
                  highlight: "NDIC Insured"
                },
                {
                  icon: TrendingUp,
                  title: "High Returns",
                  description: "Earn up to 15% annual returns on your savings with our competitive interest rates and investment options",
                  highlight: "Up to 15% Returns"
                },
                {
                  icon: Smartphone,
                  title: "Mobile First",
                  description: "Save, invest, and manage your money anytime, anywhere with our intuitive mobile app",
                  highlight: "24/7 Access"
                },
                {
                  icon: Users,
                  title: "Group Savings",
                  description: "Join savings groups or create your own to achieve financial goals together with friends and family",
                  highlight: "Social Savings"
                },
                {
                  icon: PiggyBank,
                  title: "Auto-Save",
                  description: "Set up automatic daily, weekly, or monthly saves to build wealth consistently without thinking about it",
                  highlight: "Hands-Free"
                },
                {
                  icon: Award,
                  title: "Rewards Program",
                  description: "Earn points for consistent saving habits and redeem them for exciting rewards and bonuses",
                  highlight: "Earn Rewards"
                }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="relative p-8 rounded-2xl backdrop-blur-md border border-border/50 bg-card/50 hover:bg-card/80 transition-all duration-300 hover:scale-105 group"
                >
                  <div className="absolute top-4 right-4">
                    <span className="px-2 py-1 text-xs font-medium rounded-full bg-primary/20 text-primary">
                      {feature.highlight}
                    </span>
                  </div>

                  <div className="mb-6 w-fit p-3 rounded-full bg-primary/10">
                    <feature.icon className="h-8 w-8 text-primary" />
                  </div>

                  <h3 className="text-xl font-bold mb-4">{feature.title}</h3>
                  <p className="text-muted-foreground leading-relaxed">{feature.description}</p>

                  {/* Hover effect */}
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-primary/5 to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* How It Works */}
        <section className="py-24 bg-muted/30">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6">How It Works</h2>
              <p className="text-xl text-muted-foreground">Start your savings journey in just 3 simple steps</p>
            </motion.div>

            <div className="grid md:grid-cols-3 gap-8 relative">
              {/* Connection Lines */}
              <div className="hidden md:block absolute top-24 left-1/3 right-1/3 h-0.5 bg-gradient-to-r from-primary to-primary/50"></div>

              {[
                {
                  step: "01",
                  title: "Sign Up & Verify",
                  description: "Create your account in under 2 minutes and complete our simple KYC verification process",
                  icon: Users
                },
                {
                  step: "02",
                  title: "Set Your Goals",
                  description: "Define your savings targets and choose from our flexible savings plans that suit your lifestyle",
                  icon: Target
                },
                {
                  step: "03",
                  title: "Start Saving",
                  description: "Make automatic or manual contributions and watch your money grow with competitive interest rates",
                  icon: PiggyBank
                }
              ].map((step, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  className="relative text-center"
                >
                  {/* Step Number */}
                  <div className="mx-auto w-16 h-16 rounded-full bg-gradient-to-r from-primary to-primary/80 flex items-center justify-center text-primary-foreground font-bold text-xl mb-6 relative z-10 shadow-lg">
                    {step.step}
                  </div>

                  {/* Content Card */}
                  <div className="p-6 rounded-2xl backdrop-blur-md border border-border/50 bg-card/50 hover:bg-card/80 transition-all duration-300">
                    <div className="mb-4 w-fit mx-auto p-3 rounded-full bg-primary/10">
                      <step.icon className="h-8 w-8 text-primary" />
                    </div>
                    <h3 className="text-xl font-bold mb-4">{step.title}</h3>
                    <p className="text-muted-foreground">{step.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mt-12"
            >
              <Button
                size="lg"
                className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-lg px-8 py-6"
                onClick={() => navigate('/signup')}
              >
                Get Started Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </motion.div>
          </div>
        </section>

        {/* Security Section */}
        <section className="py-24">
          <div className="container mx-auto px-4">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8 }}
              >
                <h2 className="text-4xl md:text-5xl font-bold mb-6">
                  Your Money is <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">100% Safe</span>
                </h2>
                <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                  We use the same security standards as major banks to protect your money and personal information.
                </p>

                <div className="space-y-4">
                  {[
                    "256-bit SSL encryption for all transactions",
                    "NDIC insurance coverage up to ₦500,000",
                    "Two-factor authentication (2FA)",
                    "Biometric login (fingerprint & face ID)",
                    "Real-time fraud monitoring",
                    "Regulated by CBN and SEC"
                  ].map((feature, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="flex items-center gap-3"
                    >
                      <CheckCircle className="w-5 h-5 text-primary flex-shrink-0" />
                      <span className="text-muted-foreground">{feature}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8 }}
                className="relative"
              >
                <div className="p-8 rounded-3xl backdrop-blur-md border border-border/50 bg-card/50">
                  <div className="text-center">
                    <Shield className="w-24 h-24 text-primary mx-auto mb-6" />
                    <h3 className="text-2xl font-bold mb-4">Bank-Level Security</h3>
                    <p className="text-muted-foreground mb-6">Your funds are protected with the same security standards used by major financial institutions.</p>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-4 rounded-lg bg-primary/10">
                        <Lock className="w-8 h-8 text-primary mx-auto mb-2" />
                        <p className="font-semibold">Encrypted</p>
                      </div>
                      <div className="text-center p-4 rounded-lg bg-primary/10">
                        <Award className="w-8 h-8 text-primary mx-auto mb-2" />
                        <p className="font-semibold">Insured</p>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Animated Feature Center Page - All Plans */}
        <section className="py-24 bg-muted/30 relative overflow-hidden">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                All Your <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">Savings Plans</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Choose from our comprehensive range of savings and investment products designed for every financial goal
              </p>
            </motion.div>

            {/* Central Animated Hub for Plans */}
            <div className="flex justify-center mb-16">
              <div className="relative w-[400px] h-[400px]">
                {/* Background effects */}
                <div className="absolute inset-0 bg-gradient-radial from-primary/10 via-primary/5 to-transparent rounded-full animate-pulse"></div>

                {/* Plan nodes */}
                {[
                  { title: 'SafeLock', icon: Lock, angle: 0, color: 'text-red-500' },
                  { title: 'Group Savings', icon: Users, angle: 60, color: 'text-blue-500' },
                  { title: 'SavingsFlex', icon: PiggyBank, angle: 120, color: 'text-green-500' },
                  { title: 'Fixed Deposit', icon: TrendingUp, angle: 180, color: 'text-purple-500' },
                  { title: 'Target Savings', icon: Target, angle: 240, color: 'text-orange-500' },
                  { title: 'Investment', icon: BarChart3, angle: 300, color: 'text-pink-500' }
                ].map((plan, index) => {
                  const radius = 160;
                  const centerX = 200;
                  const centerY = 200;
                  const angle = (plan.angle * Math.PI) / 180;
                  const x = centerX + radius * Math.cos(angle);
                  const y = centerY + radius * Math.sin(angle);

                  return (
                    <motion.div
                      key={index}
                      className="absolute w-20 h-20 -translate-x-1/2 -translate-y-1/2 group cursor-pointer"
                      style={{ left: `${x}px`, top: `${y}px` }}
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ duration: 0.5, delay: index * 0.15 }}
                      whileHover={{ scale: 1.15 }}
                      onClick={() => navigate('/savings')}
                    >
                      <div className="w-full h-full bg-background/90 backdrop-blur-md border border-primary/30 rounded-2xl flex flex-col items-center justify-center shadow-lg hover:shadow-primary/25 transition-all duration-300 p-2">
                        <plan.icon className={`w-8 h-8 ${plan.color} mb-1`} />
                        <span className="text-xs font-medium text-center leading-tight">{plan.title}</span>
                      </div>

                      {/* Connecting line */}
                      <svg className="absolute inset-0 w-full h-full pointer-events-none" style={{ left: `${-x + centerX}px`, top: `${-y + centerY}px` }}>
                        <line
                          x1={centerX}
                          y1={centerY}
                          x2={x}
                          y2={y}
                          stroke="hsl(var(--primary))"
                          strokeWidth="1"
                          strokeDasharray="4,2"
                          opacity="0.3"
                          className="animate-pulse"
                        />
                      </svg>
                    </motion.div>
                  );
                })}

                {/* Central Hub */}
                <motion.div
                  className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-28 h-28"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.8, delay: 0.8 }}
                >
                  <div className="w-full h-full bg-gradient-to-br from-primary to-primary/80 rounded-full flex items-center justify-center shadow-2xl relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-primary/30 to-transparent rounded-full animate-ping"></div>
                    <div className="relative z-10 text-primary-foreground font-bold text-sm text-center">
                      All Plans
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>

            {/* Plan Cards Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {isLoading ? (
                // Loading skeletons
                [...Array(6)].map((_, index) => (
                  <div key={index} className="p-6 rounded-2xl border border-border/50 bg-card/50 animate-pulse">
                    <div className="w-12 h-12 bg-muted rounded-full mb-4"></div>
                    <div className="h-6 bg-muted rounded mb-2"></div>
                    <div className="h-4 bg-muted rounded mb-4"></div>
                    <div className="space-y-2">
                      <div className="h-3 bg-muted rounded"></div>
                      <div className="h-3 bg-muted rounded w-3/4"></div>
                    </div>
                  </div>
                ))
              ) : (
                featureNodes.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="p-6 rounded-2xl backdrop-blur-md border border-border/50 bg-card/50 hover:bg-card/80 transition-all duration-300 hover:scale-105 cursor-pointer"
                    onClick={() => navigate('/savings')}
                  >
                    <div className="mb-4 w-fit p-3 rounded-full bg-primary/10">
                      <feature.icon className="h-8 w-8 text-primary" />
                    </div>
                    <h3 className="text-lg font-bold mb-2">{feature.title}</h3>
                    <p className="text-muted-foreground text-sm leading-relaxed">{feature.description}</p>
                  </motion.div>
                ))
              )}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-24 bg-gradient-to-br from-primary to-primary/80 text-primary-foreground relative overflow-hidden">
          {/* Background effects */}
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.1),transparent_50%)]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(255,255,255,0.05),transparent_50%)]"></div>

          <div className="container mx-auto px-4 text-center relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Ready to Transform Your Financial Future?
              </h2>
              <p className="text-xl text-primary-foreground/90 mb-8 max-w-3xl mx-auto leading-relaxed">
                Join over 50,000 Nigerians who are building wealth with Better Interest. Start with as little as ₦500 and watch your money grow.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <Button
                  size="lg"
                  variant="secondary"
                  className="bg-background text-foreground hover:bg-background/90 text-lg px-8 py-6 font-bold shadow-lg"
                  onClick={() => navigate('/signup')}
                >
                  Start Saving Today
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-primary-foreground/30 text-primary-foreground hover:bg-primary-foreground/10 text-lg px-8 py-6"
                >
                  Download App
                </Button>
              </div>

              <p className="text-primary-foreground/80 text-sm">
                ✓ No hidden fees  ✓ Start with ₦500  ✓ Withdraw anytime
              </p>
            </motion.div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
};

export default NewLanding;
