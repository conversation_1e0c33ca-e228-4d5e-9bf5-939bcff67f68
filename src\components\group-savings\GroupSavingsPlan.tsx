import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { FintechCard } from '@/components/ui/fintech-card';
import { FloatingLabelInput } from '@/components/ui/floating-label-input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { CreateGroupDialog } from './CreateGroupDialog';
import { JoinGroupDialog } from './JoinGroupDialog';
import { GroupSavingsCard } from './GroupSavingsCard';
import { useGroupSavings, GroupSavingsPlan as GroupPlan } from '@/services/group-savings-simple';
import { useAuth } from '@/hooks/use-auth';
import { Users, Plus, Search, TrendingUp } from 'lucide-react';
import { GroupPaymentIntegration } from './GroupPaymentIntegration';

const GroupSavingsPlan = () => {
  const [groups, setGroups] = useState<GroupPlan[]>([]);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showJoinDialog, setShowJoinDialog] = useState(false);
  const [showPayment, setShowPayment] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<GroupPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const { user } = useAuth();
  const { toast } = useToast();
  const { createGroup, joinGroup, getGroups } = useGroupSavings();

  useEffect(() => {
    loadUserGroups();
  }, [user]);

  const loadUserGroups = async () => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      const userGroups = await getGroups();
      setGroups(userGroups);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to load groups",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateGroup = async (groupData: any) => {
    if (!user?.id) return;

    try {
      const newGroup = await createGroup({
        ...groupData,
        createdBy: user.id,
        status: 'active' as const
      });
      
      toast({
        title: "Group Created!",
        description: `Group "${newGroup.name}" has been created successfully.`,
      });
      
      setShowCreateDialog(false);
      loadUserGroups();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create group",
        variant: "destructive",
      });
    }
  };

  const handleJoinGroup = async (inviteCode: string) => {
    if (!user?.id || !user?.email) return;

    try {
      const success = await joinGroup(inviteCode);
      
      if (success) {
        toast({
          title: "Joined Group!",
          description: "You have successfully joined the group.",
        });
        
        setShowJoinDialog(false);
        loadUserGroups();
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to join group",
        variant: "destructive",
      });
    }
  };

  const handleContribute = (group: GroupPlan) => {
    setSelectedGroup(group);
    setShowPayment(true);
  };

  const handlePaymentSuccess = async (amount: number) => {
    if (!selectedGroup || !user?.id) return;

    try {
      // Mock contribution recording
      console.log('Recording contribution:', {
        groupId: selectedGroup._id,
        memberId: user.id,
        amount,
        type: 'regular',
        status: 'completed',
        paymentMethod: 'paystack',
        transactionRef: `TXN_${Date.now()}`,
        createdAt: new Date()
      });

      toast({
        title: "Contribution Successful!",
        description: `₦${amount.toLocaleString()} contributed to ${selectedGroup.name}.`,
      });
      
      setShowPayment(false);
      setSelectedGroup(null);
      loadUserGroups();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to record contribution",
        variant: "destructive",
      });
    }
  };

  const filteredGroups = groups.filter(group => {
    const matchesSearch = group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         group.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = filterCategory === 'all' || group.category === filterCategory;
    return matchesSearch && matchesCategory;
  });

  if (showPayment && selectedGroup) {
    return (
      <GroupPaymentIntegration
        group={selectedGroup}
        onBack={() => {
          setShowPayment(false);
          setSelectedGroup(null);
        }}
        onPaymentSuccess={async (amount: number) => {
          await handlePaymentSuccess(amount);
        }}
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="h-10 w-10 rounded-none bg-primary/10 flex items-center justify-center shadow-[inset_2px_2px_4px_rgba(255,255,255,0.3),inset_-2px_-2px_4px_rgba(0,0,0,0.2)]">
          <Users className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h2 className="text-xl font-semibold">Group Savings</h2>
          <p className="text-sm text-muted-foreground">Save together, achieve more</p>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="md:col-span-2">
          <FloatingLabelInput
            id="search"
            type="text"
            label="Search Groups"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search by name or description..."
            className="w-full"
          />
        </div>
        <div className="w-full">
          <Label htmlFor="category" className="text-xs sm:text-sm">Category</Label>
          <Select value={filterCategory} onValueChange={setFilterCategory}>
            <SelectTrigger className="rounded-none shadow-[inset_2px_2px_4px_rgba(0,0,0,0.1),inset_-2px_-2px_4px_rgba(255,255,255,0.1)] w-full">
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="personal">Personal</SelectItem>
              <SelectItem value="business">Business</SelectItem>
              <SelectItem value="education">Education</SelectItem>
              <SelectItem value="investment">Investment</SelectItem>
              <SelectItem value="family">Family</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        <Button 
          onClick={() => setShowCreateDialog(true)}
          className="bg-primary hover:bg-primary/90 rounded-none shadow-[4px_4px_8px_rgba(0,0,0,0.2),-4px_-4px_8px_rgba(255,255,255,0.1)] text-xs sm:text-sm w-full"
        >
          <Plus className="mr-2 h-3 w-3 sm:h-4 sm:w-4" />
          Create New Group
        </Button>
        <Button 
          variant="outline"
          onClick={() => setShowJoinDialog(true)}
          className="border-primary text-primary hover:bg-primary hover:text-white rounded-none shadow-[4px_4px_8px_rgba(0,0,0,0.2),-4px_-4px_8px_rgba(255,255,255,0.1)] text-xs sm:text-sm w-full"
        >
          <Search className="mr-2 h-3 w-3 sm:h-4 sm:w-4" />
          Join Existing Group
        </Button>
      </div>

      {/* Groups List */}
      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
          <p className="text-muted-foreground mt-2">Loading groups...</p>
        </div>
      ) : filteredGroups.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredGroups.map((group) => (
            <GroupSavingsCard
              key={group._id}
              group={group}
              onContribute={() => handleContribute(group)}
            />
          ))}
        </div>
      ) : (
        <FintechCard className="p-8 text-center">
          <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">
            {searchTerm || filterCategory !== 'all' ? 'No groups found' : 'No groups yet'}
          </h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm || filterCategory !== 'all' 
              ? 'Try adjusting your search or filter criteria' 
              : 'Create your first group or join an existing one to start saving together'
            }
          </p>
          {!searchTerm && filterCategory === 'all' && (
            <div className="flex gap-2 justify-center">
              <Button onClick={() => setShowCreateDialog(true)}>
                Create Group
              </Button>
              <Button variant="outline" onClick={() => setShowJoinDialog(true)}>
                Join Group
              </Button>
            </div>
          )}
        </FintechCard>
      )}

      {/* Dialogs */}
      <CreateGroupDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onCreateGroup={handleCreateGroup}
      />
      
      <JoinGroupDialog
        open={showJoinDialog}
        onOpenChange={setShowJoinDialog}
        onJoinGroup={handleJoinGroup}
      />
    </div>
  );
};

export default GroupSavingsPlan;