# 🚀 Better Interest - Quick Deployment Guide

## 🌐 **PRODUCTION DOMAINS**
- **Frontend**: https://demo.kojaonline.store
- **Backend API**: https://api.kojaonline.store

---

## ⚡ **QUICK DEPLOYMENT (Windows)**

### **1. Prerequisites**
```bash
# Check Node.js (required: 16+)
node --version

# Check npm
npm --version
```

### **2. Install Netlify CLI**
```bash
npm install -g netlify-cli
netlify login
```

### **3. Deploy with Batch Script**
```bash
# Deploy preview
deploy.bat preview

# Deploy to production
deploy.bat production
```

### **4. Deploy with npm Scripts**
```bash
# Deploy preview
npm run deploy

# Deploy to production  
npm run deploy:prod

# Windows PowerShell
npm run deploy:win:prod
```

---

## 🐧 **QUICK DEPLOYMENT (Linux/Mac)**

### **1. Make Script Executable**
```bash
chmod +x scripts/netlify-deploy.sh
```

### **2. Deploy**
```bash
# Deploy preview
./scripts/netlify-deploy.sh preview

# Deploy to production
./scripts/netlify-deploy.sh production

# Skip tests
./scripts/netlify-deploy.sh production skip-tests
```

---

## 🔧 **MANUAL DEPLOYMENT**

### **1. Build Application**
```bash
npm install
npm run build
```

### **2. Deploy to Netlify**
```bash
# Login to Netlify
netlify login

# Deploy preview
netlify deploy --dir=dist --site=better-interest-demo

# Deploy production
netlify deploy --prod --dir=dist --site=better-interest-demo
```

---

## ✅ **VERIFICATION STEPS**

### **1. Check Deployment**
```bash
# Test frontend
curl -I https://demo.kojaonline.store

# Test API connectivity (requires backend)
curl -I https://api.kojaonline.store/health
```

### **2. Browser Testing**
1. Visit https://demo.kojaonline.store
2. Check browser console for errors
3. Test navigation and responsiveness
4. Verify API calls work (with backend deployed)

---

## 🔍 **TROUBLESHOOTING**

### **Common Issues**

#### **Build Fails**
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
npm run build
```

#### **Netlify CLI Issues**
```bash
# Reinstall Netlify CLI
npm uninstall -g netlify-cli
npm install -g netlify-cli
netlify login
```

#### **Environment Variables**
```bash
# Check .env file exists
cat .env

# Verify required variables
echo $VITE_API_URL
```

#### **Permission Issues (Linux/Mac)**
```bash
# Make scripts executable
chmod +x scripts/netlify-deploy.sh
```

---

## 📋 **DEPLOYMENT CHECKLIST**

### **Pre-Deployment**
- [ ] Node.js 16+ installed
- [ ] npm dependencies installed
- [ ] Netlify CLI installed and logged in
- [ ] .env file configured
- [ ] Backend API deployed (api.kojaonline.store)

### **During Deployment**
- [ ] Build completes successfully
- [ ] No TypeScript errors
- [ ] Linting passes
- [ ] Deployment uploads successfully

### **Post-Deployment**
- [ ] Frontend loads at demo.kojaonline.store
- [ ] API calls work (check browser console)
- [ ] Navigation functions correctly
- [ ] Mobile responsiveness works
- [ ] SSL certificate is valid

---

## 🎯 **ENVIRONMENT CONFIGURATION**

### **Current Settings**
```bash
VITE_API_URL=https://api.kojaonline.store/api/v1
VITE_FRONTEND_URL=https://demo.kojaonline.store
VITE_PAYSTACK_CALLBACK_URL=https://demo.kojaonline.store/payment/callback
```

### **Netlify Configuration**
- Site Name: better-interest-demo
- Build Command: npm run build
- Publish Directory: dist
- API Proxy: /api/* → https://api.kojaonline.store/api/*

---

## 🔄 **CONTINUOUS DEPLOYMENT**

### **Automatic Deployments**
Once connected to Git repository:
- Push to `main` branch → Production deployment
- Pull requests → Preview deployments
- All branches → Branch deployments

### **Manual Deployments**
Use the scripts provided for manual control over deployments.

---

## 📞 **SUPPORT**

### **Deployment Issues**
1. Check build logs in terminal
2. Verify environment variables
3. Test locally with `npm run preview`
4. Check Netlify dashboard for errors

### **API Integration Issues**
1. Verify backend is deployed at api.kojaonline.store
2. Check CORS configuration
3. Test API endpoints directly
4. Review browser console for errors

---

## 🎉 **SUCCESS!**

Once deployed successfully:

🌐 **Frontend**: https://demo.kojaonline.store
📊 **Netlify Dashboard**: https://app.netlify.com/sites/better-interest-demo
🔧 **API**: https://api.kojaonline.store

The Better Interest application is now live and ready for users! 🚀
