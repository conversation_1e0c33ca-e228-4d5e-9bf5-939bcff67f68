import { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { LoadingScreen } from '@/components/ui/loading-screen';

const AuthCallback = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { toast } = useToast();

  useEffect(() => {
    const token = searchParams.get('token');
    const provider = searchParams.get('provider');
    const error = searchParams.get('error');

    if (error) {
      toast({
        title: "Authentication Failed",
        description: "There was an error signing in with social media. Please try again.",
        variant: "destructive",
      });
      navigate('/login');
      return;
    }

    if (token) {
      // Store the token
      localStorage.setItem('token', token);
      
      toast({
        title: "Success!",
        description: `Successfully signed in with ${provider}`,
      });
      
      // Redirect to dashboard
      navigate('/dashboard');
    } else {
      toast({
        title: "Authentication Failed",
        description: "No authentication token received. Please try again.",
        variant: "destructive",
      });
      navigate('/login');
    }
  }, [searchParams, navigate, toast]);

  return <LoadingScreen />;
};

export default AuthCallback;