import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { FintechCard } from '@/components/ui/fintech-card';
import FixedDepositForm from '@/components/savings/FixedDepositForm';
import FlexSavingsCard from '@/components/savings/FlexSavingsCard';
import SafeLockForm from '@/components/savings/SafeLockForm';
import AutoSaveSettings from '@/components/savings/AutoSaveSettings';
import TopUpPlan from '@/components/plans/TopUpPlan';
import GroupSavingsPlan from '@/components/group-savings/GroupSavingsPlan';
import { RoundUpSavings } from '@/components/savings/RoundUpSavings';
import { SocialGroupFeatures } from '@/components/group-savings/SocialGroupFeatures';
import { SocialShare } from '@/components/ui/social-share';
import { Button } from '@/components/ui/button';
import { TrendingUp, Wallet, Shield, Repeat, Target, Coins, Users, Share2 } from 'lucide-react';

const SavingsPlans = () => {
  return (
    <div className="container mx-auto p-4 sm:p-6 lg:p-8 space-y-6">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="responsive-heading font-bold mb-2">Savings Plans</h1>
          <p className="responsive-text text-muted-foreground">
            Choose the perfect savings plan to grow your wealth
          </p>
        </div>
        <SocialShare
          title="Check out these amazing savings plans!"
          description="Flexible savings options to help you achieve your financial goals"
          hashtags={['savings', 'finance', 'money', 'goals']}
        >
          <Button variant="outline">
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
        </SocialShare>
      </div>

      <Tabs defaultValue="flex" className="w-full">
        <TabsList className="grid w-full grid-cols-4 sm:grid-cols-4 md:grid-cols-8 mb-6 h-auto">
          <TabsTrigger value="flex" className="flex flex-col sm:flex-row items-center gap-1 text-xs sm:text-sm p-2 sm:p-3">
            <Wallet className="h-4 w-4 sm:h-3 sm:w-3 md:h-4 md:w-4" />
            <span className="text-xs sm:text-sm">Flex</span>
          </TabsTrigger>
          <TabsTrigger value="fixed" className="flex flex-col sm:flex-row items-center gap-1 text-xs sm:text-sm p-2 sm:p-3">
            <TrendingUp className="h-4 w-4 sm:h-3 sm:w-3 md:h-4 md:w-4" />
            <span className="text-xs sm:text-sm">Fixed</span>
          </TabsTrigger>
          <TabsTrigger value="safelock" className="flex flex-col sm:flex-row items-center gap-1 text-xs sm:text-sm p-2 sm:p-3">
            <Shield className="h-4 w-4 sm:h-3 sm:w-3 md:h-4 md:w-4" />
            <span className="text-xs sm:text-sm">SafeLock</span>
          </TabsTrigger>
          <TabsTrigger value="target" className="flex flex-col sm:flex-row items-center gap-1 text-xs sm:text-sm p-2 sm:p-3">
            <Target className="h-4 w-4 sm:h-3 sm:w-3 md:h-4 md:w-4" />
            <span className="text-xs sm:text-sm">Target</span>
          </TabsTrigger>
          <TabsTrigger value="autosave" className="flex flex-col sm:flex-row items-center gap-1 text-xs sm:text-sm p-2 sm:p-3">
            <Repeat className="h-4 w-4 sm:h-3 sm:w-3 md:h-4 md:w-4" />
            <span className="text-xs sm:text-sm">AutoSave</span>
          </TabsTrigger>
          <TabsTrigger value="roundup" className="flex flex-col sm:flex-row items-center gap-1 text-xs sm:text-sm p-2 sm:p-3">
            <Coins className="h-4 w-4 sm:h-3 sm:w-3 md:h-4 md:w-4" />
            <span className="text-xs sm:text-sm">Round-Up</span>
          </TabsTrigger>
          <TabsTrigger value="group" className="flex flex-col sm:flex-row items-center gap-1 text-xs sm:text-sm p-2 sm:p-3">
            <Users className="h-4 w-4 sm:h-3 sm:w-3 md:h-4 md:w-4" />
            <span className="text-xs sm:text-sm">Group</span>
          </TabsTrigger>
          <TabsTrigger value="social" className="flex flex-col sm:flex-row items-center gap-1 text-xs sm:text-sm p-2 sm:p-3">
            <Share2 className="h-4 w-4 sm:h-3 sm:w-3 md:h-4 md:w-4" />
            <span className="text-xs sm:text-sm">Social</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="flex" className="space-y-4 sm:space-y-6">
          <div className="w-full max-w-none">
            <FlexSavingsCard />
          </div>
        </TabsContent>

        <TabsContent value="fixed" className="space-y-4 sm:space-y-6">
          <div className="w-full max-w-none">
            <FixedDepositForm />
          </div>
        </TabsContent>

        <TabsContent value="safelock" className="space-y-4 sm:space-y-6">
          <div className="w-full max-w-none">
            <SafeLockForm />
          </div>
        </TabsContent>

        <TabsContent value="target" className="space-y-4 sm:space-y-6">
          <div className="w-full max-w-none">
            <TopUpPlan />
          </div>
        </TabsContent>

        <TabsContent value="autosave" className="space-y-4 sm:space-y-6">
          <div className="w-full max-w-none">
            <AutoSaveSettings />
          </div>
        </TabsContent>

        <TabsContent value="roundup" className="space-y-4 sm:space-y-6">
          <div className="w-full max-w-none">
            <RoundUpSavings />
          </div>
        </TabsContent>

        <TabsContent value="group" className="space-y-4 sm:space-y-6">
          <div className="w-full max-w-none">
            <GroupSavingsPlan />
          </div>
        </TabsContent>

        <TabsContent value="social" className="space-y-4 sm:space-y-6">
          <div className="w-full max-w-none">
            <SocialGroupFeatures />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SavingsPlans;