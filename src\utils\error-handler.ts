import { toast } from 'sonner';

export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
}

export class ErrorHandler {
  static handle(error: any, context?: string): AppError {
    const appError = this.createAppError(error);
    
    // Log error with context
    console.group(`🚨 Error in ${context || 'Application'}`);
    console.error('Original Error:', error);
    console.error('Processed Error:', appError);
    console.groupEnd();
    
    // Show user-friendly toast
    this.showErrorToast(appError);
    
    return appError;
  }

  static createAppError(error: any): AppError {
    const timestamp = Date.now();
    
    // Network errors
    if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
      return {
        code: 'NETWORK_ERROR',
        message: 'Please check your internet connection and try again.',
        details: error,
        timestamp,
      };
    }
    
    // HTTP errors
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;
      
      switch (status) {
        case 400:
          return {
            code: 'VALIDATION_ERROR',
            message: data?.message || 'Invalid request. Please check your input.',
            details: data,
            timestamp,
          };
        case 401:
          return {
            code: 'UNAUTHORIZED',
            message: 'Please sign in again.',
            details: data,
            timestamp,
          };
        case 403:
          return {
            code: 'FORBIDDEN',
            message: 'You do not have permission to perform this action.',
            details: data,
            timestamp,
          };
        case 404:
          return {
            code: 'NOT_FOUND',
            message: 'The requested resource was not found.',
            details: data,
            timestamp,
          };
        case 429:
          return {
            code: 'RATE_LIMITED',
            message: 'Too many requests. Please wait a moment and try again.',
            details: data,
            timestamp,
          };
        case 500:
        case 502:
        case 503:
        case 504:
          return {
            code: 'SERVER_ERROR',
            message: 'Server error. Please try again later.',
            details: data,
            timestamp,
          };
        default:
          return {
            code: 'HTTP_ERROR',
            message: data?.message || `Request failed with status ${status}`,
            details: data,
            timestamp,
          };
      }
    }
    
    // JavaScript errors
    if (error instanceof Error) {
      return {
        code: 'JAVASCRIPT_ERROR',
        message: error.message || 'An unexpected error occurred.',
        details: {
          name: error.name,
          stack: error.stack,
        },
        timestamp,
      };
    }
    
    // Unknown errors
    return {
      code: 'UNKNOWN_ERROR',
      message: typeof error === 'string' ? error : 'An unexpected error occurred.',
      details: error,
      timestamp,
    };
  }

  static showErrorToast(error: AppError) {
    const isImportantError = ['UNAUTHORIZED', 'SERVER_ERROR', 'NETWORK_ERROR'].includes(error.code);
    
    toast.error(error.message, {
      duration: isImportantError ? 10000 : 5000,
      description: error.code !== 'UNKNOWN_ERROR' ? `Error code: ${error.code}` : undefined,
    });
  }

  static handleAuthError() {
    localStorage.clear();
    window.location.href = '/login';
    toast.error('Your session has expired. Please sign in again.');
  }

  static handleNetworkError() {
    toast.error('Network connection lost. Please check your internet connection.');
  }
}

// Global error handler for unhandled promise rejections
if (typeof window !== 'undefined') {
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    ErrorHandler.handle(event.reason, 'Unhandled Promise Rejection');
    event.preventDefault();
  });
}