import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { FintechCard } from '@/components/ui/fintech-card';
import { FloatingLabelInput } from '@/components/ui/floating-label-input';
import { useToast } from '@/hooks/use-toast';
import { PaystackIntegration } from '@/components/payments/PaystackIntegration';
import { PolicyModal } from '@/components/modals/PolicyModal';
import { Wallet, Plus, Minus, TrendingUp } from 'lucide-react';

const FlexSavingsCard = () => {
  const [balance, setBalance] = useState(45000);
  const [amount, setAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPayment, setShowPayment] = useState(false);
  const [showPolicy, setShowPolicy] = useState(false);
  const [actionType, setActionType] = useState<'deposit' | 'withdraw'>('deposit');
  const { toast } = useToast();

  const dailyInterest = 0.0411; // 15% annually
  const todaysEarning = balance * (dailyInterest / 100);

  const handleDeposit = () => {
    if (!amount || parseFloat(amount) < 100) {
      toast({
        title: "Error",
        description: "Minimum deposit amount is ₦100",
        variant: "destructive",
      });
      return;
    }
    setActionType('deposit');
    setShowPolicy(true);
  };

  const handleWithdraw = () => {
    if (!amount || parseFloat(amount) > balance) {
      toast({
        title: "Error",
        description: "Invalid withdrawal amount",
        variant: "destructive",
      });
      return;
    }
    setActionType('withdraw');
    processWithdrawal();
  };

  const processWithdrawal = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      setBalance(prev => prev - parseFloat(amount));
      toast({
        title: "Withdrawal Successful!",
        description: `₦${parseFloat(amount).toLocaleString()} withdrawn from your Flex Savings.`,
      });
      setAmount('');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process withdrawal.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePolicyAccept = () => {
    setShowPolicy(false);
    if (actionType === 'deposit') {
      setShowPayment(true);
    }
  };

  const handlePaymentSuccess = (depositAmount: number) => {
    setBalance(prev => prev + depositAmount);
    setShowPayment(false);
    setAmount('');
    toast({
      title: "Deposit Successful!",
      description: `₦${depositAmount.toLocaleString()} added to your Flex Savings.`,
    });
  };

  const handlePaymentError = (error: string) => {
    setShowPayment(false);
    toast({
      title: "Payment Failed",
      description: error,
      variant: "destructive",
    });
  };

  if (showPayment) {
    return (
      <FintechCard className="p-6">
        <div className="mb-4">
          <Button 
            variant="outline" 
            onClick={() => setShowPayment(false)}
            className="mb-4"
          >
            ← Back to Flex Savings
          </Button>
        </div>
        <PaystackIntegration
          amount={parseFloat(amount)}
          title="Deposit to Flex Savings"
          onSuccess={handlePaymentSuccess}
          onError={handlePaymentError}
          purpose="flex_savings"
        />
      </FintechCard>
    );
  }

  return (
    <>
      <FintechCard variant="glassmorphic" className="p-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
          <Wallet className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h2 className="text-xl font-semibold">Flex Savings</h2>
          <p className="text-sm text-muted-foreground">Save with daily interest</p>
        </div>
      </div>

      <div className="space-y-4 mb-6">
        <div className="text-center">
          <p className="text-sm text-muted-foreground">Current Balance</p>
          <p className="text-3xl font-bold">₦{balance.toLocaleString()}</p>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-1">
            <TrendingUp className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-green-600">Today's Earning</span>
          </div>
          <p className="text-lg font-semibold text-green-600">
            +₦{todaysEarning.toFixed(2)}
          </p>
          <p className="text-xs text-green-600/80">15% annual rate</p>
        </div>
      </div>

      <div className="space-y-4">
        <FloatingLabelInput
          id="amount"
          type="number"
          label="Amount (₦)"
          value={amount}
          onChange={(e) => setAmount(e.target.value)}
          placeholder="1,000"
        />

        <div className="grid grid-cols-2 gap-3">
          <Button 
            variant="outline" 
            onClick={handleDeposit}
            disabled={loading || !amount}
            className="border-primary text-primary hover:bg-primary hover:text-white"
          >
            <Plus className="mr-2 h-4 w-4" />
            Deposit
          </Button>
          <Button 
            variant="outline" 
            onClick={handleWithdraw}
            disabled={loading || !amount || parseFloat(amount) > balance}
            className="border-gray-300 text-gray-600 hover:bg-gray-100"
          >
            <Minus className="mr-2 h-4 w-4" />
            Withdraw
          </Button>
        </div>

        {/* Powered by section */}
        <div className="mt-6 pt-4 border-t text-center">
          <p className="text-xs text-muted-foreground">
            INSURED BY THE NDIC & AMAC MFB LTD
          </p>
        </div>
      </div>
    </FintechCard>

    {/* Policy Modal */}
    <PolicyModal
      open={showPolicy}
      onOpenChange={setShowPolicy}
      onAccept={handlePolicyAccept}
      title="Flex Savings Terms"
      type="general"
    />
    </>
  );
};

export default FlexSavingsCard;