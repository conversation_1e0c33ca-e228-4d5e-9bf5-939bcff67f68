const express = require('express');
const router = express.Router();
const { auth, admin } = require('../middleware/auth');
const User = require('../models/User');
const SavingsPlan = require('../models/SavingsPlan');
const FixedDeposit = require('../models/FixedDeposit');
const Investment = require('../models/Investment');
const BillPayment = require('../models/BillPayment');
const Referral = require('../models/Referral');

// User dashboard analytics
router.get('/user-dashboard', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { period = '30' } = req.query; // days
    
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    // Get user's financial overview
    const user = await User.findById(userId);
    
    // Savings analytics
    const savingsPlans = await SavingsPlan.find({ userId });
    const totalSavings = savingsPlans.reduce((sum, plan) => sum + plan.currentAmount, 0);
    const activePlans = savingsPlans.filter(plan => plan.status === 'active').length;
    
    // Fixed deposits analytics
    const fixedDeposits = await FixedDeposit.find({ userId });
    const totalFixedDeposits = fixedDeposits.reduce((sum, fd) => sum + fd.amount, 0);
    const activeDeposits = fixedDeposits.filter(fd => fd.status === 'active').length;
    
    // Investment analytics
    const investments = await Investment.find({ userId });
    const totalInvestments = investments.reduce((sum, inv) => sum + inv.amount, 0);
    const currentInvestmentValue = investments.reduce((sum, inv) => sum + inv.currentValue, 0);
    
    // Recent transactions (last 30 days)
    const recentSavings = await SavingsPlan.aggregate([
      { $match: { userId: mongoose.Types.ObjectId(userId) } },
      { $unwind: '$transactions' },
      { $match: { 'transactions.date': { $gte: startDate } } },
      { $group: { _id: null, total: { $sum: '$transactions.amount' }, count: { $sum: 1 } } }
    ]);

    // Bill payments analytics
    const billPayments = await BillPayment.find({ 
      userId, 
      createdAt: { $gte: startDate } 
    });
    const totalBillPayments = billPayments.reduce((sum, bill) => sum + bill.amount, 0);

    // Referral analytics
    const referralStats = await Referral.getReferralStats(userId);

    // Calculate growth metrics
    const previousPeriodStart = new Date(startDate);
    previousPeriodStart.setDate(previousPeriodStart.getDate() - parseInt(period));
    
    const previousSavings = await SavingsPlan.aggregate([
      { $match: { userId: mongoose.Types.ObjectId(userId) } },
      { $unwind: '$transactions' },
      { $match: { 
        'transactions.date': { 
          $gte: previousPeriodStart, 
          $lt: startDate 
        } 
      }},
      { $group: { _id: null, total: { $sum: '$transactions.amount' } } }
    ]);

    const currentPeriodSavings = recentSavings.length > 0 ? recentSavings[0].total : 0;
    const previousPeriodSavings = previousSavings.length > 0 ? previousSavings[0].total : 0;
    const savingsGrowth = previousPeriodSavings > 0 ? 
      ((currentPeriodSavings - previousPeriodSavings) / previousPeriodSavings) * 100 : 0;

    const analytics = {
      overview: {
        totalBalance: user.balance || 0,
        totalSavings,
        totalFixedDeposits,
        totalInvestments,
        currentInvestmentValue,
        netWorth: (user.balance || 0) + totalSavings + totalFixedDeposits + currentInvestmentValue
      },
      activity: {
        activeSavingsPlans: activePlans,
        activeFixedDeposits: activeDeposits,
        activeInvestments: investments.filter(inv => inv.status === 'active').length,
        recentTransactions: recentSavings.length > 0 ? recentSavings[0].count : 0,
        totalBillPayments: billPayments.length,
        billPaymentsAmount: totalBillPayments
      },
      growth: {
        savingsGrowth: Math.round(savingsGrowth * 100) / 100,
        period: parseInt(period),
        currentPeriodSavings,
        previousPeriodSavings
      },
      referrals: referralStats,
      goals: {
        savingsGoalProgress: user.savingsGoal ? (totalSavings / user.savingsGoal) * 100 : 0,
        savingsGoal: user.savingsGoal || 0
      }
    };

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Error fetching user analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch analytics'
    });
  }
});

// Admin dashboard analytics
router.get('/admin-dashboard', [auth, admin], async (req, res) => {
  try {
    const { period = '30' } = req.query;
    
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    // User analytics
    const totalUsers = await User.countDocuments();
    const newUsers = await User.countDocuments({ createdAt: { $gte: startDate } });
    const activeUsers = await User.countDocuments({ 
      lastLoginAt: { $gte: startDate },
      isActive: true 
    });

    // Financial analytics
    const totalSavings = await SavingsPlan.aggregate([
      { $group: { _id: null, total: { $sum: '$currentAmount' } } }
    ]);

    const totalFixedDeposits = await FixedDeposit.aggregate([
      { $match: { status: 'active' } },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);

    const totalInvestments = await Investment.aggregate([
      { $match: { status: 'active' } },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);

    // Transaction analytics
    const recentTransactions = await SavingsPlan.aggregate([
      { $unwind: '$transactions' },
      { $match: { 'transactions.date': { $gte: startDate } } },
      {
        $group: {
          _id: '$transactions.type',
          count: { $sum: 1 },
          totalAmount: { $sum: '$transactions.amount' }
        }
      }
    ]);

    // Bill payment analytics
    const billPaymentStats = await BillPayment.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      {
        $group: {
          _id: '$billType',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          revenue: { $sum: '$commission' }
        }
      }
    ]);

    // Referral analytics
    const referralStats = await Referral.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalReward: { $sum: '$rewardAmount' }
        }
      }
    ]);

    // Growth metrics
    const userGrowth = await User.aggregate([
      {
        $match: {
          createdAt: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Revenue analytics
    const totalRevenue = billPaymentStats.reduce((sum, stat) => sum + (stat.revenue || 0), 0);
    
    const analytics = {
      users: {
        total: totalUsers,
        new: newUsers,
        active: activeUsers,
        growth: userGrowth
      },
      financial: {
        totalSavings: totalSavings.length > 0 ? totalSavings[0].total : 0,
        totalFixedDeposits: totalFixedDeposits.length > 0 ? totalFixedDeposits[0].total : 0,
        totalInvestments: totalInvestments.length > 0 ? totalInvestments[0].total : 0,
        totalRevenue
      },
      transactions: {
        recent: recentTransactions,
        billPayments: billPaymentStats
      },
      referrals: referralStats,
      period: parseInt(period)
    };

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Error fetching admin analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch admin analytics'
    });
  }
});

// Financial reports
router.get('/financial-reports', [auth, admin], async (req, res) => {
  try {
    const { startDate, endDate, reportType = 'summary' } = req.query;
    
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();

    let report = {};

    switch (reportType) {
      case 'savings':
        report = await generateSavingsReport(start, end);
        break;
      case 'investments':
        report = await generateInvestmentsReport(start, end);
        break;
      case 'revenue':
        report = await generateRevenueReport(start, end);
        break;
      default:
        report = await generateSummaryReport(start, end);
    }

    res.json({
      success: true,
      data: {
        reportType,
        period: { startDate: start, endDate: end },
        ...report
      }
    });
  } catch (error) {
    console.error('Error generating financial report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate financial report'
    });
  }
});

// Helper functions for report generation
async function generateSummaryReport(startDate, endDate) {
  // Implementation for summary report
  return {
    totalUsers: await User.countDocuments({ createdAt: { $gte: startDate, $lte: endDate } }),
    totalSavings: 0, // Calculate from aggregation
    totalRevenue: 0, // Calculate from bill payments
    summary: 'Summary report data'
  };
}

async function generateSavingsReport(startDate, endDate) {
  // Implementation for savings report
  return {
    newSavingsPlans: await SavingsPlan.countDocuments({ createdAt: { $gte: startDate, $lte: endDate } }),
    totalSavingsAmount: 0, // Calculate from aggregation
    summary: 'Savings report data'
  };
}

async function generateInvestmentsReport(startDate, endDate) {
  // Implementation for investments report
  return {
    newInvestments: await Investment.countDocuments({ investmentDate: { $gte: startDate, $lte: endDate } }),
    totalInvestmentAmount: 0, // Calculate from aggregation
    summary: 'Investments report data'
  };
}

async function generateRevenueReport(startDate, endDate) {
  // Implementation for revenue report
  const billPayments = await BillPayment.aggregate([
    { $match: { createdAt: { $gte: startDate, $lte: endDate } } },
    { $group: { _id: null, totalRevenue: { $sum: '$commission' }, count: { $sum: 1 } } }
  ]);

  return {
    totalRevenue: billPayments.length > 0 ? billPayments[0].totalRevenue : 0,
    transactionCount: billPayments.length > 0 ? billPayments[0].count : 0,
    summary: 'Revenue report data'
  };
}

module.exports = router;
