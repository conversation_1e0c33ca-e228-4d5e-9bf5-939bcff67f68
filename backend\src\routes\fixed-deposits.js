const express = require('express');
const router = express.Router();
const { auth, admin } = require('../middleware/auth');
const FixedDeposit = require('../models/FixedDeposit');
const User = require('../models/User');
const { validateFixedDeposit } = require('../middleware/validation');

// Get current fixed deposit rates
router.get('/rates', async (req, res) => {
  try {
    // Fixed deposit rates based on duration
    const rates = [
      { duration: 30, rate: 12.0, label: '1 Month' },
      { duration: 60, rate: 13.5, label: '2 Months' },
      { duration: 90, rate: 15.0, label: '3 Months' },
      { duration: 180, rate: 17.5, label: '6 Months' },
      { duration: 365, rate: 20.0, label: '12 Months' }
    ];

    res.json({
      success: true,
      data: {
        rates,
        minimumAmount: 10000,
        maximumAmount: 10000000,
        penaltyRate: 25,
        compoundingFrequency: 'daily'
      }
    });
  } catch (error) {
    console.error('Error fetching fixed deposit rates:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch rates'
    });
  }
});

// Create new fixed deposit
router.post('/', [auth, validateFixedDeposit], async (req, res) => {
  try {
    const { amount, duration, autoRenewal } = req.body;
    const userId = req.user.id;

    // Check user balance
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    if (user.balance < amount) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient balance'
      });
    }

    // Determine interest rate based on duration
    let interestRate;
    if (duration <= 30) interestRate = 12.0;
    else if (duration <= 60) interestRate = 13.5;
    else if (duration <= 90) interestRate = 15.0;
    else if (duration <= 180) interestRate = 17.5;
    else interestRate = 20.0;

    // Create fixed deposit
    const fixedDeposit = new FixedDeposit({
      userId,
      amount,
      duration,
      interestRate,
      autoRenewal: autoRenewal || false,
      metadata: {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    });

    await fixedDeposit.save();

    // Deduct amount from user balance
    user.balance -= amount;
    await user.save();

    // Populate user details for response
    await fixedDeposit.populate('userId', 'firstName lastName email');

    res.status(201).json({
      success: true,
      message: 'Fixed deposit created successfully',
      data: fixedDeposit
    });
  } catch (error) {
    console.error('Error creating fixed deposit:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create fixed deposit',
      error: error.message
    });
  }
});

// Get user's fixed deposits
router.get('/', auth, async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;
    const userId = req.user.id;

    const query = { userId };
    if (status) query.status = status;

    const deposits = await FixedDeposit.find(query)
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('userId', 'firstName lastName email');

    const total = await FixedDeposit.countDocuments(query);

    res.json({
      success: true,
      data: {
        deposits,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching fixed deposits:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch fixed deposits'
    });
  }
});

// Get specific fixed deposit
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const deposit = await FixedDeposit.findOne({ _id: id, userId })
      .populate('userId', 'firstName lastName email');

    if (!deposit) {
      return res.status(404).json({
        success: false,
        message: 'Fixed deposit not found'
      });
    }

    res.json({
      success: true,
      data: deposit
    });
  } catch (error) {
    console.error('Error fetching fixed deposit:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch fixed deposit'
    });
  }
});

// Break fixed deposit early
router.post('/:id/break', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const deposit = await FixedDeposit.findOne({ _id: id, userId });

    if (!deposit) {
      return res.status(404).json({
        success: false,
        message: 'Fixed deposit not found'
      });
    }

    if (deposit.status !== 'active') {
      return res.status(400).json({
        success: false,
        message: 'Only active deposits can be broken'
      });
    }

    // Calculate breakage details
    const currentInterest = deposit.calculateCurrentInterest();
    const penalty = (currentInterest * deposit.penaltyRate) / 100;
    const netInterest = Math.max(0, currentInterest - penalty);
    const finalAmount = deposit.amount + netInterest;

    // Break the deposit
    await deposit.breakDeposit();

    // Credit user account
    const user = await User.findById(userId);
    user.balance += finalAmount;
    await user.save();

    res.json({
      success: true,
      message: 'Fixed deposit broken successfully',
      data: {
        originalAmount: deposit.amount,
        interestEarned: currentInterest,
        penalty,
        netInterest,
        finalAmount,
        creditedAmount: finalAmount
      }
    });
  } catch (error) {
    console.error('Error breaking fixed deposit:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to break fixed deposit',
      error: error.message
    });
  }
});

// Calculate interest for a potential deposit
router.post('/calculate-interest', async (req, res) => {
  try {
    const { amount, duration } = req.body;

    if (!amount || !duration) {
      return res.status(400).json({
        success: false,
        message: 'Amount and duration are required'
      });
    }

    // Determine interest rate
    let interestRate;
    if (duration <= 30) interestRate = 12.0;
    else if (duration <= 60) interestRate = 13.5;
    else if (duration <= 90) interestRate = 15.0;
    else if (duration <= 180) interestRate = 17.5;
    else interestRate = 20.0;

    // Calculate projected returns
    const principal = parseFloat(amount);
    const rate = interestRate / 100;
    const dailyRate = rate / 365;
    const maturityAmount = principal * Math.pow(1 + dailyRate, duration);
    const totalInterest = maturityAmount - principal;

    res.json({
      success: true,
      data: {
        principal,
        duration,
        interestRate,
        maturityAmount: Math.round(maturityAmount),
        totalInterest: Math.round(totalInterest),
        dailyInterest: Math.round(totalInterest / duration),
        effectiveAnnualRate: ((maturityAmount / principal) ** (365 / duration) - 1) * 100
      }
    });
  } catch (error) {
    console.error('Error calculating interest:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to calculate interest'
    });
  }
});

// Admin: Get all fixed deposits
router.get('/admin/all', [auth, admin], async (req, res) => {
  try {
    const { status, page = 1, limit = 20 } = req.query;

    const query = {};
    if (status) query.status = status;

    const deposits = await FixedDeposit.find(query)
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('userId', 'firstName lastName email phone');

    const total = await FixedDeposit.countDocuments(query);

    // Calculate summary statistics
    const stats = await FixedDeposit.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          totalInterest: { $sum: '$accruedInterest' }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        deposits,
        stats,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching admin fixed deposits:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch fixed deposits'
    });
  }
});

module.exports = router;
