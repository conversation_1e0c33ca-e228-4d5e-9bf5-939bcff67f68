import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { paystackService } from '@/services/paystack';
import { useToast } from '@/hooks/use-toast';
import { CreditCard, Calendar, DollarSign, Repeat } from 'lucide-react';

interface AutoSavePaymentProps {
  userId: string;
  userEmail: string;
  userName: string;
}

interface AutoSavePlan {
  id: string;
  name: string;
  targetAmount: number;
  frequency: 'daily' | 'weekly' | 'monthly';
  amount: number;
  isActive: boolean;
  nextDebitDate: Date;
  paystackPlanCode?: string;
  subscriptionCode?: string;
}

export const AutoSavePayment: React.FC<AutoSavePaymentProps> = ({
  userId,
  userEmail,
  userName
}) => {
  const { toast } = useToast();
  const [autoSavePlans, setAutoSavePlans] = useState<AutoSavePlan[]>([]);
  const [isCreatingPlan, setIsCreatingPlan] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    targetAmount: '',
    frequency: 'monthly' as 'daily' | 'weekly' | 'monthly',
    amount: ''
  });

  // Create new auto-save plan
  const handleCreatePlan = async () => {
    if (!formData.name || !formData.targetAmount || !formData.amount) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    setIsCreatingPlan(true);
    try {
      // Create Paystack plan
      const planData = {
        name: `AutoSave - ${formData.name}`,
        amount: parseFloat(formData.amount) * 100, // Convert to kobo
        interval: formData.frequency,
        description: `Auto-save plan for ${formData.name}`
      };

      const paystackPlan = await paystackService.createPlan(planData);
      
      if (paystackPlan.status) {
        // Create local plan
        const newPlan: AutoSavePlan = {
          id: `plan_${Date.now()}`,
          name: formData.name,
          targetAmount: parseFloat(formData.targetAmount),
          frequency: formData.frequency,
          amount: parseFloat(formData.amount),
          isActive: false,
          nextDebitDate: new Date(),
          paystackPlanCode: paystackPlan.data.plan_code
        };

        setAutoSavePlans(prev => [...prev, newPlan]);
        
        // Reset form
        setFormData({
          name: '',
          targetAmount: '',
          frequency: 'monthly',
          amount: ''
        });

        toast({
          title: "Plan Created",
          description: "Auto-save plan created successfully. Activate it to start automatic deductions.",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create auto-save plan",
        variant: "destructive"
      });
    } finally {
      setIsCreatingPlan(false);
    }
  };

  // Activate auto-save plan
  const handleActivatePlan = async (planId: string) => {
    const plan = autoSavePlans.find(p => p.id === planId);
    if (!plan || !plan.paystackPlanCode) return;

    try {
      // Initialize payment for authorization
      const paymentData = {
        email: userEmail,
        amount: plan.amount * 100, // Convert to kobo
        reference: paystackService.generateReference(),
        plan: plan.paystackPlanCode,
        metadata: {
          planId: plan.id,
          userId,
          type: 'auto_save_activation'
        }
      };

      const result = await paystackService.initializePayment(paymentData);
      
      if (result.status) {
        // Open Paystack popup
        paystackService.openPaystackPopup(
          paymentData,
          (reference) => {
            // Payment successful - activate plan
            setAutoSavePlans(prev => prev.map(p => 
              p.id === planId 
                ? { ...p, isActive: true, nextDebitDate: getNextDebitDate(p.frequency) }
                : p
            ));
            
            toast({
              title: "Plan Activated",
              description: "Auto-save plan activated successfully. Automatic deductions will begin.",
            });
          },
          () => {
            toast({
              title: "Payment Cancelled",
              description: "Plan activation was cancelled",
              variant: "destructive"
            });
          }
        );
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to activate auto-save plan",
        variant: "destructive"
      });
    }
  };

  // Deactivate auto-save plan
  const handleDeactivatePlan = (planId: string) => {
    setAutoSavePlans(prev => prev.map(p => 
      p.id === planId ? { ...p, isActive: false } : p
    ));
    
    toast({
      title: "Plan Deactivated",
      description: "Auto-save plan has been deactivated",
    });
  };

  // Get next debit date based on frequency
  const getNextDebitDate = (frequency: string): Date => {
    const now = new Date();
    switch (frequency) {
      case 'daily':
        return new Date(now.getTime() + 24 * 60 * 60 * 1000);
      case 'weekly':
        return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
      case 'monthly':
        return new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
      default:
        return now;
    }
  };

  // Load Paystack script
  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://js.paystack.co/v1/inline.js';
    script.async = true;
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return (
    <div className="space-y-6">
      {/* Create New Plan */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Repeat className="h-5 w-5" />
            Create Auto-Save Plan
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="planName">Plan Name</Label>
              <Input
                id="planName"
                placeholder="e.g., Emergency Fund"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="targetAmount">Target Amount (₦)</Label>
              <Input
                id="targetAmount"
                type="number"
                placeholder="50000"
                value={formData.targetAmount}
                onChange={(e) => setFormData(prev => ({ ...prev, targetAmount: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="frequency">Frequency</Label>
              <Select value={formData.frequency} onValueChange={(value: 'daily' | 'weekly' | 'monthly') => 
                setFormData(prev => ({ ...prev, frequency: value }))
              }>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="amount">Amount per Payment (₦)</Label>
              <Input
                id="amount"
                type="number"
                placeholder="5000"
                value={formData.amount}
                onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
              />
            </div>
          </div>
          <Button 
            onClick={handleCreatePlan} 
            disabled={isCreatingPlan}
            className="w-full"
          >
            <CreditCard className="h-4 w-4 mr-2" />
            {isCreatingPlan ? 'Creating Plan...' : 'Create Auto-Save Plan'}
          </Button>
        </CardContent>
      </Card>

      {/* Existing Plans */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Your Auto-Save Plans</h3>
        {autoSavePlans.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-muted-foreground">No auto-save plans yet. Create one to get started!</p>
            </CardContent>
          </Card>
        ) : (
          autoSavePlans.map((plan) => (
            <Card key={plan.id}>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{plan.name}</h4>
                      <Badge variant={plan.isActive ? "default" : "secondary"}>
                        {plan.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <DollarSign className="h-4 w-4" />
                        ₦{plan.amount.toLocaleString()} {plan.frequency}
                      </span>
                      <span className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        Target: ₦{plan.targetAmount.toLocaleString()}
                      </span>
                      {plan.isActive && (
                        <span>Next: {plan.nextDebitDate.toLocaleDateString()}</span>
                      )}
                    </div>
                  </div>
                  <div>
                    {plan.isActive ? (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleDeactivatePlan(plan.id)}
                      >
                        Deactivate
                      </Button>
                    ) : (
                      <Button 
                        size="sm"
                        onClick={() => handleActivatePlan(plan.id)}
                      >
                        Activate
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};
