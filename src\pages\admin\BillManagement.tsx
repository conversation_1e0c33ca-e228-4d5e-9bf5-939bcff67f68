import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { BillProviderManagement } from '@/components/admin/BillProviderManagement';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useToast } from '@/hooks/use-toast';
import { Settings, BarChart3, Phone, Zap, Tv, Wifi, Plane, GraduationCap, TrendingUp, DollarSign, Users, Activity } from 'lucide-react';
import axios from 'axios';
import { format } from 'date-fns';

interface BillStats {
  totalRevenue: number;
  totalTransactions: number;
  successRate: number;
  topCategories: Array<{
    category: string;
    count: number;
    revenue: number;
  }>;
}

interface RecentPayment {
  _id: string;
  reference: string;
  category: string;
  amount: number;
  status: string;
  userId: {
    name: string;
    email: string;
  };
  providerId: {
    name: string;
  };
  createdAt: string;
}

const categoryIcons = {
  airtime: Phone,
  data: Wifi,
  electricity: Zap,
  cable_tv: Tv,
  internet: Wifi,
  flight: Plane,
  education: GraduationCap,
  betting: Phone
};

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  processing: 'bg-blue-100 text-blue-800',
  completed: 'bg-green-100 text-green-800',
  failed: 'bg-red-100 text-red-800',
  cancelled: 'bg-gray-100 text-gray-800'
};

export default function BillManagement() {
  const [stats, setStats] = useState<BillStats | null>(null);
  const [recentPayments, setRecentPayments] = useState<RecentPayment[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      // Fetch recent payments
      const paymentsResponse = await axios.get('/api/v1/bills/admin/payments?limit=10');
      setRecentPayments(paymentsResponse.data.data);

      // Mock stats for demo
      setStats({
        totalRevenue: 2500000,
        totalTransactions: 1234,
        successRate: 95.8,
        topCategories: [
          { category: 'airtime', count: 456, revenue: 890000 },
          { category: 'data', count: 324, revenue: 654000 },
          { category: 'electricity', count: 234, revenue: 567000 },
          { category: 'cable_tv', count: 123, revenue: 234000 },
          { category: 'internet', count: 97, revenue: 154000 }
        ]
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch dashboard data',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Bill Payment Management</h1>
          <p className="text-muted-foreground">Manage bill payment providers and monitor transactions</p>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="providers" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Providers
          </TabsTrigger>
          <TabsTrigger value="transactions" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Transactions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Stats Cards */}
          {stats && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">₦{stats.totalRevenue.toLocaleString()}</div>
                    <p className="text-xs text-muted-foreground">
                      +12.5% from last month
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
                    <Activity className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.totalTransactions.toLocaleString()}</div>
                    <p className="text-xs text-muted-foreground">
                      +8.2% from last month
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.successRate}%</div>
                    <p className="text-xs text-muted-foreground">
                      +2.1% from last month
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">2,834</div>
                    <p className="text-xs text-muted-foreground">
                      +15.3% from last month
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Top Categories */}
              <Card>
                <CardHeader>
                  <CardTitle>Top Service Categories</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {stats.topCategories.map((category) => {
                      const Icon = categoryIcons[category.category as keyof typeof categoryIcons];
                      return (
                        <div key={category.category} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Icon className="h-5 w-5" />
                            <div>
                              <p className="font-medium capitalize">{category.category.replace('_', ' ')}</p>
                              <p className="text-sm text-muted-foreground">{category.count} transactions</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-medium">₦{category.revenue.toLocaleString()}</p>
                            <p className="text-sm text-muted-foreground">
                              {((category.revenue / stats.totalRevenue) * 100).toFixed(1)}%
                            </p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </>
          )}

          {/* Recent Transactions */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Transactions</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Service</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Reference</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentPayments.map((payment) => {
                    const Icon = categoryIcons[payment.category as keyof typeof categoryIcons];
                    return (
                      <TableRow key={payment._id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{payment.userId.name}</div>
                            <div className="text-sm text-muted-foreground">{payment.userId.email}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Icon className="h-4 w-4" />
                            <div>
                              <div className="font-medium">{payment.providerId.name}</div>
                              <div className="text-sm text-muted-foreground capitalize">
                                {payment.category.replace('_', ' ')}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">₦{payment.amount.toLocaleString()}</div>
                        </TableCell>
                        <TableCell>
                          <Badge className={statusColors[payment.status as keyof typeof statusColors]}>
                            {payment.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {format(new Date(payment.createdAt), 'MMM dd, yyyy')}
                          </div>
                        </TableCell>
                        <TableCell>
                          <code className="text-xs bg-muted px-2 py-1 rounded">
                            {payment.reference}
                          </code>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="providers" className="space-y-6">
          <BillProviderManagement />
        </TabsContent>

        <TabsContent value="transactions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>All Transactions</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Detailed transaction management coming soon. Use the Overview tab to see recent transactions.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}