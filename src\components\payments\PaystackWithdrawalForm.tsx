import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { CreditCard, Building2, User, CircleDollarSign, CheckCircle, AlertCircle } from "lucide-react";
import { useBalance } from "@/hooks/use-balance";
import { paystackService } from "@/services/paystack";

interface Bank {
  id: number;
  name: string;
  code: string;
  longcode: string;
  gateway: string;
  pay_with_bank: boolean;
  active: boolean;
  country: string;
  currency: string;
  type: string;
  is_deleted: boolean;
  createdAt: string;
  updatedAt: string;
}

interface PaystackWithdrawalFormProps {
  onSubmit?: (data: any) => void;
  isLoading?: boolean;
}

export function PaystackWithdrawalForm({ 
  onSubmit, 
  isLoading = false
}: PaystackWithdrawalFormProps) {
  const { balance } = useBalance();
  const [banks, setBanks] = useState<Bank[]>([]);
  const [loadingBanks, setLoadingBanks] = useState(true);
  const [verifyingAccount, setVerifyingAccount] = useState(false);
  const [accountVerified, setAccountVerified] = useState(false);
  const [processing, setProcessing] = useState(false);
  
  const [formData, setFormData] = useState({
    amount: "",
    bankCode: "",
    accountNumber: "",
    accountName: "",
    reason: "",
  });

  // Load banks on component mount
  useEffect(() => {
    const loadBanks = async () => {
      try {
        setLoadingBanks(true);
        const response = await paystackService.getBanks();
        
        if (response.success || response.status) {
          // Filter active Nigerian banks
          const nigerianBanks = response.data.filter((bank: Bank) => 
            bank.active && bank.country === 'Nigeria' && bank.type === 'nuban'
          );
          setBanks(nigerianBanks);
        } else {
          throw new Error(response.message);
        }
      } catch (error) {
        console.error('Error loading banks:', error);
        toast.error('Failed to load banks. Using fallback list.');
        
        // Fallback to common Nigerian banks
        setBanks([
          { id: 1, name: 'Access Bank', code: '044', longcode: '*********', gateway: 'emandate', pay_with_bank: false, active: true, country: 'Nigeria', currency: 'NGN', type: 'nuban', is_deleted: false, createdAt: '', updatedAt: '' },
          { id: 2, name: 'Guaranty Trust Bank', code: '058', longcode: '*********', gateway: 'emandate', pay_with_bank: false, active: true, country: 'Nigeria', currency: 'NGN', type: 'nuban', is_deleted: false, createdAt: '', updatedAt: '' },
          { id: 3, name: 'First Bank of Nigeria', code: '011', longcode: '*********', gateway: 'emandate', pay_with_bank: false, active: true, country: 'Nigeria', currency: 'NGN', type: 'nuban', is_deleted: false, createdAt: '', updatedAt: '' },
          { id: 4, name: 'United Bank for Africa', code: '033', longcode: '*********', gateway: 'emandate', pay_with_bank: false, active: true, country: 'Nigeria', currency: 'NGN', type: 'nuban', is_deleted: false, createdAt: '', updatedAt: '' },
          { id: 5, name: 'Zenith Bank', code: '057', longcode: '*********', gateway: 'emandate', pay_with_bank: false, active: true, country: 'Nigeria', currency: 'NGN', type: 'nuban', is_deleted: false, createdAt: '', updatedAt: '' }
        ]);
      } finally {
        setLoadingBanks(false);
      }
    };

    loadBanks();
  }, []);

  // Load saved payout settings if available
  useEffect(() => {
    const savedSettings = localStorage.getItem('user_payout_settings');
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      if (settings.payoutMode === 'bank_transfer') {
        setFormData(prev => ({
          ...prev,
          bankCode: settings.bankCode || '',
          accountNumber: settings.accountNumber || '',
          accountName: settings.accountName || ''
        }));
      }
    }
  }, []);

  const handleChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Reset account verification when account details change
    if (name === 'accountNumber' || name === 'bankCode') {
      setAccountVerified(false);
    }
  };

  const verifyAccount = async () => {
    if (!formData.accountNumber || !formData.bankCode) {
      toast.error("Please select a bank and enter account number");
      return;
    }

    try {
      setVerifyingAccount(true);
      const response = await paystackService.verifyAccount(formData.accountNumber, formData.bankCode);
      
      if (response.success || response.status) {
        setFormData(prev => ({
          ...prev,
          accountName: response.data.accountName
        }));
        setAccountVerified(true);
        toast.success(`Account verified: ${response.data.accountName}`);
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      console.error('Account verification error:', error);
      toast.error('Account verification failed. Please check your details.');
      setAccountVerified(false);
    } finally {
      setVerifyingAccount(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const amount = parseFloat(formData.amount);
    
    if (amount <= 0) {
      toast.error("Please enter a valid withdrawal amount");
      return;
    }
    
    if (amount > balance) {
      toast.error("Your withdrawal amount exceeds your available balance");
      return;
    }
    
    if (!formData.bankCode || !formData.accountNumber || !formData.accountName) {
      toast.error("Please provide all required banking details");
      return;
    }

    if (!accountVerified) {
      toast.error("Please verify your account details first");
      return;
    }
    
    try {
      setProcessing(true);
      
      const response = await paystackService.initiateWithdrawal({
        amount: amount,
        bankCode: formData.bankCode,
        accountNumber: formData.accountNumber,
        accountName: formData.accountName,
        reason: formData.reason
      });

      if (response.success || response.status) {
        toast.success("Withdrawal initiated successfully!");
        
        // Save withdrawal record locally
        const transactions = JSON.parse(localStorage.getItem('user_transactions') || '[]');
        transactions.push({
          id: response.data.reference,
          type: 'withdrawal',
          amount: amount,
          date: new Date().toISOString().split('T')[0],
          status: response.data.status || 'processing',
          reference: response.data.reference,
          paystackReference: response.data.paystackReference
        });
        localStorage.setItem('user_transactions', JSON.stringify(transactions));

        // Reset form
        setFormData({
          amount: "",
          bankCode: formData.bankCode, // Keep bank details for convenience
          accountNumber: formData.accountNumber,
          accountName: formData.accountName,
          reason: "",
        });

        if (onSubmit) {
          onSubmit(response.data);
        }
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      console.error('Withdrawal error:', error);
      toast.error('Withdrawal failed. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="bg-brand-blue text-brand-yellow p-4 rounded-sm mb-4 border border-brand-yellow/30">
        <div className="flex items-center gap-2">
          <CircleDollarSign className="h-5 w-5" />
          <p className="font-medium">Available Balance: ₦{balance.toLocaleString()}</p>
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="amount" className="flex items-center gap-1">
          <CircleDollarSign className="h-4 w-4 text-muted-foreground" />
          Withdrawal Amount (₦)
        </Label>
        <Input
          id="amount"
          name="amount"
          type="number"
          value={formData.amount}
          onChange={(e) => handleChange('amount', e.target.value)}
          placeholder="Enter amount to withdraw"
          className="border-0 rounded-none focus:border-brand-yellow focus:ring-brand-yellow/30"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="bankCode" className="flex items-center gap-1">
          <Building2 className="h-4 w-4 text-muted-foreground" />
          Select Bank
        </Label>
        <Select 
          value={formData.bankCode} 
          onValueChange={(value) => handleChange('bankCode', value)}
          disabled={loadingBanks}
        >
          <SelectTrigger className="border-0 rounded-none focus:border-brand-yellow focus:ring-brand-yellow/30">
            <SelectValue placeholder={loadingBanks ? "Loading banks..." : "Select your bank"} />
          </SelectTrigger>
          <SelectContent>
            {banks.map((bank) => (
              <SelectItem key={bank.code} value={bank.code}>
                {bank.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="accountNumber" className="flex items-center gap-1">
          <CreditCard className="h-4 w-4 text-muted-foreground" />
          Account Number
        </Label>
        <div className="flex gap-2">
          <Input
            id="accountNumber"
            name="accountNumber"
            value={formData.accountNumber}
            onChange={(e) => handleChange('accountNumber', e.target.value)}
            placeholder="Enter your account number"
            className="border-0 rounded-none focus:border-brand-yellow focus:ring-brand-yellow/30"
            required
          />
          <Button
            type="button"
            onClick={verifyAccount}
            disabled={!formData.accountNumber || !formData.bankCode || verifyingAccount}
            variant="outline"
            size="sm"
            className="border-0 rounded-none"
          >
            {verifyingAccount ? "Verifying..." : "Verify"}
          </Button>
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="accountName" className="flex items-center gap-1">
          <User className="h-4 w-4 text-muted-foreground" />
          Account Name
          {accountVerified && <CheckCircle className="h-4 w-4 text-green-500" />}
          {!accountVerified && formData.accountName && <AlertCircle className="h-4 w-4 text-yellow-500" />}
        </Label>
        <Input
          id="accountName"
          name="accountName"
          value={formData.accountName}
          onChange={(e) => handleChange('accountName', e.target.value)}
          placeholder="Account holder name (auto-filled after verification)"
          className="border-0 rounded-none focus:border-brand-yellow focus:ring-brand-yellow/30"
          readOnly={accountVerified}
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="reason">Reason for Withdrawal (Optional)</Label>
        <Textarea
          id="reason"
          name="reason"
          value={formData.reason}
          onChange={(e) => handleChange('reason', e.target.value)}
          placeholder="Why are you making this withdrawal?"
          className="border-0 rounded-none focus:border-brand-yellow focus:ring-brand-yellow/30"
          rows={3}
        />
      </div>
      
      <Button 
        type="submit" 
        disabled={isLoading || processing || !accountVerified} 
        variant="default"
        className="w-full bg-gradient-to-r from-black to-green-600 text-white border-0 rounded-none"
      >
        {processing ? "Processing Withdrawal..." : "Withdraw Funds"}
      </Button>
    </form>
  );
}