@echo off
REM ===========================================
REM BETTER INTEREST - QUICK DEPLOYMENT SCRIPT
REM ===========================================

echo.
echo ========================================
echo  BETTER INTEREST - DEPLOYMENT SCRIPT
echo ========================================
echo.

REM Check if argument is provided
if "%1"=="" (
    echo Usage: deploy.bat [preview^|production]
    echo.
    echo Commands:
    echo   preview     Deploy preview ^(default^)
    echo   production  Deploy to production ^(demo.kojaonline.store^)
    echo.
    echo Examples:
    echo   deploy.bat preview
    echo   deploy.bat production
    echo.
    goto :end
)

REM Set deployment type
set DEPLOY_TYPE=%1

echo [INFO] Starting deployment...
echo [INFO] Deploy type: %DEPLOY_TYPE%
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed. Please install Node.js 16 or higher.
    goto :error
)

REM Check if npm is installed
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed. Please install npm.
    goto :error
)

REM Check if Netlify CLI is installed
netlify --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Netlify CLI is not installed. Installing...
    npm install -g netlify-cli
    if errorlevel 1 (
        echo [ERROR] Failed to install Netlify CLI.
        goto :error
    )
)

echo [INFO] Installing dependencies...
npm ci --silent
if errorlevel 1 (
    echo [ERROR] Failed to install dependencies.
    goto :error
)

echo [INFO] Running type check...
npx tsc --noEmit
if errorlevel 1 (
    echo [WARNING] TypeScript type checking failed.
)

echo [INFO] Running linter...
npm run lint
if errorlevel 1 (
    echo [WARNING] Linting failed.
)

echo [INFO] Building application...
npm run build
if errorlevel 1 (
    echo [ERROR] Build failed.
    goto :error
)

REM Check if dist directory exists
if not exist "dist" (
    echo [ERROR] Build failed - dist directory not found.
    goto :error
)

if not exist "dist\index.html" (
    echo [ERROR] Build failed - index.html not found.
    goto :error
)

echo [INFO] Build completed successfully.

REM Deploy based on type
if "%DEPLOY_TYPE%"=="production" (
    echo [INFO] Deploying to production...
    netlify deploy --prod --dir=dist --site=better-interest-demo
    if errorlevel 1 (
        echo [ERROR] Production deployment failed.
        goto :error
    )
    echo.
    echo [SUCCESS] Production deployment completed!
    echo.
    echo 🌐 Production URL: https://demo.kojaonline.store
    echo 📊 Netlify Dashboard: https://app.netlify.com/sites/better-interest-demo
    echo 🔧 API URL: https://api.kojaonline.store
    echo.
) else (
    echo [INFO] Deploying preview...
    netlify deploy --dir=dist --site=better-interest-demo
    if errorlevel 1 (
        echo [ERROR] Preview deployment failed.
        goto :error
    )
    echo [SUCCESS] Preview deployment completed!
)

echo.
echo [SUCCESS] 🎉 Deployment completed successfully!
goto :end

:error
echo.
echo [ERROR] ❌ Deployment failed!
exit /b 1

:end
echo.
pause
