import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { AuthService } from '@/services/auth';
import { apiService } from '@/services/api';

// Mock the API service
vi.mock('@/services/api', () => ({
  apiService: {
    post: vi.fn(),
    get: vi.fn(),
  }
}));

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('AuthService', () => {
  let authService: AuthService;

  beforeEach(() => {
    authService = new AuthService();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('login', () => {
    it('should login successfully with valid credentials', async () => {
      const mockResponse = {
        success: true,
        data: {
          user: {
            id: '1',
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe'
          },
          token: 'mock-jwt-token'
        }
      };

      vi.mocked(apiService.post).mockResolvedValue(mockResponse);

      const credentials = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const result = await authService.login(credentials);

      expect(apiService.post).toHaveBeenCalledWith('/auth/login', credentials);
      expect(result).toEqual(mockResponse);
      expect(localStorageMock.setItem).toHaveBeenCalledWith('token', 'mock-jwt-token');
    });

    it('should handle login failure', async () => {
      const mockError = new Error('Invalid credentials');
      vi.mocked(apiService.post).mockRejectedValue(mockError);

      const credentials = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      await expect(authService.login(credentials)).rejects.toThrow('Invalid credentials');
      expect(localStorageMock.setItem).not.toHaveBeenCalled();
    });

    it('should use demo mode when enabled', async () => {
      // Mock environment variable
      vi.stubEnv('VITE_DEMO_MODE', 'true');
      
      const credentials = {
        email: '<EMAIL>',
        password: 'password'
      };

      const result = await authService.login(credentials);

      expect(result.success).toBe(true);
      expect(result.data.user.email).toBe('<EMAIL>');
      expect(apiService.post).not.toHaveBeenCalled();
    });
  });

  describe('register', () => {
    it('should register successfully with valid data', async () => {
      const mockResponse = {
        success: true,
        data: {
          user: {
            id: '1',
            email: '<EMAIL>',
            firstName: 'Jane',
            lastName: 'Doe'
          },
          token: 'mock-jwt-token'
        }
      };

      vi.mocked(apiService.post).mockResolvedValue(mockResponse);

      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Jane',
        lastName: 'Doe',
        phone: '+2348012345678'
      };

      const result = await authService.register(userData);

      expect(apiService.post).toHaveBeenCalledWith('/auth/register', userData);
      expect(result).toEqual(mockResponse);
      expect(localStorageMock.setItem).toHaveBeenCalledWith('token', 'mock-jwt-token');
    });

    it('should handle registration validation errors', async () => {
      const mockError = new Error('Email already exists');
      vi.mocked(apiService.post).mockRejectedValue(mockError);

      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Jane',
        lastName: 'Doe',
        phone: '+2348012345678'
      };

      await expect(authService.register(userData)).rejects.toThrow('Email already exists');
    });
  });

  describe('logout', () => {
    it('should logout successfully', async () => {
      vi.mocked(apiService.post).mockResolvedValue({ success: true });
      localStorageMock.getItem.mockReturnValue('mock-token');

      await authService.logout();

      expect(apiService.post).toHaveBeenCalledWith('/auth/logout');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('token');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('user');
    });

    it('should handle logout errors gracefully', async () => {
      vi.mocked(apiService.post).mockRejectedValue(new Error('Network error'));
      
      // Should not throw even if API call fails
      await expect(authService.logout()).resolves.not.toThrow();
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('token');
    });
  });

  describe('getCurrentUser', () => {
    it('should return user from localStorage', () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe'
      };

      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockUser));

      const user = authService.getCurrentUser();

      expect(user).toEqual(mockUser);
      expect(localStorageMock.getItem).toHaveBeenCalledWith('user');
    });

    it('should return null when no user in localStorage', () => {
      localStorageMock.getItem.mockReturnValue(null);

      const user = authService.getCurrentUser();

      expect(user).toBeNull();
    });

    it('should handle invalid JSON in localStorage', () => {
      localStorageMock.getItem.mockReturnValue('invalid-json');

      const user = authService.getCurrentUser();

      expect(user).toBeNull();
    });
  });

  describe('isAuthenticated', () => {
    it('should return true when token exists', () => {
      localStorageMock.getItem.mockReturnValue('mock-token');

      const isAuth = authService.isAuthenticated();

      expect(isAuth).toBe(true);
    });

    it('should return false when no token', () => {
      localStorageMock.getItem.mockReturnValue(null);

      const isAuth = authService.isAuthenticated();

      expect(isAuth).toBe(false);
    });
  });

  describe('changePassword', () => {
    it('should change password successfully', async () => {
      vi.mocked(apiService.post).mockResolvedValue({ success: true });

      await authService.changePassword('oldPassword', 'newPassword');

      expect(apiService.post).toHaveBeenCalledWith('/auth/change-password', {
        currentPassword: 'oldPassword',
        newPassword: 'newPassword'
      });
    });

    it('should validate password length in demo mode', async () => {
      vi.stubEnv('VITE_DEMO_MODE', 'true');

      await expect(authService.changePassword('old', 'new')).rejects.toThrow(
        'Password must be at least 6 characters'
      );
    });
  });

  describe('requestPasswordReset', () => {
    it('should request password reset successfully', async () => {
      vi.mocked(apiService.post).mockResolvedValue({ success: true });

      await authService.requestPasswordReset('<EMAIL>');

      expect(apiService.post).toHaveBeenCalledWith('/auth/forgot-password', {
        email: '<EMAIL>'
      });
    });
  });

  describe('resetPassword', () => {
    it('should reset password successfully', async () => {
      vi.mocked(apiService.post).mockResolvedValue({ success: true });

      await authService.resetPassword('reset-token', 'newPassword');

      expect(apiService.post).toHaveBeenCalledWith('/auth/reset-password', {
        token: 'reset-token',
        newPassword: 'newPassword'
      });
    });
  });
});
