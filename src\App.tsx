
import { Toaster as Sonner } from "@/components/ui/sonner";
import { Toaster } from "@/components/ui/toaster";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { <PERSON>rowserRouter } from "react-router-dom";
import { AppContent } from "./components/AppContent";
import ErrorBoundary from "./components/ErrorBoundary";
import { ThemeProvider } from "./hooks/use-theme";

// Set document title
document.title = "Better Interest | Secure Digital Savings";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 60 * 60 * 1000, // 1 hour
      retry: (failureCount, error: any) => {
        if (!navigator.onLine || error?.status === 404) {
          return false;
        }
        return failureCount < 3;
      }
    }
  }
});

function App() {
  console.log('App component rendering');
  console.log('Bill Payment System: Ready for Airtime, Data, Electricity, Flight, and more');

  return (
    <ErrorBoundary>
      <BrowserRouter>
        <QueryClientProvider client={queryClient}>
          <ThemeProvider>
            <div className="min-h-screen bg-background">
              <Toaster />
              <Sonner />
              <AppContent />
            </div>
          </ThemeProvider>
        </QueryClientProvider>
      </BrowserRouter>
    </ErrorBoundary>
  );
}

export default App;
