const mongoose = require('mongoose');

const staffAssignmentSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  roleId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'StaffRole',
    required: true
  },
  assignedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  module: {
    type: String,
    required: true,
    enum: ['bill_payment', 'user_management', 'savings_plans', 'withdrawals', 'kyc', 'notifications', 'analytics', 'all']
  },
  customPermissions: [{
    action: {
      type: String,
      required: true,
      enum: ['view', 'create', 'edit', 'delete', 'approve', 'reject', 'export', 'import', 'assign']
    },
    resource: {
      type: String,
      required: true,
      enum: ['users', 'staff', 'roles', 'bills', 'providers', 'payments', 'withdrawals', 'savings', 'kyc', 'notifications', 'reports']
    },
    granted: {
      type: Boolean,
      default: true
    }
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  startDate: {
    type: Date,
    default: Date.now
  },
  endDate: {
    type: Date
  },
  notes: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

// Compound index to ensure one active assignment per user-module pair
staffAssignmentSchema.index({ userId: 1, module: 1, isActive: 1 });
staffAssignmentSchema.index({ roleId: 1 });
staffAssignmentSchema.index({ assignedBy: 1 });

module.exports = mongoose.model('StaffAssignment', staffAssignmentSchema);