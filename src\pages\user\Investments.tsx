import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { InvestmentOptions } from '@/components/investments/InvestmentOptions';
import { SocialShare } from '@/components/ui/social-share';
import { Button } from '@/components/ui/button';
import { Share2 } from 'lucide-react';

const Investments = () => {
  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Investments</h1>
          <p className="text-muted-foreground mt-2">
            Grow your wealth with our investment products
          </p>
        </div>
        <SocialShare
          title="Check out these amazing investment opportunities!"
          description="Grow your wealth with mutual funds, stocks, and bonds"
          hashtags={['investment', 'wealth', 'finance', 'Nigeria']}
        >
          <Button variant="outline">
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
        </SocialShare>
      </div>

      <Tabs defaultValue="products" className="space-y-6">
        <TabsList>
          <TabsTrigger value="products">Investment Products</TabsTrigger>
          <TabsTrigger value="portfolio">My Portfolio</TabsTrigger>
        </TabsList>

        <TabsContent value="products">
          <InvestmentOptions />
        </TabsContent>

        <TabsContent value="portfolio">
          <InvestmentOptions />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Investments;