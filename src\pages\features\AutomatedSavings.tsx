import Header from '@/components/landing/Header';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { <PERSON>gyBank, ArrowRight, CheckCircle, Zap, Clock, Target } from 'lucide-react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';

const AutomatedSavings = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: Zap,
      title: 'Set It & Forget It',
      description: 'Automatically save a percentage of your income without thinking about it'
    },
    {
      icon: Clock,
      title: 'Flexible Scheduling',
      description: 'Choose daily, weekly, or monthly automatic transfers that work for you'
    },
    {
      icon: Target,
      title: 'Goal-Based Saving',
      description: 'Link your automated savings to specific financial goals'
    }
  ];

  return (
    <>
      <Helmet>
        <title>Automated Savings | Better Interest</title>
        <meta name="description" content="Set it and forget it with Better Interest's automated savings feature. Save effortlessly and reach your financial goals faster." />
      </Helmet>

      <div className="min-h-screen landing-bg text-white">
        <Header />
        
        {/* Hero Section */}
        <section className="relative pt-20 pb-16 overflow-hidden">
          <div className="container mx-auto px-4 relative z-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[70vh]">
              {/* Left Column - Content */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="space-y-8"
              >
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-12 h-12 bg-[#39E59E] rounded-xl flex items-center justify-center">
                    <PiggyBank className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-[#39E59E] font-semibold">Automated Savings</span>
                </div>
                
                <h1 className="text-5xl md:text-6xl font-bold leading-tight">
                  <span className="text-white">Save Money</span>
                  <br />
                  <span className="text-[#39E59E]">Automatically</span>
                </h1>
                
                <p className="text-xl text-gray-300 leading-relaxed max-w-lg">
                  Build wealth effortlessly with our intelligent automated savings system. 
                  Set your preferences once and watch your savings grow without any manual effort.
                </p>

                <div className="flex flex-col sm:flex-row gap-4">
                  <button 
                    className="btn-neumorphic-inward text-lg px-8 py-6 group bg-[#39E59E] hover:bg-[#2dd489]"
                    onClick={() => navigate('/signup')}
                  >
                    Start Auto-Saving
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                  </button>
                  
                  <Button 
                    variant="outline" 
                    size="lg" 
                    className="text-lg px-8 py-6 rounded-xl border-[#39E59E] text-[#39E59E] hover:bg-[#39E59E]/10"
                    onClick={() => navigate('/demo')}
                  >
                    See How It Works
                  </Button>
                </div>
              </motion.div>

              {/* Right Column - Image */}
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="flex justify-center"
              >
                <div className="relative">
                  <img
                    src="/lovable-uploads/371e0f57-a280-42c0-8e1b-50aee7ebddc6.png"
                    alt="Automated Savings"
                    className="w-full max-w-md h-auto object-contain"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-[#39E59E]/20 to-transparent rounded-lg"></div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-24 relative">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
                Why Choose Automated Savings?
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Take the guesswork out of saving and build consistent wealth-building habits.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="w-16 h-16 bg-[#39E59E]/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <feature.icon className="w-8 h-8 text-[#39E59E]" />
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-white">{feature.title}</h3>
                  <p className="text-gray-300 leading-relaxed">{feature.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Additional Section */}
        <section className="py-24 bg-black/20">
          <div className="container mx-auto px-4">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                className="space-y-6"
              >
                <h2 className="text-4xl font-bold text-white">
                  Smart Savings Made Simple
                </h2>
                <p className="text-xl text-gray-300">
                  Our automated savings feature uses intelligent algorithms to help you save more 
                  without impacting your daily spending habits.
                </p>
                <ul className="space-y-4">
                  {[
                    'Round-up spare change from purchases',
                    'Percentage-based income allocation',
                    'Smart timing to avoid overdrafts',
                    'Instant notifications and controls'
                  ].map((item, index) => (
                    <li key={index} className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-[#39E59E] flex-shrink-0" />
                      <span className="text-gray-300">{item}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                className="flex justify-center"
              >
                <img
                  src="/lovable-uploads/371e0f57-a280-42c0-8e1b-50aee7ebddc6.png"
                  alt="Smart Savings"
                  className="w-full max-w-md h-auto object-contain"
                />
              </motion.div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default AutomatedSavings;
