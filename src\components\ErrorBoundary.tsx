
import { Component, ErrorInfo, ReactNode } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Wifi, Server, Bug } from "lucide-react";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorType: 'network' | 'auth' | 'react' | 'server' | 'unknown';
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
    errorType: 'unknown',
  };

  public static getDerivedStateFromError(error: Error): State {
    const errorType = ErrorBoundary.categorizeError(error);
    return { hasError: true, error, errorInfo: null, errorType };
  }

  private static categorizeError(error: Error): State['errorType'] {
    const message = error.message?.toLowerCase() || '';
    
    if (message.includes('usestate') || message.includes('dispatcher') || message.includes('react')) {
      return 'react';
    }
    if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
      return 'network';
    }
    if (message.includes('unauthorized') || message.includes('auth') || message.includes('token')) {
      return 'auth';
    }
    if (message.includes('server') || message.includes('500') || message.includes('503')) {
      return 'server';
    }
    
    return 'unknown';
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.group('🚨 Error Boundary Caught Error');
    console.error('Error:', error);
    console.error('Error Info:', errorInfo);
    console.error('Error Type:', this.state.errorType);
    console.error('Component Stack:', errorInfo.componentStack);
    console.groupEnd();
    
    this.setState({
      error,
      errorInfo,
    });

    // Special handling for different error types
    if (this.state.errorType === 'react') {
      console.error('React bundling issue detected. This might be resolved by refreshing the page.');
    }
  }

  private getErrorIcon() {
    switch (this.state.errorType) {
      case 'network': return <Wifi className="h-6 w-6 text-red-600" />;
      case 'server': return <Server className="h-6 w-6 text-red-600" />;
      case 'react': return <Bug className="h-6 w-6 text-red-600" />;
      default: return <AlertTriangle className="h-6 w-6 text-red-600" />;
    }
  }

  private getErrorMessage() {
    switch (this.state.errorType) {
      case 'network':
        return 'Network connection issue. Please check your internet connection and try again.';
      case 'auth':
        return 'Authentication error. Please sign in again.';
      case 'react':
        return 'A React application error occurred. Please try refreshing the page.';
      case 'server':
        return 'Server error. Our team has been notified and is working on a fix.';
      default:
        return 'An unexpected error occurred. Please try refreshing the page.';
    }
  }

  private handleReload = () => {
    if (this.state.errorType === 'auth') {
      // Clear auth data and redirect to login
      localStorage.clear();
      window.location.href = '/login';
    } else {
      window.location.reload();
    }
  };

  private handleGoHome = () => {
    window.location.href = "/";
  };

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen flex items-center justify-center p-4 bg-background">
          <Card className="w-full max-w-md border-destructive/50">
            <CardHeader className="space-y-1">
              <div className="flex items-center justify-center mb-4">
                <div className="h-12 w-12 bg-destructive/10 rounded-full flex items-center justify-center">
                  {this.getErrorIcon()}
                </div>
              </div>
              <CardTitle className="text-2xl text-center text-destructive">Error Detected</CardTitle>
              <CardDescription className="text-center">
                {this.getErrorMessage()}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <div className="bg-muted p-3 rounded-md text-sm overflow-auto max-h-32">
                  <p className="font-medium">Error: {this.state.error?.toString()}</p>
                  {this.state.errorInfo && (
                    <details className="mt-2">
                      <summary className="cursor-pointer text-xs text-muted-foreground">
                        View technical details
                      </summary>
                      <pre className="mt-2 text-xs whitespace-pre-wrap">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </details>
                  )}
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={this.handleGoHome}>
                Go Home
              </Button>
              <Button onClick={this.handleReload}>
                <RefreshCcw className="h-4 w-4 mr-2" />
                {this.state.errorType === 'auth' ? 'Sign In Again' : 'Try Again'}
              </Button>
            </CardFooter>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
