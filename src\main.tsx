
import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';

// Debug environment variables
console.log('Environment Debug:', {
  env: import.meta.env,
  api_url: import.meta.env.VITE_API_URL,
  paystack_key: import.meta.env.VITE_PAYSTACK_PUBLIC_KEY,
  backend_url: import.meta.env.VITE_BACKEND_URL
});

// Initialize bill payment system
console.log('Bill Payment System: Initialized with 5 Nigerian payment processors');

// Get the root element
const rootElement = document.getElementById("root");

// Check if the root element exists
if (!rootElement) {
  throw new Error("Root element not found! Make sure there's a div with id='root' in your HTML.");
}

const root = createRoot(rootElement);

// Render the app
root.render(<App />);
