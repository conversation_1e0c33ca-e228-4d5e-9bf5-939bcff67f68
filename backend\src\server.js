require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { startCronJobs } = require('./services/cronJobs');
const { interestCalculationService } = require('./services/InterestCalculationService');
const helmet = require('helmet');

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const savingsRoutes = require('./routes/savings');
const paymentsRoutes = require('./routes/payments');
const adminRoutes = require('./routes/admin');
const groupSavingsRoutes = require('./routes/groupSavings');
const notificationsRoutes = require('./routes/notifications');
const referralsRoutes = require('./routes/referrals');
const supportRoutes = require('./routes/support');
const billsRoutes = require('./routes/bills');
const nibssRoutes = require('./routes/nibss');
const uploadRoutes = require('./routes/upload');
const session = require('express-session');
const passport = require('passport');

// Import middleware
const { connectDB } = require('./config/database');
const { errorHandler } = require('./middleware/errorHandler');
const { rateLimiter } = require('./middleware/rateLimiter');

const app = express();
const PORT = process.env.PORT || 3000;

// Connect to MongoDB
connectDB();

// Security middleware
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
    },
  },
}));

// CORS configuration
app.use(cors({
  origin: [
    process.env.CORS_ORIGIN || 'http://localhost:8080',
    process.env.FRONTEND_URL || 'http://localhost:8080',
    'https://preview--betterinterest.lovable.app',
    'https://preview--betterinterest81.lovable.app',
    'https://5949cc55-7687-46ed-88ca-9a3debf001d2.lovableproject.com'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept']
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Session middleware for OAuth
app.use(session({
  secret: process.env.SESSION_SECRET || 'your-session-secret-change-this-in-production',
  resave: false,
  saveUninitialized: false,
  cookie: { secure: process.env.NODE_ENV === 'production' }
}));

// Passport middleware
app.use(passport.initialize());
app.use(passport.session());

// Rate limiting
app.use(rateLimiter);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    version: process.env.API_VERSION || 'v1'
  });
});

// API version prefix
const apiVersion = process.env.API_VERSION || 'v1';
const apiPrefix = `/api/${apiVersion}`;

// Routes with API versioning (v1)
app.use(`${apiPrefix}/auth`, authRoutes);
app.use(`${apiPrefix}/users`, userRoutes);
app.use(`${apiPrefix}/savings`, savingsRoutes);
app.use(`${apiPrefix}/payments`, paymentsRoutes);
app.use(`${apiPrefix}/admin`, adminRoutes);
app.use(`${apiPrefix}/group-savings`, groupSavingsRoutes);
app.use(`${apiPrefix}/notifications`, notificationsRoutes);
app.use(`${apiPrefix}/referrals`, referralsRoutes);
app.use(`${apiPrefix}/support`, supportRoutes);
app.use(`${apiPrefix}/bills`, billsRoutes);
app.use(`${apiPrefix}/nibss`, nibssRoutes);
app.use(`${apiPrefix}/upload`, uploadRoutes);
app.use(`${apiPrefix}/investments`, investmentRoutes);
app.use(`${apiPrefix}/roundup`, roundupRoutes);
app.use(`${apiPrefix}/social`, socialRoutes);
app.use(`${apiPrefix}/staff-management`, staffManagementRoutes);

// Add new routes
const investmentRoutes = require('./routes/investments');
const roundupRoutes = require('./routes/roundup');
const socialRoutes = require('./routes/social');
const fixedDepositRoutes = require('./routes/fixed-deposits');
const cardRoutes = require('./routes/cards');
const analyticsRoutes = require('./routes/analytics');

app.use(`${apiPrefix}/investments`, investmentRoutes);
app.use(`${apiPrefix}/roundup`, roundupRoutes);
app.use(`${apiPrefix}/social`, socialRoutes);
app.use(`${apiPrefix}/fixed-deposits`, fixedDepositRoutes);
app.use(`${apiPrefix}/cards`, cardRoutes);
app.use(`${apiPrefix}/analytics`, analyticsRoutes);

// Legacy routes (for backward compatibility)
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/savings', savingsRoutes);
app.use('/api/payments', paymentsRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/group-savings', groupSavingsRoutes);
app.use('/api/notifications', notificationsRoutes);
app.use('/api/referrals', referralsRoutes);
app.use('/api/support', supportRoutes);
app.use('/api/bills', billsRoutes);
app.use('/api/nibss', nibssRoutes);
app.use('/api/upload', uploadRoutes);

// Legacy routes for new features
app.use('/api/investments', investmentRoutes);
app.use('/api/roundup', roundupRoutes);
app.use('/api/social', socialRoutes);

// Staff management routes
const staffManagementRoutes = require('./routes/staff-management');
app.use('/api/staff-management', staffManagementRoutes);

// Error handling middleware
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ 
    success: false,
    message: 'Route not found',
    path: req.originalUrl,
    timestamp: new Date().toISOString()
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received. Shutting down gracefully...');
  process.exit(0);
});

app.listen(PORT, () => {
  console.log(`🚀 Better Interest API Server running on port ${PORT} in ${process.env.NODE_ENV} mode`);
  console.log(`📊 Health check available at: http://localhost:${PORT}/health`);
  console.log(`🔗 API base URL: http://localhost:${PORT}/api`);
  console.log(`🌐 CORS Origins: ${JSON.stringify([
    process.env.CORS_ORIGIN || 'http://localhost:8080',
    process.env.FRONTEND_URL || 'http://localhost:8080',
    'https://preview--betterinterest.lovable.app',
    'https://preview--betterinterest81.lovable.app',
    'https://5949cc55-7687-46ed-88ca-9a3debf001d2.lovableproject.com'
  ])}`);
  console.log(`💳 Paystack: ${process.env.PAYSTACK_PUBLIC_KEY ? 'Configured' : 'Missing'}`);
  console.log(`🗄️ MongoDB: ${process.env.MONGO_URI ? 'Connected' : 'Missing'}`);

  // Initialize Interest Calculation Service
  interestCalculationService.initialize();
  console.log('🏦 Interest Calculation Service initialized');
});
