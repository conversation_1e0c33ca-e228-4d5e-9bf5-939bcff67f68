<?xml version="1.0" encoding="UTF-8"?>
<svg version="1.1" viewBox="0 0 1600 1600" width="600" height="600" xmlns="http://www.w3.org/2000/svg">
<path transform="translate(196,91)" d="m0 0 32 4 22 5 25 7 21 8 20 9 21 11 22 14 16 12 11 9 13 12 16 16 11 14 11 15 14 24 8 16 9 20 9 27 5 21 4 23 2 26 1 35 27-14 21-10 27-11 36-12 30-8 30-6 30-4 24-2 21-1h30l42 3 36 5 34 7 30 8 35 12 29 12 29 14 22 12 24 15 20 14 13 10 10 8 13 11 10 9 8 7 27 27 7 8 11 13 13 16 13 18 12 17 10 16 4 6v2l11-7 15-11 21-16 16-13 14-12 8-7 10-9 8-7 15-14 12-12 1-2h2l2-4 13-14 9-11 11-13 14-19 14-21 14-24 8-16 12-28 5-15h3l14 29 8 19 8 22 8 28 7 35 3 21 3 38v38l-3 38-5 34-7 30-7 25-9 25-11 25-15 29-13 21-12 17-13 17-12 14-11 12-24 24-11 9-9 8-16 12-20 14-13 8-3 37-5 38-6 31-7 28-11 35-11 28-13 29-15 29-12 20-14 22-13 18-11 14-9 11-11 13-11 12-14 15-10 10-8 7-11 10-11 9-16 13-19 13-11 8-19 12-23 13-27 14-25 11-27 10-28 9-28 7-36 7-31 4-43 3h-21l-45-3-37-5-35-7-31-8-41-14-28-12-25-12-23-13-20-12-16-11-18-13-16-13-11-9-11-10-8-7-36-36-9-11-8-9-13-17-14-19-13-20-9-15-10-18-11-21-14-32-10-28-8-25-8-32-5-26-4-31-2-26-1-25-2-235v-172l1-154 1-274zm534 553-24 3-23 5-19 6-20 8-23 12-20 13-16 12-13 11-23 23-13 17-12 18-10 18-8 17-11 33-6 27-3 25v13l5-3 16-13 20-14 20-13 24-14 27-14 25-12 37-15 34-12 41-13 55-15 65-16 73-16 14-3-1-4h-2l-2-4-13-13-11-9-17-13-21-13-15-8-24-10-19-6-23-5-25-3zm-79 358-29 1-40 4-33 6-22 6-15 5 1 5 10 18 10 15 10 13 10 11 7 8 16 15 17 13 15 10 16 9 16 8 25 10 24 7 22 4 19 2h41l28-4 22-5 24-8 18-8 23-12 19-13 11-9 13-12 13-13 7-8 13-17 11-17 10-18 9-20h-10l-27 2-21 1h-51l-46-2-110-6-37-1z" fill="#82E05F"/>
<path transform="translate(196,91)" d="m0 0 32 4 22 5 25 7 21 8 20 9 21 11 22 14 16 12 11 9 13 12 16 16 11 14 11 15 14 24 8 16 9 20 9 27 5 21 4 23 2 26 1 35 27-14 21-10 27-11 36-12 30-8 30-6 30-4 24-2 21-1h30l42 3 36 5 34 7 30 8 35 12 29 12 29 14 22 12 24 15 20 14 13 10 10 8 6 5-1 2-11-9-16-12-20-14-23-14-18-10-17-9-29-13-23-9-23-8 1 1v2l4 1v6 3l-3 1-2-1-1 3-5 2-4 4-3-1-5 6-3 6-4 6h-2l-2 4-6 7-2 5-2 6-5 9-2 3-2 6h-2v6h-2l-1 7-4 9-1 3h-2v5h-2l1 10-5 6-3 8-3 1v2l-4 3-1 9-3 5v7l-5 6h-3l-1 9-1 5-5 10-5 3-5 10-6 8-4 6-3 9-5 4v6l-2 3h-2l-1 8 3 3 3 1 1 5h3v-3h3l2 5 13 2 5 4-2 6 17 5 15 6v1l-8-1-24-8-23-5-25-3h-39l-24 3-23 5-19 6-20 8-23 12-20 13-16 12-13 11-22 22-13 17-12 18-10 18-8 17-11 33-6 27-5 39-12 12-8 7-14 15-1 2h-2l-2 4-14 19-12 18-13 23-12 27-10 30-6 26-3 18-2 13 9-11 25-25 14-11 13-10 21-13 12-7 26-12 15-6 2-1 13 23 10 15 10 13 10 11 7 8 15 14 17 13 15 10 16 9 16 8 25 10 24 7 22 4 19 2h41l16-2h12l-3 2-33 4h-29l-7-1v4h2v7h-2l-2 7-5 5-10 13-2 3-5 3-3 4h-2l-2 4-5 7-5 5-5 7h-2l-2 7-10 9-3 5-4 4-2 4-6 5h-2l-7 7-6 7-4 6-3 5-7 6-6 10-9 8h-2v2l-3 3h-2v3h-3l-1 5-3 3-2 3-5 3-12 12-7 8-5 3-6 9-6 7-4 6-6 3-6 8-3 3-5 16v3l-4 2 27 11 25 9 30 9 12 3v1h-7l-26-7-38-13-28-12-25-12-23-13-20-12-16-11-18-13-16-13-11-9-11-10-8-7-36-36-9-11-8-9-13-17-14-19-13-20-9-15-10-18-11-21-14-32-10-28-8-25-8-32-5-26-4-31-2-26-1-25-2-235v-172l1-154 1-274z" fill="#008155"/>
<path transform="translate(1453,345)" d="m0 0 3 3 12 25 9 21 8 22 8 28 7 35 3 21 3 38v38l-3 38-5 34-7 30-7 25-9 25-11 25-15 29-13 21-12 17-13 17-12 14-11 12-24 24-11 9-9 8-16 12-20 14-13 8-3 37-5 38-6 31-7 28-11 35-11 28-13 29-15 29-12 20-14 22-13 18-11 14-9 11-11 13-11 12-14 15-10 10-8 7-11 10-11 9-16 13-19 13-11 8-19 12-23 13-27 14-25 11-27 10-28 9-28 7-24 5h-5l-3-2-16-2-4-1-13-1-4-4-3-2 1-12 3-5h2l2-6 4-7 5-5 5-6 6-5 3-5 4-5 4-6 8-9 6-8 6-15 4-4 2-9 5-15 3-3h2l1-5 4-7 5-7 4-7 4-5h2l1-4 4-8 5-8 8-11 3-5 4-4 3-4h2l1-6 6-7 6-11 7-10 8-15 7-12 4-7 5-9h-2l3-10 8-16 4-7 1-6 6-11 2-3 3-10 7-18 2-7 4-9 2-10 5-15 3-5 4-13 3-13 7-13 4-5 4-7 2-5 4-6 1-7 6-8 6-11 3-3 4-7 7-5 3-8 6-14 5-2v-3h2l2-7 2-3h2v-2l2-1 4-9 3-3 2-5h2l2-10 3-6 7-5 2-5h2l1-6 6-9h2l2-5 4-5 7-3 1-3 5-5 7-3 2-5v-6l5-2 2 3 3-1 1 5 5 2v2l5 1-4-11-1-10 3-8 4-5 4-1-2 7h-2l-1 7-1 1 1 9 3-1h3l2-3 8-1 2-4h3l2 3 3-4-2-6 1-3-1-2-5-2-3-1v-2h9l-1-3-7-5-1-2v-7l1-4-2-3v-3l4-1 3-3 7-1v-7l5-5 5-3 1-4-2-3v-3h2l2-4 7-4 2-9 4-2h5l1-13h7l5 1v-7l4 2-6-15-10-24v-2l-3-1-4-7v-2h-2l-1-2-1 4h-2l-1-2-4 1-2 4-1 9-6 5-4 6-2 12h-4l-1 9-1 3-1 6-3 1v2h-5l-1 11h-2l-7-23-11-24-3-6 2-5 1-1 8-1 5-8 3-5 5-11 5-10 2-8-1-12h3l5 5 8 7 4 4 2 4 11-7 15-11 21-16 16-13 14-12 8-7 10-9 8-7 15-14 12-12 1-2h2l2-4 13-14 9-11 11-13 14-19 14-21 14-24 8-16 12-28 5-15z" fill="#96EB5C"/>
<path transform="translate(196,91)" d="m0 0 32 4 22 5 25 7 21 8 20 9 21 11 22 14 16 12 11 9 13 12 16 16 11 14 11 15 14 24 8 16 9 20 9 27 5 21 4 23 2 26v88h-1l-1-14h-3l-2-7v-6l-3 12-4 9-6 4-2 5-2 7-4 6-2 7-3 1-1 6-2 1-2 6-3 5-4 2-1 3-5 1-1 4-5 1-1 7h-2v2h-6l-2 6-7 2-6-1-4 9-4 4h-2l-3 6-5 1-3 1-1 4-4 9-4 2-3 4h-2v5l-4 2h-3v3l-9 5-4 1-3 1-2 4-5 1v4h-8v5h-6v3h-2l-2 5-3 1v3l-5 2-1 5-5 2-4 7-3 2h-3l1 3-5 3-2 2-7 1-6 7-8 6-4 8-2 5-4 3h-3v2l-5 4-5 1v-3h-3v4l2 1-5 1h-4l-5 4-13 2h-6l-6-5-3-2v-3h-2l-3-7-3-19-2-21-2-6-1 70h-1v-172l1-154 1-274z" fill="#1DC67F"/>
<path transform="translate(937,732)" d="m0 0h6l9 10 13 17 12 18 6 11 9 20 8 22 6 24 4 27 1 8v31l-3 28-6 29-10 30-10 2-27 2-21 1h-51l-46-2-110-6-37-1h-39l-29 1-40 4-33 6-22 6-13 4h-2l-1 2h-2l-24 10-19 9-25 15-14 10-16 13-10 9-20 20-6 8-2 1 4-26 4-20 7-25 9-24 11-24 14-24 7-10 11-15 8-11h2l2-4 7-7 7-8 16-15 2-2 1-8h1l1 6 14-11 13-10 22-15 18-11 19-11 23-12 27-13 37-15 34-12 41-13 55-15 65-16 73-16z" fill="#E3C41A"/>
<path transform="translate(1453,347)" d="m0 0 3 1 12 25 9 21 8 22 8 28 7 35 3 21 3 38v38l-3 38-5 34-7 30-7 25-9 25-11 25-15 29-13 21-12 17-13 17-12 14-11 12-24 24-11 9-9 8-16 12-20 14-13 8-3 37-5 38-6 31-7 28-11 35-11 28-13 29-15 29-12 20-14 22-13 18-11 14-9 11-11 13-11 12-14 15-10 10-8 7-11 10-11 9-16 13-19 13-11 8-19 12-23 13-9 5-7-1-4 3h-6v3l-10 2h-7l-5-5-2-3 1-7 7-21 1-2h-2l-1 3h-2l-1 4-2-1 6-10 5-6h2l1-4 8-9 2-1 4-7 7-11 4-6 1-7 4-6 4-11 2-4 3 1 2-7 5-11 3-9h5l1-7 6-7 5-8h2v-2h-2l2-4 5-5 2-6v-3l5-5 2-1v-2l4-4 1-6 2 2 3-5-2-3 6-8 3-2h3l1-6 4-5 7-11 1-4 7-8 6-2v-6l-1-2 3-6h3l2-7 4-5 3-1 5-8 8-16 5-8h4l2-6 6-11 1-3 1-6 3-2 5-1 3-9 10-11 4-8 6-22-2-1 2-6 2-1 3-5 2-1h6l6-10 6-13 2-5 1-14 3-2 3 5 5-2 5-3h2l2-5 3-2-2-6 5-1 2-5 1-5h2l2-4 4-5 4-9 5-6 3 1 1-8 2-3h2l2-6 2-3 7-1 1-9h2v-2l3-3h2v6l3-2 1-8 3 1 3 3 3-6 1-9v-16l1-10v-17l-1-3-2-18-4-22-2-16-1-7 14-12 7-7 8-7 11-11 7-8 9-10 9-11 12-15 13-18 9-13 13-21 5-6 1-4h2l2-5 18-36 7-16 3-7 5-11 4-14 7-21 5-18 3-12 5-33 1-13v-60l-3-27z" fill="#B3F055"/>
<path transform="translate(911,379)" d="m0 0 9 2 28 10 26 11 25 12 20 11 20 12 18 12 18 13 10 8 9 7 5 2 13 12 8 7 27 27 7 8 11 13 13 16 13 18 12 17 10 16 2 5-5-5-10-9v-2l-4-1 1 12-3 10-4 8-7 15-4 5-3 5-8 1-2 3h-2l-13-23-14-21-8-11-7-9-9-11-4-4v-2l-5-2-6-1-1 6-5 4-5 5-4 2-2 4-6 3h-2l-10 8h-6l-6-3 6 7 26 26 7 8 11 13 13 17 12 17 10 15 10 17-1 3-3 1-11-7-11 3-10 8-13 11-1 3h-2l-2 5-5 5-8 11-4 9-6 11-5 12-3 4-4 10-3 5h-2l-2 5-3 6-7 9-4 10-3 4-2 7-3 4-10 3-3-2-4-10-5-10-8-20-5-12-11-25-8-14-13-19-12-15-7-8-1-3-17-17-11-9-17-13-21-13-15-8-14-6 1-2-22-8-11-3 4-1v-2l-3-1h4v-2l-3-1v-2l-15-2-2-3v-3h-3v3h-3l-1-5-4-1-3-3 1-5 1-3h2l1-9 5-4 3-9 4-6 4-5 9-15 4-2 4-9v-5l3-9h3l2-4 2-3v-6l3-5 1-9 3-3h2v-2l3-2 3-8 4-5-1-8 1-2h2v-5h2l2-9 3-7 2-4 1-5 2-1 2-7 3-5 5-9 1-5 3-5h2l2-4 6-7 4-6 4-7 3-3 5 1 2-4 5-2 1-2 5-1-1-3v-5l-3-1v-2l-4-1z" fill="#3FC367"/>
<path transform="translate(481,444)" d="m0 0h1v9l2 4 2-1 1 2 1 14v157l-1 44-3 15v7l-4 5-4 2-8 6-4 8-5 5-6 7-3 6h-2l-1 3-7 6-5 5-4 7-4 4h-2l-2 5-8 6-8 10-9 6-6 7-5 4-5 5-7 6-6 4-5 2-3 3-4 2-5 5-5 2h-4l-2 4-5 1-1 3-15 10-5 4h-2l-1 2-4 1-1 4-8 4h-2l-2 4-9 2-4 3h-2l-1 3-4 4-8 2-5 5-5 4-7 4-7 2v3l-3 1-5-1h-6l-5-2-3-5v-2l-3-1-3-5-5-4h-4l-7 2-1-1-1-119-2-1v53h-1v-178l2 1 2 8 2 19 3 20 2 3v3h2l1 3 5 3 3 3h9l10-2h2l1-3 5-2h4l-1-4 6-2-1 4 6-2v-2h3v-2l6-3 4-9 3-5 8-6 5-6v-1l8-1 4-4 2-1v-2l2-2 4-1 5-8h4l1-5 5-2v-3l3-3 2-1v-2h2v-3l3-1 3 1v-5l3-1 5 1-1-5 7-2 3-4 2-1 4-1 5-3h3v-3l6-2 1-5 7-6h2l3-9 2-4 5-3h3l3-6 4-2 1-2 2-2 2-6 2-1 6 1 6-1 2-6h6l3-9 5-2 1-3 5-1 1-4h2l1-2 4-5 2-6h2l1-6 3-3 2-6 4-7 1-8h2l2-4 4-2 4-9 3-12z" fill="#02A165"/>
<path transform="translate(743,355)" d="m0 0h47l24 2 36 6 20 4 21 5v-2l16 4 35 12 29 12 29 14 22 12 24 15 20 14 13 10 10 8 6 5-1 2-11-9-16-12-20-14-23-14-18-10-17-9-29-13-23-9-23-8 1 1v2l4 1v6 3l-3 1-2-1-1 3-5 2-4 4-3-1-5 6-3 6-4 6h-2l-2 4-6 7-2 5-2 6-5 9-2 3-2 6h-2v6h-2l-1 7-4 9-1 3h-2v5h-2l1 10-5 6-3 8-3 1v2l-4 3-1 9-3 5v7l-5 6h-3l-1 9-1 5-5 10-5 3-5 10-6 8-4 6-3 9-5 4v6l-2 3h-2l-1 8 3 3 3 1 1 5h3v-3h3l2 5 13 2 5 4-2 6 17 5 15 6v1l-8-1-24-8-23-5-25-3h-39l-24 3-23 5-16 5h-2v-2l-14 5-5 1v2l-10 4-10 5-4-1v-2l-8 1 1-3-8 1-2-6-3-2-1-4-3-4 1-10v-10l3-9 3-8 1-13h2v-22l2-15 3-7 2-6 3-5 3-7 3-2-1-8-1-1 1-9 4-16 5-4 4-9 4-6 7-18 3-3 4-7 3-6 4-1 2-13 2-3h2l2-5 5-10 7-10 4-10 7-14 5-6h2l1-3 7-5 1-2 5-3 5-4 3-3 10-4z" fill="#02A165"/>
<path transform="translate(929,1100)" d="m0 0h2l-2 4-5 5-10 9h-2v2l-5 4-3 7 2 5 1 1-1 7-3 8-4 14-4 7-9 19-5 10-7 11-9 15-9 12-16 22-9 14-5 6-4 6-6 11-5 8-7 10h-2v2h-2l-2 6-6 9-6 8h-2l-2 6-6 7-3 5h-2l-7 14-4 6-2 5h-2l-2 4-4 4-4 6h-2v2l-5 4-2 4h-2l-1 3-7 6-4 8-5 4-7 8-6 10-8 6-1 6h2l-1 9 2 1v2l14 2 4 2h-15l-36-4-28-5-24-5-1-2-29-8-29-10-27-11-7-3 2-2 3-1 4-16 3-5 5-5 4-5 6-3 5-8 6-7 4-6 8-6 7-9 8-7 3-3 3-1 3-5 2-1 1-4h3v-3h2l1-3h2v-2l8-6 6-8 4-6 7-6 2-4 6-9 3-1 2-5 5-2 1-3 5-1 1-3h2l2-6 5-5 7-8 5-5 2-5 5-5 5-7 4-4 5-7h2l2-4 3-3h3l2-4 9-12 3-1 2-4 3-7h2v-7h-2v-4h36l33-4-2-2 24-5 27-9 18-8 23-12 19-13 11-9z" fill="#3FC468"/>
<path transform="translate(1453,347)" d="m0 0 3 1 12 25 9 21 8 22 8 28 7 35 3 21 3 38v38l-3 38-5 34-7 30-7 25-9 25-11 25-15 29-13 21-12 17-13 17-12 14-11 12-24 24-11 9-9 8-16 12-20 14-12 7h-2l-1-22-1-39-3-35-3-23-9-45 2-4 11-9 8-8 8-7 11-11 7-8 9-10 9-11 12-15 13-18 9-13 13-21 5-6 1-4h2l2-5 18-36 7-16 3-7 5-11 4-14 7-21 5-18 3-12 5-33 1-13v-60l-3-27z" fill="#D39810"/>
<path transform="translate(937,732)" d="m0 0h6l9 10 13 17 12 18 6 11 9 20 8 22 6 24 4 27 1 8v31l-3 28-6 29-10 30-10 2-27 2-21 1h-51l-46-2-91-5-13-1-10-2-1-2-5-1-3-1h-7l-8-1 5-4 22-8 4-4 6-4 7-4h2l3-10 1-6-1-2 5-8 1-4v-4l2-3 1-5-14-3 1-3 31-4 1-1 32-3 31-2 5-2-3-5 3-6 2-17 2-5 2-18v-13l2-9 1-13v-38l-6-13-6-6-11-3-5-5-3 1-13 2h-5l2-3 62-15z" fill="#D39910"/>
<path transform="translate(1453,345)" d="m0 0 2 2h-2l3 15 2 20v60l-2 21-4 25-6 24-9 27-4 14-5 11-4 10-10 22-10 19-7 13h-2l-1 6-4 4-16 26-10 13-10 14-13 16-9 11-12 13-9 10-11 11-8 7-11 10-4 1-4-11-9-30-10-28-5-15-10-23-2-7-3-1-4-7v-2h-2l-1-2-1 4h-2l-1-2-4 1-2 4-1 9-6 5-4 6-2 12h-4l-1 9-1 3-1 6-3 1v2h-5l-1 11h-2l-7-23-11-24-3-6 2-5 1-1 8-1 5-8 3-5 5-11 5-10 2-8-1-12h3l5 5 8 7 4 4 2 4 11-7 15-11 21-16 16-13 14-12 8-7 10-9 8-7 15-14 12-12 1-2h2l2-4 13-14 9-11 11-13 14-19 14-21 14-24 8-16 12-28 5-15z" fill="#82E15F"/>
<path transform="translate(487,625)" d="m0 0 2 2-1 140-4 14-10 15-6 10-6 8h-2l-1 3-6 3-10 10-3 5-8 7-5 3-2 4h-3l-2 4-4 2-1 3h-2l-2 4-4 4h-2l-2 5-3 3h-2l-2 4-8 9-5 5h-2l-1 4-6 4h-2l-2 4-4 2v2l-6 5-21 28-4 7-4 2-4 6-3 3-7 8-5 4-9 11-9 14-4 10-6 27-4 8-3 7-4 9-4 8-1 9-4 3-7 3-5 2v-2l-4-2v-2l-10-1-6-9-1-6-2-1-3-3v-2l-4-2h-3l-2 8 1 3-3 3-3-9v-4h-2l-1 2-6-29-5-37-2-26-1-25v-181l3 1 1 119 6-1 2-2 6 3v2l3 1 3 5 4 4 1 4 11 1h5l3 1v-3l8-4 9-5 5-4 4-4 8-2 1-3 8-6 9-2 2-4 4-1 6-3 1-3 24-16 4-4 6-3 1-2 9-3 7-6 4-1 1-3 8-4 5-3 5-5 4-2 2-4 8-7 5-5 8-6 7-9 6-4 3-5 6-5 4-7 5-5 7-6 1-2h2l2-6 7-8 3-4h2l2-6 4-5 8-6 4-2 1-3 1-9 2-12z" fill="#00905C"/>
<path transform="translate(650,1156)" d="m0 0 5 1 26 8 15 4 22 3h6v-1h47l16-2h12l-3 2-33 4h-29l-7-1v4h2v7h-2l-2 7-5 5-10 13-2 3-5 3-3 4h-2l-2 4-5 7-5 5-5 7h-2l-2 7-10 9-3 5-4 4-2 4-6 5h-2l-7 7-6 7-4 6-3 5-7 6-6 10-9 8h-2v2l-3 3h-2v3h-3l-1 5-3 3-2 3-5 3-12 12-7 8-5 3-6 9-6 7-4 6-6 3-6 8-3 3-5 16v3l-4 2 27 11 25 9 30 9 12 3v1h-7l-26-7-38-13-28-12 1-3-5-1-26-13-10-6-12-7-9-6-6-3v-3l-4-2-1-4v-4l-1-8-3-3 1-5 1-2h-2l-2-6 2-8 1-3 5-3 4-2 4-4 3-2h3l2-5 5-6 4 1 4-5 5-3 3-1v-2l4-4h2v-2l5-5 3-1 2-4 11-8 8-4h5l2-3 5-1 1-5-1-2h5l7-6 3-5 3-7 7-8 5-5 8-7 4-4 6-2v-2l8-6 5-3 10-7 8-5 7-8 11-10 7-8 9-2 2-4 4-2 5-1v-5l-3-2-1-4z" fill="#02A064"/>
<path transform="translate(657,807)" d="m0 0 3 1h-2l-1 3h-3l-1 2-3 3-6 8 1 8 2 1 3 7 1 6-2 9 2 2-2 10 1 3-2 5 2 9 2 13v9l-1 2h2v7l-3 9h-2v3h-2l-1 4-5 3-5 2-12 5-15 4-40 12v2l-25 10-10 3-16 8-24 13-21 14-11 8-14 11-13 12h-2v2l-8 7-12 13-8 10-6 8-5 5-2 5-8 7-4 7-2 10h-2l1-13 5-24 7-25 9-24 11-24 14-24 7-10 11-15 8-11h2l2-4 7-7 7-8 16-15 2-2 1-8h1l1 6 14-11 13-10 22-15 18-11 19-11 23-12 27-13z" fill="#B3F055"/>
<path transform="translate(735,351)" d="m0 0h30l42 3 36 5 34 7 14 4v2l-11-2-24-5-36-6-17-2-13-1h-47l-12 1-10 4h-2l-2 4-6 5h-3l-2 4-6 4-7 8-8 16-4 10-4 5-6 10-4 8-3 3-1 10-4 4h-2l-1 5-4 5-3 5-4 9-6 14-4 5-3 7-4 3-5 20v5l2 6-1 4-3 1-1 5-3 6-2 2-3 9-2 11-1 14-1 15-1 5-2 11-4 13-1 4v11l-1 6 4 6-1 2 4 2 2 5h7l1 2h6l1 2 3 2-20 12-11 8-12 9-13 11-11 11-5 3-4 1-4-1-6-30-2-11 1-17v-12l2-13 4-44 4-16 4-14 3-23 2-13 3-6 3-12 3-6h2l2-13 5-10 4-12 2-3v-8l6-21 6-10 4-9 4-5 4-7 7-14 10-11 5-5 6-7 3-4v-4l-5 1-2-2 12-3 21-4 20-3 9-1-2-2 24-2z" fill="#00905C"/>
<path transform="translate(605,1133)" d="m0 0 5 2 22 12 19 8 2 3v4l4 2v5l-3 2h-5l-4 5-9 3-14 15-8 7-5 5-16 11-5 3-6 5h-2v2l-6 3-5 5-8 7-3 1-2 4-3 4h-2l-3 9-5 7-6 5h-4v3l-1 4-5 1-2 3-7 1-9 5-9 7-7 8h-2v2l-4 4h-2v2l-5 4h-3l-2 4-4 2h-3l-6 9-9 5v2h-2l-1 2-7 2-2 13 1 4h2l-1 7 3 3 2 14v2l3 2v2l-5-2-23-16-17-13-15-13-8-7-3-3-3-9-4-6v-11l4-11 4-5 8-4 7-5h2v-2l9-7 14-9 3-3 6-3 5-2 1-3 5-3 6-3h2v-2l10-4 8-6 4-4 7-3 10-9 5-5 9-8h2v-2l5-3 4-5 9-4 6-5 7-2v-2l15-9 7-3 5-4 5-3 8-7 5-1v-2l9-6 5-4 6-4 1-7z" fill="#00905C"/>
<path transform="translate(937,732)" d="m0 0h6l9 10 13 17 12 18 6 11 9 20 8 22 6 24 4 27 1 8v31l-3 28-6 29-10 30-10 2-27 2-21 1h-51l-28-1-13-2v-1h12v-5h2v-2l13-7 4-4h2l3-9 2-10 3-19 1-13 2-7 1-10-3-2v-2l-13-1-5-2 4-2 72-8 39-5h5v-2l-12-1-3-1-2-4-2-8-1-17 1-5-3-13-5-13-10-14-9-11-5-8-2-6-2-9v-8l-2-11-3-11-6-10 1-8-6-1 1-2z" fill="#AE7014"/>
<path transform="translate(984,1027)" d="m0 0 1 2-9 17-10 15 4-1h2l5-2-1 5 2 1-3 10-1 12-1 2h-2l-2 5-5 12-1 5-4 4-1 6-3 6-2 1-2 18-6 9-3 1-4 9-2 10-4 7-3 7h-2l-3 10-5 8h-3l-1 9-6 5-6 12-5 4-3 6-6 9-7 10-2 4h-2l-1 5-3 1-2 6-6 11-3 3-5 10-5 7h-2l-3 9-7 10-9 15-7 12-1 4h-2l-2 7-8 14-8 11-6 8-2 5-9 10-7 12-8 6-6 10-5 6-3 3v5l-4 5 3 2-1 5 4 4 16 2 3 1v2l-5 1-18 1h-21l-18-1v-2l-10-1-8-2v-2l-3-1 2-9h-2v-6l6-5 4-4 5-8 10-11h2l2-5 3-5 6-5 3-3 3-5 4-2v-2l4-4 4-6 4-4h2l3-9 4-5 3-7 5-5 4-7 4-4 3-6 5-5 7-10 4-8h2v-2l4-4 6-8 4-8 5-9 7-10 6-8 7-11 15-20 8-11 9-15 5-7 7-14 8-17 3-6 3-5 3-12 4-10v-5l-3-4 2-7 2-3h2l1-3h2v-2l6-5h2l1-3 5-4h2l2-6 6-7 13-14 12-16 11-17 10-18z" fill="#76DA61"/>
<path transform="translate(1453,345)" d="m0 0 2 2h-2l3 15 2 20v60l-2 21-4 25-6 24-9 27-4 14-5 11-4 10-10 22-10 19-7 13h-2l-1 6-2 1 3-10 2-7-8-1-5 1-3-1-2-5-4-1-1-2-3-2-1-4h-10l-2-3-8-2-2-3-5-1-5-6-9-2-2-2 5-1v-2l-6-2-8-3 2-2-6-4v-2l-5-2-3 1 1-7 3-6 7-9h2l2-4 5-5 3-5 18-18 1-2h2l2-4 13-14 9-11 11-13 14-19 14-21 14-24 8-16 12-28 5-15z" fill="#B4F054"/>
<path transform="translate(756,775)" d="m0 0h3v5l-2 1h3v2l3 1 5 4v2l5 4 4 5 3 8v23l-3 25-3 7-2 10-1 11 1 10-3 17 4 6-4 4-32 4-60 9h-5v2l-18 1-28 6-33 9-22 7-3-1 17-6 23-7 17-5 10-4 10-5 1-3h2v-3h2l3-16h-2v-11l-2-13-2-9 2-7-1-2 2-9-2-4 2-8-2-7-1-5-3-1-1-8 4-6 5-6 5-2 1-2h2v-2l34-12 41-13z" fill="#E1D221"/>
<path transform="translate(657,807)" d="m0 0 3 1h-2l-1 3h-3l-1 2-3 3-6 8 1 8 2 1 3 7 1 6-2 9 2 2-2 10 1 3-2 5 2 9 2 13v9l-1 2h2v7l-3 9h-2v3h-2l-1 4-5 3-5 2-12 5-15 4-40 12v2l-25 10-10 3-16 8-24 13-21 14-11 8-14 11-13 12h-2v2l-8 7-12 13-8 10-2 1 2-4 7-9 9-10 16-16 11-9 15-13 11-7 28-20 6-5 14-10 2-3h2l2-4 9-4 10-6v-3l-2-4 6-4 1-9h-5l2-5 3-3 1-6-4 1-1-5 3-9-2-1 2-5 3-4-4-1h-9l-1-6 5-8-1-3 4-6 5-6 4-4-4 1v-3l21-12 23-12 27-13z" fill="#D1EB42"/>
<path transform="translate(487,749)" d="m0 0h1v18l-4 14-10 15-6 10-6 8h-2l-1 3-6 3-10 10-3 5-8 7-5 3-2 4h-3l-2 4-4 2-1 3h-2l-2 4-4 4h-2l-2 5-3 3h-2l-2 4-8 9-5 5h-2l-1 4-6 4h-2l-2 4-4 2v2l-6 5-21 28-4 7-4 2-4 6-3 3-7 8-5 4-9 11-9 14-4 10-6 27-4 8-3 7-4 9-4 8-1 9-4 3-7 3-5 2v-2l-4-2v-2l-10-1-6-9-1-6-2-1-3-3v-2l-4-2h-3l-2 8 1 3-3 3-3-9v-4h-2l-1 2-6-29-5-37-1-15h1l1-17 3 3 1 6h2l4 4 3 5 4 6v9l3 3 1 4 8 5 6 12h5l4 1h5l9-4 3-3 2-5 3-3h2l3-9 4-7 3-1-1-4 3-5h2l1-10 4-2 3-5h6l1-5 5-4 3-4 3-1v-4h2l1-5h2v-2l7-3 4-9 5-3h2l2-5 6-7 6-4 2-4 6-3 13-13 4-1 1-5 6-3 9-9 4-7h2l2-5 7-4h2v-2h2l2-4 7-5 5-1 2-5 8-9h2l1-3h2v-2h2l1-9 6-2 2-3v-3l7-2 1-3 6-4 3-5h2l1-4 7-11 4-2 1-3 8-4z" fill="#008A59"/>
<path transform="translate(1129,721)" d="m0 0 6 2 9 6 1-3 4 1 9 16 6 7 6 4v2l-8-1-2-2-9-3v3h-2l-1 6-2 2h-3v2l-4 3-1 8-4 2v2h-2l-1 3-4 1-1 5-4 4-2 6-3 1-1 4-7 2-1 8-3 6-3 2-1 3h-2v2h-2v8l-2 1-2 5-5 3-6 14-3 5h-4l-3 12-4 6-3 3-3 7h-2l-1 5-6 9h-3l-1 5v6l-5 5-4 8-2 5-3 4-4 8-4 5-6 16-4 11-6 9h-2l-2-6-1-7 1-9-1-9 3-22 1-12v-31l-4-29-4-19-4-16 2 1 5 15 7 14 3 7v3l4 1 9-3 3-5 2-7 3-3 3-9 8-10 5-10h2l2-5 5-12 3-3 5-14 6-9 3-8 12-16h2l2-5h2l1-4 14-12 9-7z" fill="#76DA61"/>
<path transform="translate(1458,398)" d="m0 0 2 4v33l-2 21-2 22-1 30-1 14-1 9-3 22h-2l-3 24-1 3h-2l-1 2-3-1-3 3-3 15-4 12-2 18-4 21-3 21-2 12-3 10-2 11-7 18-2 8-6 8-3 6-5 2v2l-4 1v2l-13 9-10 4-9 6-5 3h-10l-5-2-9-1-2-1-19-2-9 3-2 1-13-1 5-5 10-9 15-14 8-8 7-8 9-10 9-11 12-15 13-18 9-13 13-21 5-6 1-4h2l2-5 18-36 7-16 3-7 5-11 4-14 7-21 5-18 3-12 5-33 1-13z" fill="#E8AD0D"/>
<path transform="translate(487,870)" d="m0 0h1v11l-3 25-12 12-8 7-14 15-1 2h-2l-2 4-14 19-12 18-13 23-12 27-10 30-6 26-3 18-2 13 9-11 25-25 14-11 13-10 21-13 12-7 26-12 15-6 2-1 13 23 10 15 10 13 10 11 7 8 15 14 17 13 15 10 16 9 16 8 25 10 24 7 22 4 13 2 2 2-17-1-17-3-28-8-16-6-24-11-14-8-21-14-16-13-15-14-7-7-7-8 3 13-2 2-13 2-8 4h-2l-1 3-9 1-10 5-8 1v-2l-15 3-16 4-5 1-9 1-3-1 1-2h-2v-2h-4v2h-2v3h-13l-4-1-2-3h-8l-22 5-10 1-4 2h-4v-2h-2l-1-7v-6l-1-6 3-8 1-6 3-12 4-17 5-14 4-8 10-25 9-17 1-3h2l1-5h2l1-5 2-3h2l1-4 6-10 16-22h2l2-4 3-4h2l2-4 4-2 2-4 4-4h2l2-4 12-12h2l3-11z" fill="#00704A"/>
<path transform="translate(669,936)" d="m0 0h12l2 1 8-1 6 3 8 3 1 1 1 11-5 5-2 3 2 2-6 9-10 4-12 4-5 2h-3v2l-9 5h-13v2l-15 2h-7l-7 2h-10v3l-7 1-3 2-14 2-8 3-19 2-11 3h-3l-1 4-16 1-14 4-4 3-16 6-15 7-16 8-18 11-5 4h-3v2l-6 2 3-5h2l2-4 4-4 7-8 3-1 1-3 8-7 5-7 8-7 4-4 13-9 5-3 9-7 22-13 21-11 28-11 25-8 31-8 23-5z" fill="#F0B40A"/>
<path transform="translate(914,737)" d="m0 0 8 2-1 9 7 12 2 9 2 13 1 9 1 8 7 12 11 13 10 16 4 13 2 11-1 6 1 14 2 9 1 3 12 1 3 1v2l-36 5-42 5h-7l4-2-10-4-5-1 1-31 1-10 1-17 1-3v-20h3l-1-6 1-13 2-4h2v-12l-4-8-3-2v-2l-4-2-4-5-8-7-3-3-5-2 1-4 10-4-8 1-8 2h-6l1-2z" fill="#D39810"/>
<path transform="translate(1098,576)" d="m0 0h7l4 3v2l3 1 11 14 9 11 16 24 8 13 10 18 11 23 5 13 3 10v5l-2-4-2-6v9l1 4v6l-3 1-1-2-9 3-2 5-1 2h-2l-1 4-3 3h-2l-12-18-5-8v-2h-2l8 14-1 2-8-13-7-11-8-12-13-18-13-16-11-13-12-13-24-24 1-2 6 3h6l9-7 2-2h3l5-3 7-8 7-5z" fill="#84E373"/>
<path transform="translate(709,789)" d="m0 0 2 1-5 5-2 4-1 5 3 3 2 7 4 4v9l3 2-4 16 3 12v12l-4 15 1 16-1 10-1 9-5 5-7 3-22 5-36 7-28 7-31 9-9 3-3-1 17-6 23-7 17-5 10-4 10-5 1-3h2v-3h2l3-16h-2v-11l-2-13-2-9 2-7-1-2 2-9-2-4 2-8-2-7-1-5-3-1-1-8 4-6 5-6 5-2 1-2h2v-2l34-12z" fill="#DEDE36"/>
<path transform="translate(724,925)" d="m0 0h20l-1 3 12 2 2 1-1 7-2 4 1 2-6 12 1 5-5 12-9 5-7 5h-2l-1 3-22 8-2 2 10 1h7v1l5 1-1 2 10 2v1l-43-1h-39l-29 1-40 4-33 6-10 2 1-4 14-4 19-2 13-4 9-1 2-2h8v-3l13-2 4-1h10l12-1v-2l13-1 7-4h2v-2l9-3 11-4 10-4 5-8-2-3 4-5 3-2-1-11-8-3-6-3-11 1v-1h-11l-23 4-26 6-24 6-36 12-23 10-19 10-24 15-5 5-9 5-8 6h-2l-2 4-7 6-4 6-7 7-8 7-7 9-6 5-1 3 4-1v-2l5-3 13-8 17-10 24-11 16-6 4-3 12-3 12-1 2 2-23 7h-2l-1 2h-2l-24 10-19 9-25 15-14 10-16 13-10 9-20 20-6 8-2 1 2-9h2v-7l4-9 6-5 2-3h2l1-5 5-5 4-5 11-14 7-7 7-8 4-4h2v-2l8-7 14-12 15-11 21-14 14-8 18-10 13-6 8-2 20-8 30-10 26-7 30-7 5-1h18v-2l45-7z" fill="#E8AD0D"/>
<path transform="translate(877,747)" d="m0 0h5l-3 3-8 3v3l5 2 13 13 5 4v2l4 2 4 8-1 12-2 1-1 3-1 19h-3l1 20-1 4-1 22-1 4-1 28-1 3 7 1 9 3-1 2-7 2-18 2-31 2h-8l3-3-3-4 3-6 2-17 2-5 2-18v-13l2-9 1-13v-38l-6-13-6-6-11-3-5-5-3 1-13 2h-5l2-3 49-12h10z" fill="#DBAB16"/>
<path transform="translate(605,1133)" d="m0 0 5 2 5 3 1 4-1 4 4 2 2 4-2 2-9 2-2 4-3 3h-2v2l-11 8-11 9-4 3-4 1-5 4-11 2-9 6h-3v3l-4 1-2 4-6 8-7 5h-3l-1 3-6 5-1 2-5 1-2 3-6 2v2h2l-2 5-6 3-12 4-1 2 4 1-8 2h-2l-1 4-4 2-3 5-5-1v3l-5 3-4 1-9 2-1 2h-4l-2 6-8 3-9 9-6 4-4 4-6 4-8 5-3 3-1 7v8l3 3-1 8 3 8 7 8 3 3-1 2-11-10-11-9-6-6-3-9-4-6v-11l4-11 4-5 8-4 7-5h2v-2l9-7 14-9 3-3 6-3 5-2 1-3 5-3 6-3h2v-2l10-4 8-6 4-4 7-3 10-9 5-5 9-8h2v-2l5-3 4-5 9-4 6-5 7-2v-2l15-9 7-3 5-4 5-3 8-7 5-1v-2l9-6 5-4 6-4 1-7z" fill="#008A59"/>
<path transform="translate(1326,534)" d="m0 0v3l-7 8-5 5-5 7-3 10h2l1-2 5 3v2l5 2 2 2v2l11 5v2l-3 2 9 2 6 7 4-1 2 1v4h5l5 2v3l8-1 4 4 5 4 4 4v3l14-1v2h2l-1 7-4 10-9 14-1-4 2-4-4 1-7 3-3 3-7 3-4-1-5-1h-9l-6-3-13-3-11-7-2-3-5 1-7-6-2-5-6-3-9-1-12-4-10-5 1-4-3-1 2-2-1-2 1-7 10-11 5-5 4-5 11-9 11-10 8-7 10-9z" fill="#9DE35E"/>
<path transform="translate(651,362)" d="m0 0h2v2l8 1 6-1 1 4-8 3-15 15-7 8h-2l-2 5-5 11-3 5-6 7-14 22-2 9-2 7-6 19-5 14-5 13-6 10v5l-3 5-3 10v10l2 1-1 8-4 9-2 6v6l-2 2-1 7-3 5-2 6v11l-1 7 2 1-2 5v14h-2l-1 7-1 11-2 16 1 8 2 14 7 7v4l4-1-3 9 5-3 6-3 3-1 3 2-11 9-12 12-5 3-4 1-4-1-6-30-2-11 1-17v-12l2-13 4-44 4-16 4-14 3-23 2-13 3-6 3-12 3-6h2l2-13 5-10 4-12 2-3v-8l6-21 6-10 4-9 4-5 4-7 7-14 10-11 5-5 6-7 3-4v-4l-5 1-2-2 12-3z" fill="#008A59"/>
<path transform="translate(1233,640)" d="m0 0 11 5 13 8 2 3h4l4 1 2 5 6 4 3 3 7 3 16 8 8 3v3l9 1 13-1v-2l4 1v1l9 2 6-4 5-1 1 2-2 6-13 16-9 11-12 13-9 10-11 11-8 7-11 10-4 1-4-11-9-30-10-28-5-15-10-23-5-13z" fill="#79CF5A"/>
<path transform="translate(898,910)" d="m0 0 4 1-1 1h16l9 3 4 5 1 7-3 8 2 1v2h-2l1 7-1 5v30l-4 13-6 9-7 4-8 2-27 1-1 2h-20l-13-2v-1h12v-5h2v-2l13-7 4-4h2l3-9 2-10 3-19 1-13 2-7 1-10-3-2v-2l-13-1-5-2 4-2z" fill="#BC8014"/>
<path transform="translate(825,760)" d="m0 0 3 1v1l2 4 11 3 8 8 5 13v37l-2 19-1 5v11l-2 18-3 10-1 12-3 7 3 4-6 3-19 1-1-5-3-3 3-6v-12l-2-4 2-13 1-2v-31l3-12 4-1-1-15 2-23h2l-1-2-4-1-4-6-8-10 2-4-1-2 9-4z" fill="#E7B815"/>
<path transform="translate(611,369)" d="m0 0 2 1-1 2-38 12-30 12-14 7-4 6-7 10-3 9-2 4-1 13v17l-2 12-4 26-4 48-4 63-2 22-2 11-4-2-1-29-1-152v-39h-2v-2l29-15 21-10 27-11 36-12z" fill="#00704A"/>
<path transform="translate(940,734)" d="m0 0 5 2 2 2 3 16 2 9 3 8 6 18 4 16 6 28 17 51 4 6 7 2 6 2 1 2-12 3-8 1v-2l-12-1-3-1-2-4-2-8-1-17 1-5-3-13-5-13-10-14-9-11-5-8-2-6-2-9v-8l-2-11-3-11-6-10 1-8-1-2z" fill="#BD8114"/>
<path transform="translate(1196,600)" d="m0 0 4 2 4 5 7 6 4 4 2 4 11-7 15-11 3-1-1 3-10 8-4 2v2l-5 2 2 7 3 9h3v2h2v2l5 2 1 3-4-1-4-2 2 15-3-3-3-5v-2h-2l-1-2-1 4h-2l-1-2-4 1-2 4-1 9-6 5-4 6-2 12h-4l-1 9-1 3-1 6-3 1v2h-5l-1 11h-2l-7-23-11-24-3-6 2-5 1-1 8-1 5-8 3-5 5-11 5-10 2-8-1-12z" fill="#76DB62"/>
<path transform="translate(1403,631)" d="m0 0 1 4-3 14-3 9-2 16v7l-5 14-8 14-2 4-10 6-5 4h-2l-2 4-7 6-10 2h-7l-5 3h-5l-3 3-7 3-5 2-3-1 5-5 5-6 7-6 1-2h2l2-4 7-7 9-11 3-4h2l2-4 21-28 12-19 5-7h2v-2h2l1-3h2l2-5z" fill="#F0B40A"/>
<path transform="translate(1458,398)" d="m0 0 2 4v33l-2 21-2 22-1 30-1 14-1 9-3 22h-2l-3 24-1 3h-2l-1 2-3-1-3 3-1 6h-2l-1 2-1-9-6 7-4 7-4 8-2 5-2 1-4 13-1 2h-2l-1 5-4 6-3 3h-2v2l-5 5-11 18-21 28-5 7h-2l-2 4-8 9-2-1 9-11 9-13 8-11 3-7 8-11 13-21 5-6 1-4h2l2-5 18-36 7-16 3-7 5-11 4-14 7-21 5-18 3-12 5-33 1-13z" fill="#DCAE16"/>
<path transform="translate(535,1014)" d="m0 0m-16-36h2v2l-17 9h-2l-1 3-19 12-20 14-14 11-16 15-9 9-8 10-1 6 10-2v-2l5-3h3v-2l5-3 13-8 17-10 24-11 16-6 4-3 12-3 12-1 2 2-23 7h-2l-1 2h-2l-24 10-19 9-25 15-14 10-16 13-10 9-20 20-6 8-2 1 2-9h2v-7l4-9 6-5 2-3h2l1-5 5-5 4-5 11-14 7-7 7-8 4-4h2v-2l8-7 14-12 15-11 21-14 14-8z" fill="#E3C61A"/>
<path transform="translate(1265,590)" d="m0 0h3l2 3 5 2 4 4 16 8 3 2 7 2 9 7 5 4 15 10 2 2h3l1-3 3-1 6 5-3 2 7 3 4 1v-2l5 1-1 3 1 3 7-1 2-3 10-3-1 3-7 3-3 3-7 3-4-1-5-1h-9l-6-3-13-3-11-7-2-3-5 1-7-6-2-5-6-3-9-1-12-4-10-5 1-4-3-1 2-2-1-2 1-7z" fill="#96EB5D"/>
<path transform="translate(724,925)" d="m0 0h7l-2 2-31 5-18 3-2 1-28 4-30 7-24 6-36 12-23 10-19 10-24 15-5 5-9 5-8 6h-2l-2 4-7 6-4 6-7 7-8 7-7 9-6 5-2 5h-3v2l-10 3-1-5 4-6 14-15 10-10 8-7 13-10 17-12 22-14 3-3 15-8h2l1-4 13-6 8-2 20-8 30-10 26-7 30-7 5-1h18v-2l45-7z" fill="#E7B915"/>
<path transform="translate(873,395)" d="m0 0 3 1-4 12-5 6-1 3h-2v3l-3 3-6 3v6h2l1-3 2 1-3 9-3 1-1 2h-3l1 8 2 2-1 4-4 5-2 11 2 3-1 7-4-1-3 5-1 6h3l-1-6 2-2 1 9-4 4-5 2-4 5v-2l-3 3-2 6h-2l-1 3-4-1 3-10 2-4 2-1v-2l-2-1v-2h2l-1-4 3-5 3-9v-8h2l1-5 5-10-1-3v-8l7-10 2-6 8-10v-2l5-2v-2h2l2-9 3-1 1-3z" fill="#1CC57F"/>
<path transform="translate(210,1053)" d="m0 0h2l6 23 6 19 10 29 12 28 14 29 10 18 10 17 8 12 12 17 9 12 11 14 9 11 9 10 7 8 33 33 8 7 15 13 15 12 17 12 21 14 7 4 13 8 11 6 23 12 11 5v2l-5-1-21-10-23-13-20-12-16-11-18-13-16-13-11-9-11-10-8-7-36-36-9-11-8-9-13-17-14-19-13-20-9-15-10-18-11-21-14-32-10-28-8-25-5-19z" fill="#077A4E"/>
<path transform="translate(966,1426)" d="m0 0h4l-1 3-20 9-27 10-28 9-24 6h-3l-2-3-11-2-3-3 3-2v-1l7-1-1-4 5-1 13 2v-3h10l10-2h8l6-2 8-1 24-7 18-5z" fill="#9DE35E"/>
<path transform="translate(1192,790)" d="m0 0h6l8 6 5 8 2 8-1 8-6 8-6 5-4 2h-8l-8-5-6-7-3-7-1-10 3-8 4-5 4-1-2 7h-2l-1 7-1 1 1 9 3-1h3l2-3 8-1 2-4h3l2 3 3-4-2-6 1-3-1-2-5-2-3-1z" fill="#B3F055"/>
<path transform="translate(1180,701)" d="m0 0 2 2 6 21 1 13-3 8-3 3-3 1-1 4-2 2h-7l-9-7-7-10-13-23-2-5h2l11 18 6 8 1 1 4-3 1-3h2l2-5 3-3h3l1-2 2 1 1-2 3 1v-8l-1-5z" fill="#96EC5D"/>
<path transform="translate(1453,345)" d="m0 0 2 2h-2l3 15 2 20v60l-2 21-4 25-6 24-12 36-1-3 8-25 6-24 4-21 2-15v-23l-1-9-1-26v-20l-1-4-3 1-1-2-5 1 1-5 8-21z" fill="#D1EB41"/>
<path transform="translate(651,995)" d="m0 0 31 1 4 1h9l8-1 9 1h7v1l5 1-1 2 10 2v1l-43-1h-39l-29 1-40 4-33 6-10 2 1-4 14-4 19-2 13-4 15-2h25l7-2 13-2z" fill="#E6BB17"/>
<path transform="translate(1432,553)" d="m0 0 1 3-7 19-2 4h2v8l-6 10-4 8-2 5-2 1-4 13-1 2h-2l-1 5-4 6-3 3h-2v2l-5 5-11 18-21 28-5 7h-2l-2 4-8 9-2-1 9-11 9-13 8-11 3-7 8-11 13-21 5-6 1-4h2l2-5 18-36 7-16 3-7z" fill="#E7BB16"/>
<path transform="translate(724,925)" d="m0 0h7l-2 2-31 5-18 3-2 1-28 4-30 7-24 6-36 12-23 10-8 4-6-1-2-1 14-7 8-2 20-8 30-10 26-7 30-7 5-1h18v-2l45-7z" fill="#E6BD17"/>
<path transform="translate(735,351)" d="m0 0h30l42 3 36 5 34 7 14 4v2l-11-2-24-5-36-6-17-2-13-1h-96l1-2 4-1z" fill="#077F52"/>
<path transform="translate(1185,788)" d="m0 0h9l4 2h-6l4 2 5 3 1 4v3l1 4-5 5-1-4-4 2-1 2h-5l-1 2h-2l-4 4-4-1-2-10h2l1-7h2v-5l1-4z" fill="#96EC5C"/>
<path transform="translate(661,656)" d="m0 0h4l-1 3-20 8-23 12-20 13-16 12-13 11-22 22-13 17-12 18-10 18-6 13-2-1 1-5 8-16 10-17 13-18 12-14 23-23 14-11 14-10 14-9 9-5 1-2 16-8h4v-2z" fill="#068152"/>
<path transform="translate(910,377)" d="m0 0 9 1 31 11 32 14 24 12 24 14 19 12 17 12 13 10 10 8 6 5-1 2-11-9-16-12-20-14-23-14-18-10-17-9-29-13-23-9-26-9z" fill="#0B8F5A"/>
<path transform="translate(415,201)" d="m0 0 4 2 11 14 10 15 12 21 8 16 9 21 8 24 5 21 4 23 2 26v36h-2l-2-53-3-23-6-27-10-30-12-26-10-19-10-16-9-12-9-11z" fill="#0A9860"/>
<path transform="translate(535,1014)" d="m0 0m-3 1 5 1-4 2-19 6h-2l-1 2h-2l-24 10-19 9-25 15-13 9-2-1 6-8 5-3 13-8 17-10 24-11 16-6 4-3 12-3z" fill="#DFCD24"/>
<path transform="translate(195,757)" d="m0 0 3 1v162h-2l-1-46z" fill="#007E53"/>
<path transform="translate(1432,553)" d="m0 0 1 3-7 19-2 4h2v8l-6 10-4 8-2 5-2 1-4 13-1 2h-2l-1 5-4 6-3 3h-2v2l-5 2 2-5 10-18 10-19 3-11 9-20 3-7z" fill="#E7B915"/>
<path transform="translate(519,978)" d="m0 0h2v2l-17 9h-2l-1 3-19 12-20 14-14 11-16 15-9 9-8 7-7 7v-3l9-10 11-12 4-2v-2l8-7 14-12 15-11 21-14 14-8z" fill="#E2CE23"/>
<path transform="translate(488,422)" d="m0 0h1l1 42 1 158-3 3z" fill="#008758"/>
<path transform="translate(627,999)" d="m0 0m-29 1h27l1 2h-4v2l-40 4-33 6-10 2 1-4 14-4 19-2 13-4z" fill="#E5B916"/>
<path transform="translate(778,352)" d="m0 0 29 2 36 5 34 7 14 4v2l-11-2-24-5-36-6-17-2-21-2v-2z" fill="#088152"/>
<path transform="translate(614,944)" d="m0 0h5v2l-17 4-13 4h-2v2l-27 9-23 10-8 4-6-1-2-1 14-7 8-2 20-8 30-10z" fill="#E4C61C"/>
<path transform="translate(1365,675)" d="m0 0v3l-25 35-2 4-7 7-1 2h-2l-2 4-9 8-3 5-8 6h-2l-1 3-6 4-7 7-2-1 10-10 8-7 11-11 7-8 9-10 9-11 12-15z" fill="#E4B515"/>
<path transform="translate(313,125)" d="m0 0 7 1 17 9 22 14 16 12 11 9 13 12 12 12-2 3-7-8-11-11-11-9-12-10-20-14-21-12-14-7z" fill="#38C56D"/>
<path transform="translate(394,1080)" d="m0 0 1 2-3 7-3 1-3 10 7-6 5-3 9-8 2 1-28 28-6 8-2 1 2-9h2v-7l4-9 6-5 2-3h2l1-5z" fill="#D7D837"/>
<path transform="translate(702,996)" d="m0 0 10 1h7v1l5 1-1 2 10 2v1l-43-1-20-1v-1h7v-2l4-1z" fill="#DAB21A"/>
<path transform="translate(415,201)" d="m0 0 4 2 11 14 10 15 12 21 8 16 9 21 4 11-1 3-2-3-8-21-15-31-7-11-5-9-10-14-10-12z" fill="#16AF69"/>
<path transform="translate(1308,565)" d="m0 0 6 3v2l5 2 2 2v2l11 5v2l-3 2-3 1-8-3-6-1-3-3-2-12z" fill="#96EC5D"/>
<path transform="translate(1501,497)" d="m0 0 2 3 3 38v38l-1 6h-1l-1-33-2-15v-18l-1-15z" fill="#C29119"/>
<path transform="translate(554,730)" d="m0 0v3l-8 9-12 16-12 19-12 23-2 3-1-4 8-16 9-16 13-18 8-10z" fill="#05774C"/>
<path transform="translate(984,1027)" d="m0 0 1 2-9 17-10 15-8 10-6 8-11 13-5 6-2-3 7-7 7-8 13-17 11-17 10-18z" fill="#7BD460"/>
<path transform="translate(195,874)" d="m0 0h1v46h2l1 32v24l-2-2-1-11-1-25z" fill="#189156"/>
<path transform="translate(681,355)" d="m0 0h12l-1 2-18 2-32 6-22 5-4-1 2-2 26-6 29-5z" fill="#097C4E"/>
<path transform="translate(910,377)" d="m0 0 9 1 31 11 25 11 2 3-5-1-32-13-29-10z" fill="#0D8F59"/>
<path transform="translate(1475,721)" d="m0 0 1 4-8 19-12 25-10 18-12 19-4 5-2-1 12-18 12-21 15-31z" fill="#B77F16"/>
<path transform="translate(1462,364)" d="m0 0 3 3 9 19 11 30 5 17v5h-2l-10-33-10-25-5-12z" fill="#BD8717"/>
<path transform="translate(787,1169)" d="m0 0h12l-3 2-33 4h-29l-4-2 1-1 40-1z" fill="#0E9F60"/>
<path transform="translate(651,1154)" d="m0 0 9 2 21 7 24 5 19 3 2 2-17-1-17-3-28-8-13-5z" fill="#0A7F50"/>
<path transform="translate(196,306)" d="m0 0h1v82l-2 1-1-5 1-76z" fill="#41C568"/>
<path transform="translate(850,662)" d="m0 0 10 4 21 11 23 16 16 13 10 9 1 2-4-2-11-10-20-15-21-13-15-8-10-5z" fill="#1EAE66"/>
<path transform="translate(1454,458)" d="m0 0h2l-1 11-3 19-6 24-12 36-1-3 8-25 6-24 4-21 2-15z" fill="#D5E83E"/>
<path transform="translate(519,978)" d="m0 0h2v2l-17 9h-2l-1 3-19 12-20 14-10 8-2-1 3-3h2v-2l8-6 18-12 5-2 2-5 16-9z" fill="#E2CE23"/>
<path transform="translate(467,1039)" d="m0 0 3 1-5 4-7 4h-2v3l-18 11-10 7-2-1 6-8 5-3 13-8z" fill="#E3C91C"/>
<path transform="translate(724,925)" d="m0 0h7l-2 2-31 5-18 3-2 1-14 2h-5l1-3h12v-2l45-7z" fill="#E7BB16"/>
<path transform="translate(1309,739)" d="m0 0h2l-2 4-9 9-8 7-11 10-4 1-1-2v-6l5-1v-2l11-4 8-7 7-7h2z" fill="#47C565"/>
<path transform="translate(543,1437)" d="m0 0 10 3 19 7 29 9 16 4v1h-7l-26-7-38-13-5-2z" fill="#17A060"/>
<path transform="translate(1413,596)" d="m0 0 1 2-3 7-8 16-11 20-5 8-4 1v3h-2l2-5 8-13 3-3 1-4h2l2-5 10-20z" fill="#E0CE22"/>
<path transform="translate(463,1397)" d="m0 0 4 2 11 6 18 10 23 11v2l-5-1-21-10-23-13-9-5z" fill="#098352"/>
<path transform="translate(423,1068)" d="m0 0m-1 1 3 2-9 8-7 5-4 2-9 8-5 3-3 3h-2l1-7 3-3v4l11-8 10-9z" fill="#DECE25"/>
<path transform="translate(602,688)" d="m0 0h2v2l-14 10-11 9-10 9-13 13-2-2 20-20 14-11z" fill="#097F50"/>
<path transform="translate(535,1014)" d="m0 0m-3 1 5 1-4 2-19 6h-2l-1 2-3-1-16 5-3-1 18-7 4-3 12-3z" fill="#DFB71A"/>
<path transform="translate(573,1006)" d="m0 0h17v1l-36 6-15 3 1-4 14-4z" fill="#D8C023"/>
<path transform="translate(194,730)" d="m0 0h2l1 5 1 22h-3v53h-1z" fill="#17A865"/>
<path transform="translate(506,804)" d="m0 0 1 3-11 33-5 20h-2l1-10 7-25 7-19z" fill="#077A4D"/>
<path transform="translate(713,1474)" d="m0 0h17l34 1v2l-8 1h-21l-18-1v-2z" fill="#7CD35F"/>
<path transform="translate(1365,675)" d="m0 0v3l-20 28-11 12-14 15-2 1 2-4 7-7 9-11 11-13 15-20z" fill="#DCAC16"/>
<path transform="translate(808,533)" d="m0 0 5 1v6l-6 7-3 3-1 4-3 1-1-5 2-4 2-5 4-4z" fill="#1DC680"/>
<path transform="translate(412,1363)" d="m0 0 4 2 17 12 21 14 7 4 2 3-5-1-24-16-18-13-4-3z" fill="#077D50"/>
<path transform="translate(485,997)" d="m0 0 2 1-4 4-11 7-10 7-5 4h-2v2l-10 8-10 9-3 1v-2l8-7 14-12 15-11z" fill="#E0D82C"/>
<path transform="translate(905,739)" d="m0 0h5v2l-28 6-17 4h-6l1-2z" fill="#C0911A"/>
<path transform="translate(1244,602)" d="m0 0h2l-1 3-10 8-4 2v2l-5 2v5l-3 2-4-2v-4l16-11z" fill="#7BD15D"/>
<path transform="translate(1337,906)" d="m0 0 2 1-8 7-23 16-8 5-4-1 8-5 15-10z" fill="#B7881B"/>
<path transform="translate(851,361)" d="m0 0 13 2 25 6 2 1v2l-11-2-24-5-6-1z" fill="#098554"/>
<path transform="translate(703,645)" d="m0 0h11l-2 2-26 5-10 3h-5l-1-2 18-5 13-2z" fill="#0E8A56"/>
<path transform="translate(765,642)" d="m0 0h10l25 4 6 2v1h-7l-19-3-11-1-4-2z" fill="#0B935D"/>
<path transform="translate(1054,445)" d="m0 0 11 7 14 11 10 8 6 5-1 2-11-9-16-12-14-10z" fill="#19AE67"/>
<path transform="translate(313,125)" d="m0 0 7 1 17 9 16 10 1 3-5-2-13-8-23-12z" fill="#35C56F"/>
<path transform="translate(768,919)" d="m0 0m-7 1 14 1v1l-25 3h-19v-1l12-2z" fill="#E4BF18"/>
<path transform="translate(1380,653)" d="m0 0 3 1-2 4-10 15-8 10v-3l5-10 8-11z" fill="#E1C019"/>
<path transform="translate(960,1064)" d="m0 0h2l-2 5-8 10-11 13-5 6-2-3 7-7 7-8z" fill="#7CD35F"/>
<path transform="translate(394,1080)" d="m0 0 1 2-3 7-3 1-2 5-5 10-5 3 2-9 5-6 5-5h2l1-5z" fill="#D3E93E"/>
<path transform="translate(1280,787)" d="m0 0h1l6 31 3 23v4h-2l-2-11-2-18-4-22z" fill="#C2ED4B"/>
<path transform="translate(654,935)" d="m0 0h6l-1 3-13 3-8 1-7-1v-1z" fill="#E5C319"/>
<path transform="translate(1021,424)" d="m0 0 14 8 18 12 1 2-4-1-24-15-6-3z" fill="#0F9C60"/>
<path transform="translate(451,1051)" d="m0 0h2v2l-15 9-10 7-2-1 6-8 13-5z" fill="#DECF28"/>
<path transform="translate(554,730)" d="m0 0v3l-8 9-12 16-2 2v-4l8-11 9-10z" fill="#077A4D"/>
<path transform="translate(681,355)" d="m0 0h12l-1 2-18 2-11 2h-6l3-3z" fill="#0A7E4F"/>
<path transform="translate(724,925)" d="m0 0h7l-2 2-31 5-8-1v-1z" fill="#E6BD16"/>
<path transform="translate(937,732)" d="m0 0h6l6 7-4-2-5-2-20 4h-5l1-2z" fill="#B37915"/>
<path transform="translate(809,650)" d="m0 0h8l17 5 15 6v1l-8-1-24-8-8-2z" fill="#14A061"/>
<path transform="translate(827,757)" d="m0 0 2 1v2l-7 3-13 2h-5l2-3z" fill="#D8B61D"/>
<path transform="translate(457,268)" d="m0 0 3 1 9 21 4 11-1 3-2-3-8-21z" fill="#0CA868"/>
<path transform="translate(630,670)" d="m0 0h3l-1 3-19 11-9 5 2-4 19-11 1-2z" fill="#077E50"/>
<path transform="translate(463,1397)" d="m0 0 4 2 11 6 9 5-1 3-25-14z" fill="#0B8654"/>
<path transform="translate(1492,447)" d="m0 0 2 2 4 19v13h-1l-6-33z" fill="#B97D14"/>
<path transform="translate(496,832)" d="m0 0 1 4-6 24h-2l1-10 5-17z" fill="#05774C"/>
<path transform="translate(850,662)" d="m0 0 10 4 19 10-2 2-17-9-10-5z" fill="#14A362"/>
<path transform="translate(554,1008)" d="m0 0h11l-4 2-9 1v2l-13 3 1-4z" fill="#E1B919"/>
<path transform="translate(1413,596)" d="m0 0 1 2-3 7-11 22h-2l1-4 10-20z" fill="#E2CD20"/>
<path transform="translate(1462,364)" d="m0 0 3 3 9 19 2 7-3-1-7-16-4-10z" fill="#C08C18"/>
<path transform="translate(574,709)" d="m0 0m-1 1m-1 1m-1 1 1 3-16 16-2-2z" fill="#108652"/>
<path transform="translate(406,1081)" d="m0 0m-1 1m-1 1m-1 1m-1 1m-1 1v3l-5 5-5 3-3 3h-2l1-7 3-3v4z" fill="#E1D222"/>
<path transform="translate(1398,849)" d="m0 0v3l-9 10-15 15-2-1 17-17 7-8z" fill="#AF7014"/>
<path transform="translate(778,352)" d="m0 0 29 2-4 2h-13l-8-1v-2z" fill="#0F8A55"/>
<path transform="translate(1156,738)" d="m0 0 6 5 5 6 6 4v1h-5l-7-6-5-7z" fill="#AFF056"/>
<path transform="translate(954,393)" d="m0 0 8 1 14 7 1 2-5-1-17-7z" fill="#068A58"/>
<path transform="translate(910,377)" d="m0 0 9 1 12 4 1 3-9-2-12-4z" fill="#068756"/>
<path transform="translate(651,1154)" d="m0 0 9 2 13 5 1 2-7-1-16-6z" fill="#087D4F"/>
<path transform="translate(929,1100)" d="m0 0h2l-2 4-5 5-10 9-2-1 7-8 8-7z" fill="#50CA69"/>
<path transform="translate(875,1008)" d="m0 0m-16 0h15l1 3h-20l-5-1v-1z" fill="#BB8718"/>
<path transform="translate(516,1020)" d="m0 0m-6 1h6l1 2-5 1-1 2-3-1-16 5-3-1 16-6z" fill="#DDC51F"/>
<path transform="translate(521,1428)" d="m0 0 9 3 13 5v2l-6-1-15-6z" fill="#0B925B"/>
<path transform="translate(1432,553)" d="m0 0 1 3-7 19-2 3h-2l2-7 3-7z" fill="#E3CA1F"/>
<path transform="translate(445,245)" d="m0 0 4 2 9 17 1 4-3-1-11-21z" fill="#17AE69"/>
<path transform="translate(702,1168)" d="m0 0 18 2 6 2v1l-17-1-6-1z" fill="#0E8452"/>
<path transform="translate(1337,906)" d="m0 0 2 1-8 7-13 9v-3l16-12z" fill="#B67F17"/>
<path transform="translate(354,146)" d="m0 0 9 6 10 7-1 2-4-2-14-10z" fill="#3CC76E"/>
<path transform="translate(905,739)" d="m0 0h5v2l-19 4-4-1 8-3z" fill="#B67B15"/>
<path transform="translate(1e3 414)" d="m0 0 12 6 6 4v2l-6-2-13-7z" fill="#12925A"/>
<path transform="translate(470,1038)" d="m0 0h7l-3 3-16 9h-2v-2l9-5h2l1-3h2z" fill="#DCD22D"/>
<path transform="translate(1475,721)" d="m0 0 1 4-8 19-1 2h-2l3-9z" fill="#B67F17"/>
<path transform="translate(444,1386)" d="m0 0 5 2 9 5 5 5-5-1-14-9z" fill="#098151"/>
<path transform="translate(787,1169)" d="m0 0h12l-3 2-14 2h-5l2-3z" fill="#13A161"/>
<path transform="translate(201 1e3)" d="m0 0 2 1 2 14v5h-2l-2-10z" fill="#077C4F"/>
<path transform="translate(768,919)" d="m0 0m-7 1 14 1v1l-16 2h-9v-1l9-2z" fill="#E0B617"/>
<path transform="translate(685,649)" d="m0 0h5l-1 2-13 4h-5l-1-2z" fill="#058253"/>
<path transform="translate(489,599)" d="m0 0 2 1v22l-3 3v-13z" fill="#008357"/>
<path transform="translate(454,1048)" d="m0 0h2v3l-6 2-5 3-8 2 3-3z" fill="#E2CC1F"/>
<path transform="translate(851,361)" d="m0 0 13 2 4 2-6 1-12-2z" fill="#0B8956"/>
<path transform="translate(984,1027)" d="m0 0 1 2-7 13-3 1 2-6 5-9z" fill="#75D15F"/>
<path transform="translate(991,1010)" d="m0 0 4 4-1 2h-3l-1 2h-2l-1 6-1 2h-2l3-9z" fill="#6FD261"/>
<path transform="translate(910,699)" d="m0 0 4 2 13 11 4 5-4-2-11-10-6-5z" fill="#15A763"/>
<path transform="translate(210,1053)" d="m0 0h2l3 12-1 6-2-4-2-8z" fill="#097F4F"/>
<path transform="translate(508,1024)" d="m0 0 1 2-17 7-3-1 6-2v-2z" fill="#D8D631"/>
<path transform="translate(313,125)" d="m0 0 7 1 11 6-4 1-14-7z" fill="#32C572"/>
<path transform="translate(680,930)" d="m0 0h3v2l-11 1v2h-12v-1z" fill="#E2CF1F"/>
<path transform="translate(1350,700)" d="m0 0v3l-9 10-2-1 8-10z" fill="#E7B915"/>
<path transform="translate(681,355)" d="m0 0h12l-1 2-9 1h-9l2-2z" fill="#05764C"/>
<path transform="translate(415,201)" d="m0 0 4 2 8 10-1 3-11-13z" fill="#0EAB69"/>
<path transform="translate(821,1162)" d="m0 0h6l-3 2-14 4h-5l4-3z" fill="#18A964"/>
<path transform="translate(1474,392)" d="m0 0 3 2 4 11-1 4-2-4-4-10z" fill="#BA8116"/>
<path transform="translate(1453,347)" d="m0 0 3 1 3 9-3-1v6h-1z" fill="#D9AF19"/>
<path transform="translate(1421,578)" d="m0 0 1 3-4 11-2 4h-2l1-5z" fill="#E2CF22"/>
<path transform="translate(580,1450)" d="m0 0 17 5v1l-10-1-9-3z" fill="#1AA361"/>
<path transform="translate(499,990)" d="m0 0 3 1-15 10-2-1 4-4 6-3h2v-2z" fill="#E3C71B"/>
<path transform="translate(703,928)" d="m0 0h6l-2 2-9 2-8-1v-1z" fill="#E6BF17"/>
<path transform="translate(923,735)" d="m0 0h8v2l-11 2h-5l1-2z" fill="#97761E"/>
<path transform="translate(1054,445)" d="m0 0 11 7 3 4-4-1-11-8z" fill="#18A562"/>
<path transform="translate(661,656)" d="m0 0h4l-1 3-10 3-1-3z" fill="#078252"/>
<path transform="translate(809,650)" d="m0 0h8l10 3v2l-10-2-8-2z" fill="#0F995E"/>
<path transform="translate(1498,482)" d="m0 0h2l1 3v11h-2l-1-6z" fill="#B88117"/>
<path transform="translate(608 1e3)" d="m0 0h8l6 1v1l-18 1v-2z" fill="#D4C428"/>
<path transform="translate(1480,408)" d="m0 0h2l3 8v5h-2l-3-9z" fill="#B8871A"/>
<path transform="translate(1388,640)" d="m0 0 1 2-2 7-4 1v3h-2l2-5z" fill="#DADD32"/>
<path transform="translate(944,1086)" d="m0 0v3l-8 9-2-3z" fill="#7BD25E"/>
<path transform="translate(482,1032)" d="m0 0 3 1-6 4-2 2-5-1 3-3z" fill="#E2CA1D"/>
<path transform="translate(313,933)" d="m0 0 4 1-1 5-5 2 1-7z" fill="#00915D"/>
<path transform="translate(640,665)" d="m0 0h3l-1 3-9 4-1-3z" fill="#0A8654"/>
<path transform="translate(1380,653)" d="m0 0 3 1-2 4-5 5-2-1z" fill="#DDD228"/>
<path transform="translate(412,1363)" d="m0 0 4 2 9 7-1 2-12-9z" fill="#0A8050"/>
<path transform="translate(213,1056)" d="m0 0 3 1 2 6-3 3-2-4z" fill="#008758"/>
<path transform="translate(536,971)" d="m0 0h2v2l-5 4-6 2v-3z" fill="#E6BF17"/>
<path transform="translate(1496,463)" d="m0 0h1l1 5v13h-1l-2-11z" fill="#BC8316"/>
<path transform="translate(627,365)" d="m0 0 2 1-1 2-8 2-4-1 2-2z" fill="#06784D"/>
<path transform="translate(506,1421)" d="m0 0 6 2 7 3v2l-5-1-8-4z" fill="#068353"/>
<path transform="translate(197,974)" d="m0 0 1 2h2v10l-2 3z" fill="#148B54"/>
<path transform="translate(1398,849)" d="m0 0v3l-9 10v-3l7-8z" fill="#BA8618"/>
<path transform="translate(295,117)" d="m0 0h6l9 4-1 2-9-3-5-2z" fill="#3CC972"/>
<path transform="translate(205,1034)" d="m0 0h3l1 10-2 2z" fill="#128752"/>
<path transform="translate(639,938)" d="m0 0h5v2l-3 2-10-1v-1z" fill="#E3C91C"/>
<path transform="translate(489,860)" d="m0 0h2l-2 14-2-4 1-9z" fill="#108451"/>
<path transform="translate(1286,830)" d="m0 0h2l2 11v4h-2l-2-11z" fill="#BDEF4E"/>
<path transform="translate(512,791)" d="m0 0 1 3-4 9-2-1 1-5z" fill="#02734B"/>
<path transform="translate(765,642)" d="m0 0h10l1 3h-7l-4-2z" fill="#0F9A5F"/>
<path transform="translate(427,214)" d="m0 0 4 5 5 7h-3l-6-8z" fill="#1CB36C"/>
<path transform="translate(515,1018)" d="m0 0h4v2l-4 2h-5-3l4-3z" fill="#E8B915"/>
<path transform="translate(535,1014)" d="m0 0m-3 1 5 1-4 2-8 2 1-3z" fill="#D5BD24"/>
<path transform="translate(850,662)" d="m0 0 10 4 2 2-2 1-10-5z" fill="#19A663"/>
<path transform="translate(1399,621)" d="m0 0v3l-2 4h-2l-1 6-2 1 3-10z" fill="#C6ED49"/>
<path transform="translate(1021,424)" d="m0 0 11 6-3 2-9-5z" fill="#179B5E"/>
</svg>
