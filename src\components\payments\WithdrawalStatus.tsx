import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { RefreshCw, CheckCircle, Clock, XCircle, AlertCircle } from "lucide-react";
import { paystackService } from "@/services/paystack";

interface WithdrawalStatusProps {
  reference: string;
  onStatusUpdate?: (status: string) => void;
}

export function WithdrawalStatus({ reference, onStatusUpdate }: WithdrawalStatusProps) {
  const [status, setStatus] = useState<string>('processing');
  const [loading, setLoading] = useState(false);
  const [withdrawalData, setWithdrawalData] = useState<any>(null);

  const checkStatus = async () => {
    try {
      setLoading(true);
      const response = await paystackService.checkWithdrawalStatus(reference);
      
      if (response.success || response.status) {
        const data = response.data;
        setStatus(data.status);
        setWithdrawalData(data);
        
        if (onStatusUpdate) {
          onStatusUpdate(data.status);
        }
        
        // Update local transaction record
        const transactions = JSON.parse(localStorage.getItem('user_transactions') || '[]');
        const updatedTransactions = transactions.map((txn: any) => 
          txn.reference === reference 
            ? { ...txn, status: data.status }
            : txn
        );
        localStorage.setItem('user_transactions', JSON.stringify(updatedTransactions));
        
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      console.error('Status check error:', error);
      toast.error('Failed to check withdrawal status');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Auto-check status on mount
    checkStatus();
    
    // Set up interval to check status every 30 seconds if still processing
    const interval = setInterval(() => {
      if (status === 'processing' || status === 'pending') {
        checkStatus();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [reference, status]);

  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'processing':
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusVariant = () => {
    switch (status) {
      case 'success':
        return 'default';
      case 'failed':
        return 'destructive';
      case 'processing':
      case 'pending':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'success':
        return 'Completed';
      case 'failed':
        return 'Failed';
      case 'processing':
        return 'Processing';
      case 'pending':
        return 'Pending';
      default:
        return 'Unknown';
    }
  };

  return (
    <Card className="border-0 rounded-none">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <span className="text-sm font-medium">Withdrawal Status</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={checkStatus}
            disabled={loading}
            className="h-8 w-8 p-0"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Status:</span>
          <Badge variant={getStatusVariant()} className="flex items-center gap-1">
            {getStatusIcon()}
            {getStatusText()}
          </Badge>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Reference:</span>
          <span className="text-sm font-mono">{reference}</span>
        </div>
        
        {withdrawalData && (
          <>
            {withdrawalData.amount && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Amount:</span>
                <span className="text-sm font-medium">₦{(withdrawalData.amount / 100).toLocaleString()}</span>
              </div>
            )}
            
            {withdrawalData.recipient && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Recipient:</span>
                <span className="text-sm">{withdrawalData.recipient.name}</span>
              </div>
            )}
            
            {withdrawalData.createdAt && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Created:</span>
                <span className="text-sm">{new Date(withdrawalData.createdAt).toLocaleDateString()}</span>
              </div>
            )}
            
            {withdrawalData.transferred_at && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Completed:</span>
                <span className="text-sm">{new Date(withdrawalData.transferred_at).toLocaleDateString()}</span>
              </div>
            )}
            
            {withdrawalData.failure_reason && (
              <div className="space-y-1">
                <span className="text-sm text-muted-foreground">Failure Reason:</span>
                <p className="text-sm text-red-600 bg-red-50 p-2 rounded">{withdrawalData.failure_reason}</p>
              </div>
            )}
          </>
        )}
        
        {(status === 'processing' || status === 'pending') && (
          <div className="text-xs text-muted-foreground bg-blue-50 p-2 rounded">
            💡 Your withdrawal is being processed. This usually takes a few minutes to complete.
          </div>
        )}
        
        {status === 'success' && (
          <div className="text-xs text-green-600 bg-green-50 p-2 rounded">
            ✅ Your withdrawal has been completed successfully!
          </div>
        )}
        
        {status === 'failed' && (
          <div className="text-xs text-red-600 bg-red-50 p-2 rounded">
            ❌ Your withdrawal failed. Please contact support if you need assistance.
          </div>
        )}
      </CardContent>
    </Card>
  );
}