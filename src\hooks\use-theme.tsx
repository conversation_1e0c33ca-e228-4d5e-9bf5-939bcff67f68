
import React, { useState, useEffect, useCallback, useMemo, createContext, useContext, ReactNode } from 'react';

type Theme = 'light' | 'dark';

interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
}

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ 
  children, 
  defaultTheme = 'light', 
  storageKey = 'better-interest-theme' 
}: ThemeProviderProps) {
  console.log('ThemeProvider rendering');
  
  // Initialize with default theme - defensive initialization
  const [theme, setTheme] = useState<Theme>(() => {
    // Only access localStorage if we're in browser environment
    if (typeof window === 'undefined') return defaultTheme;
    
    try {
      const saved = localStorage.getItem(storageKey);
      if (saved === 'light' || saved === 'dark') {
        return saved;
      }
      
      // Check system preference
      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      return systemPrefersDark ? 'dark' : 'light';
    } catch (error) {
      console.warn('Failed to load theme preference:', error);
      return defaultTheme;
    }
  });
  
  // Apply theme to document with defensive checks
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const root = document.documentElement;
    if (root) {
      root.classList.remove('light', 'dark');
      root.classList.add(theme);
    }
    
    try {
      localStorage.setItem(storageKey, theme);
    } catch (error) {
      console.warn('Failed to save theme preference:', error);
    }
  }, [theme, storageKey]);

  const toggleTheme = useCallback(() => {
    setTheme(prevTheme => prevTheme === 'light' ? 'dark' : 'light');
  }, []);

  const value = useMemo(() => ({
    theme,
    toggleTheme
  }), [theme, toggleTheme]);

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    // Add defensive fallback instead of throwing error immediately
    console.warn('useTheme must be used within a ThemeProvider');
    return {
      theme: 'light' as Theme,
      toggleTheme: () => console.warn('ThemeProvider not available')
    };
  }
  return context;
}
