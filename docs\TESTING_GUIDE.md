# Testing Guide for Better Interest API

## Overview
This guide provides comprehensive testing strategies for the Better Interest application API endpoints.

## Testing Strategy

### 1. Unit Testing
Test individual functions and methods in isolation.

### 2. Integration Testing
Test API endpoints with database and external services.

### 3. End-to-End Testing
Test complete user workflows from frontend to backend.

### 4. Performance Testing
Test API performance under load.

## Test Environment Setup

### Prerequisites
```bash
# Install testing dependencies
npm install --save-dev jest supertest mongodb-memory-server

# Create test database
MONGO_URI_TEST=mongodb://localhost:27017/betterinterest_test
```

### Test Configuration
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  testMatch: ['**/__tests__/**/*.test.js'],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/index.js',
    '!src/config/**'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};
```

## API Testing Examples

### Authentication Tests
```javascript
// tests/auth.test.js
const request = require('supertest');
const app = require('../src/app');

describe('Authentication', () => {
  describe('POST /api/v1/auth/register', () => {
    it('should register a new user', async () => {
      const userData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'password123',
        phone: '+2348012345678'
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.token).toBeDefined();
    });

    it('should not register user with invalid email', async () => {
      const userData = {
        firstName: 'John',
        lastName: 'Doe',
        email: 'invalid-email',
        password: 'password123',
        phone: '+2348012345678'
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('email');
    });
  });

  describe('POST /api/v1/auth/login', () => {
    it('should login with valid credentials', async () => {
      // First register a user
      const userData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'password123',
        phone: '+2348012345678'
      };

      await request(app)
        .post('/api/v1/auth/register')
        .send(userData);

      // Then login
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          email: userData.email,
          password: userData.password
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.token).toBeDefined();
    });
  });
});
```

### Savings Plans Tests
```javascript
// tests/savings-plans.test.js
describe('Savings Plans', () => {
  let authToken;
  let userId;

  beforeEach(async () => {
    // Register and login user
    const userData = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      password: 'password123',
      phone: '+2348012345678'
    };

    const authResponse = await request(app)
      .post('/api/v1/auth/register')
      .send(userData);

    authToken = authResponse.body.data.token;
    userId = authResponse.body.data.user.id;
  });

  describe('GET /api/v1/savings-plans', () => {
    it('should return available savings plans', async () => {
      const response = await request(app)
        .get('/api/v1/savings-plans')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });

  describe('POST /api/v1/savings-plans', () => {
    it('should create a new savings plan', async () => {
      const planData = {
        planId: 'plan_123',
        targetAmount: 100000,
        duration: 90,
        frequency: 'weekly'
      };

      const response = await request(app)
        .post('/api/v1/savings-plans')
        .set('Authorization', `Bearer ${authToken}`)
        .send(planData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.targetAmount).toBe(planData.targetAmount);
    });
  });
});
```

### Fixed Deposits Tests
```javascript
// tests/fixed-deposits.test.js
describe('Fixed Deposits', () => {
  let authToken;

  beforeEach(async () => {
    // Setup authenticated user
    const authResponse = await setupAuthenticatedUser();
    authToken = authResponse.token;
  });

  describe('POST /api/v1/fixed-deposits', () => {
    it('should create a fixed deposit', async () => {
      const depositData = {
        amount: 50000,
        duration: 90,
        autoRenewal: false
      };

      const response = await request(app)
        .post('/api/v1/fixed-deposits')
        .set('Authorization', `Bearer ${authToken}`)
        .send(depositData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.amount).toBe(depositData.amount);
      expect(response.body.data.status).toBe('active');
    });

    it('should not create deposit with insufficient balance', async () => {
      const depositData = {
        amount: 1000000, // More than user balance
        duration: 90,
        autoRenewal: false
      };

      const response = await request(app)
        .post('/api/v1/fixed-deposits')
        .set('Authorization', `Bearer ${authToken}`)
        .send(depositData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('insufficient');
    });
  });
});
```

## Error Handling Tests

### Test Error Scenarios
```javascript
describe('Error Handling', () => {
  it('should handle invalid authentication token', async () => {
    const response = await request(app)
      .get('/api/v1/user/profile')
      .set('Authorization', 'Bearer invalid_token')
      .expect(401);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('UNAUTHORIZED');
  });

  it('should handle missing required fields', async () => {
    const response = await request(app)
      .post('/api/v1/auth/register')
      .send({
        firstName: 'John'
        // Missing required fields
      })
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('VALIDATION_ERROR');
  });

  it('should handle database connection errors', async () => {
    // Mock database connection failure
    jest.spyOn(mongoose, 'connect').mockRejectedValue(new Error('DB Error'));

    const response = await request(app)
      .get('/api/v1/user/profile')
      .set('Authorization', `Bearer ${validToken}`)
      .expect(500);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('INTERNAL_ERROR');
  });
});
```

## Performance Testing

### Load Testing with Artillery
```yaml
# artillery-config.yml
config:
  target: 'http://localhost:5000'
  phases:
    - duration: 60
      arrivalRate: 10
    - duration: 120
      arrivalRate: 50
    - duration: 60
      arrivalRate: 100

scenarios:
  - name: "User Registration and Login"
    weight: 30
    flow:
      - post:
          url: "/api/v1/auth/register"
          json:
            firstName: "Test"
            lastName: "User"
            email: "test{{ $randomString() }}@example.com"
            password: "password123"
            phone: "+2348012345678"
      - post:
          url: "/api/v1/auth/login"
          json:
            email: "{{ email }}"
            password: "password123"

  - name: "Savings Operations"
    weight: 70
    flow:
      - post:
          url: "/api/v1/auth/login"
          json:
            email: "<EMAIL>"
            password: "password123"
          capture:
            - json: "$.data.token"
              as: "token"
      - get:
          url: "/api/v1/savings-plans"
          headers:
            Authorization: "Bearer {{ token }}"
      - post:
          url: "/api/v1/fixed-deposits"
          headers:
            Authorization: "Bearer {{ token }}"
          json:
            amount: 10000
            duration: 30
```

### Run Performance Tests
```bash
# Install Artillery
npm install -g artillery

# Run load test
artillery run artillery-config.yml

# Generate report
artillery run --output report.json artillery-config.yml
artillery report report.json
```

## Test Data Management

### Test Database Setup
```javascript
// tests/setup.js
const { MongoMemoryServer } = require('mongodb-memory-server');
const mongoose = require('mongoose');

let mongoServer;

beforeAll(async () => {
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();
  await mongoose.connect(mongoUri);
});

afterAll(async () => {
  await mongoose.disconnect();
  await mongoServer.stop();
});

afterEach(async () => {
  const collections = mongoose.connection.collections;
  for (const key in collections) {
    await collections[key].deleteMany({});
  }
});
```

### Test Fixtures
```javascript
// tests/fixtures/users.js
const bcrypt = require('bcrypt');

const createTestUser = async (overrides = {}) => {
  const defaultUser = {
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
    password: await bcrypt.hash('password123', 10),
    phone: '+2348012345678',
    isVerified: true,
    kycStatus: 'verified',
    balance: 100000
  };

  return { ...defaultUser, ...overrides };
};

module.exports = { createTestUser };
```

## Continuous Integration

### GitHub Actions Workflow
```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      mongodb:
        image: mongo:4.4
        ports:
          - 27017:27017
      redis:
        image: redis:6
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
      env:
        NODE_ENV: test
        MONGO_URI: mongodb://localhost:27017/betterinterest_test
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test_jwt_secret
    
    - name: Upload coverage
      uses: codecov/codecov-action@v2
```

## Test Coverage

### Coverage Requirements
- Minimum 80% code coverage
- 100% coverage for critical paths (authentication, payments)
- All error scenarios tested
- All API endpoints tested

### Generate Coverage Report
```bash
# Run tests with coverage
npm test -- --coverage

# Generate HTML report
npm test -- --coverage --coverageReporters=html

# View report
open coverage/lcov-report/index.html
```

## Testing Checklist

### Before Deployment
- [ ] All unit tests pass
- [ ] All integration tests pass
- [ ] API endpoints return correct status codes
- [ ] Error handling works correctly
- [ ] Authentication and authorization work
- [ ] Data validation works
- [ ] Performance tests pass
- [ ] Security tests pass
- [ ] Database operations work correctly
- [ ] External service integrations work
- [ ] File upload/download works
- [ ] Email and SMS notifications work
- [ ] Payment processing works
- [ ] Interest calculations are accurate
- [ ] Backup and recovery procedures tested
