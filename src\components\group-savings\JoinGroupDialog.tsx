import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { AlertTriangle, Users, Target, Calendar, Shield } from 'lucide-react';
import { GroupSavingsPlan, groupSavingsService } from '@/services/group-savings';
import { GroupSavingsDisclaimer } from '@/components/legal/GroupSavingsDisclaimer';
import { format } from 'date-fns';
import { toast } from 'sonner';

interface JoinGroupDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onJoinGroup?: (inviteCode: string) => Promise<void>;
  onGroupJoined?: (group: GroupSavingsPlan) => void;
  currentUserId?: string;
  currentUserName?: string;
  currentUserEmail?: string;
}

export const JoinGroupDialog: React.FC<JoinGroupDialogProps> = ({
  open,
  onOpenChange,
  onJoinGroup,
  onGroupJoined,
  currentUserId,
  currentUserName,
  currentUserEmail
}) => {
  const [loading, setLoading] = useState(false);
  const [inviteCode, setInviteCode] = useState('');
  const [showDisclaimer, setShowDisclaimer] = useState(false);

  const handleJoinGroup = async () => {
    if (!inviteCode.trim()) {
      toast.error('Please enter an invite code');
      return;
    }

    setShowDisclaimer(true);
  };

  const handleDisclaimerAccept = async () => {
    setShowDisclaimer(false);
    
    if (!onJoinGroup) {
      toast.error('Join function not available');
      return;
    }

    setLoading(true);
    try {
      await onJoinGroup(inviteCode.trim());
      setInviteCode('');
      onOpenChange(false);
    } catch (error: any) {
      console.error('Error joining group:', error);
      toast.error(error.message || 'Failed to join group');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setInviteCode('');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[400px] rounded-none shadow-[8px_8px_16px_rgba(0,0,0,0.2),-8px_-8px_16px_rgba(255,255,255,0.1)]">
        <DialogHeader>
          <DialogTitle>Join Group Savings</DialogTitle>
          <DialogDescription>
            Enter an invite code to join an existing group savings plan
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="inviteCode">Invite Code</Label>
            <div className="flex gap-2">
              <Input
                id="inviteCode"
                value={inviteCode}
                onChange={(e) => setInviteCode(e.target.value.toUpperCase())}
                placeholder="Enter 6-character code"
                maxLength={6}
                className="uppercase rounded-none shadow-[inset_2px_2px_4px_rgba(0,0,0,0.1),inset_-2px_-2px_4px_rgba(255,255,255,0.1)]"
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={loading}
            className="rounded-none"
          >
            Cancel
          </Button>
          <Button
            onClick={handleJoinGroup}
            disabled={loading || !inviteCode.trim()}
            className="bg-primary hover:bg-primary/90 rounded-none"
          >
            {loading ? 'Joining...' : 'Join Group'}
          </Button>
        </DialogFooter>
      </DialogContent>

      {/* Group Savings Disclaimer Modal */}
      <GroupSavingsDisclaimer
        open={showDisclaimer}
        onOpenChange={setShowDisclaimer}
        onAccept={handleDisclaimerAccept}
        groupName="the selected group"
      />
    </Dialog>
  );
};