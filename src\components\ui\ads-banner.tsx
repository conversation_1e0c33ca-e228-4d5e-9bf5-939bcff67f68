import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface AdsBannerProps {
  className?: string;
  isAdmin?: boolean;
  onEditAd?: (index: number) => void;
}

// Default ad content - can be managed by admin
const defaultAds = [
  {
    image: '/lovable-uploads/a95ab938-222b-48b2-a5dc-67971a2ae055.png',
    title: 'Secure Your Savings Today!',
    description: 'Premium banking solutions with the highest security standards',
    link: '/savings'
  },
  {
    image: '/lovable-uploads/26c20ac0-f786-4d30-9c1c-7927225b7b61.png',
    title: 'Premium Banking Solutions',
    description: 'Unlock exclusive benefits and higher interest rates',
    link: '/premium'
  }
];

export function AdsBanner({ className, isAdmin = false, onEditAd }: AdsBannerProps) {
  const [currentAdIndex, setCurrentAdIndex] = useState(0);
  const [ads, setAds] = useState(defaultAds);

  useEffect(() => {
    // Load ads from localStorage if available (admin updates)
    const savedAds = localStorage.getItem('admin_ads');
    if (savedAds) {
      try {
        const parsedAds = JSON.parse(savedAds);
        if (parsedAds.length > 0) {
          setAds(parsedAds);
        }
      } catch (error) {
        console.error('Error loading ads:', error);
      }
    }
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentAdIndex((prev) => (prev + 1) % ads.length);
    }, 4000); // Change ad every 4 seconds

    return () => clearInterval(interval);
  }, [ads.length]);

  const currentAd = ads[currentAdIndex];

  return (
    <div className={cn(
      "relative overflow-hidden rounded-none border-2 border-orange-400 bg-gradient-to-r from-yellow-50 via-orange-50 to-yellow-50 dark:from-yellow-900/10 dark:via-orange-900/10 dark:to-yellow-900/10",
      "shadow-[0_0_20px_rgba(251,146,60,0.3)] hover:shadow-[0_0_30px_rgba(251,146,60,0.5)]",
      "transition-all duration-500 cursor-pointer group",
      className
    )}>
      {/* Static overlay - no flashing */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-yellow-200/10 to-transparent opacity-50"></div>
      
      {/* Main content */}
      <div className="relative z-10 flex items-center gap-4 p-4">
        {/* Image container with 3D effect */}
        <div className="flex-shrink-0 w-16 h-16 rounded-none overflow-hidden bg-white/20 shadow-[inset_2px_2px_4px_rgba(255,255,255,0.3),inset_-2px_-2px_4px_rgba(0,0,0,0.2)] border border-orange-300">
          <img 
            src={currentAd.image}
            alt="Advertisement"
            className="w-full h-full object-contain transition-all duration-500 group-hover:scale-110"
          />
        </div>
        
        {/* Content */}
        <div className="flex-1 min-w-0">
          <h3 className="font-bebas text-lg font-bold text-orange-700 dark:text-orange-300 tracking-wide uppercase mb-1">
            {currentAd.title}
          </h3>
          <p className="text-sm text-orange-600 dark:text-orange-400 font-medium line-clamp-2">
            {currentAd.description}
          </p>
        </div>
        
        {/* Right indicator */}
        <div className="flex-shrink-0 flex flex-col items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-orange-500 shadow-[0_0_10px_rgba(251,146,60,0.8)]"></div>
          
          {/* Ad indicators */}
          <div className="flex gap-1">
            {ads.map((_, index) => (
              <div 
                key={index}
                className={cn(
                  "w-1.5 h-1.5 rounded-full transition-all duration-300",
                  index === currentAdIndex 
                    ? "bg-orange-500 shadow-[0_0_8px_rgba(251,146,60,0.8)]" 
                    : "bg-orange-300/50"
                )}
              />
            ))}
          </div>
        </div>
        
        {/* Admin edit button */}
        {isAdmin && onEditAd && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onEditAd(currentAdIndex);
            }}
            className="absolute top-2 right-2 p-1 bg-black/20 text-white rounded-none text-xs hover:bg-black/40 transition-colors"
          >
            Edit
          </button>
        )}
      </div>
      
      {/* Static border effect */}
      <div className="absolute inset-0 border-2 border-transparent rounded-none">
        <div className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-400"></div>
        <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-400"></div>
        <div className="absolute left-0 top-0 w-0.5 h-full bg-gradient-to-b from-yellow-400 via-orange-400 to-yellow-400"></div>
        <div className="absolute right-0 top-0 w-0.5 h-full bg-gradient-to-b from-yellow-400 via-orange-400 to-yellow-400"></div>
      </div>
    </div>
  );
}