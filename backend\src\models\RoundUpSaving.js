const mongoose = require('mongoose');

const roundUpSavingSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  isEnabled: {
    type: Boolean,
    default: true
  },
  multiplier: {
    type: Number,
    default: 1,
    min: 1,
    max: 5
  },
  goalAmount: {
    type: Number,
    default: 50000,
    min: 1000
  },
  totalSaved: {
    type: Number,
    default: 0
  },
  totalTransactions: {
    type: Number,
    default: 0
  },
  averageRoundUp: {
    type: Number,
    default: 0
  },
  lastRoundUpDate: {
    type: Date
  },
  roundUpTransactions: [{
    transactionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Transaction',
      required: true
    },
    originalAmount: {
      type: Number,
      required: true
    },
    roundUpAmount: {
      type: Number,
      required: true
    },
    description: String,
    date: {
      type: Date,
      default: Date.now
    }
  }],
  settings: {
    autoInvest: {
      type: Boolean,
      default: false
    },
    investmentThreshold: {
      type: Number,
      default: 10000
    },
    preferredInvestmentType: {
      type: String,
      enum: ['mutual_fund', 'bond', 'stock'],
      default: 'mutual_fund'
    }
  },
  statistics: {
    thisMonth: {
      saved: { type: Number, default: 0 },
      transactions: { type: Number, default: 0 }
    },
    thisYear: {
      saved: { type: Number, default: 0 },
      transactions: { type: Number, default: 0 }
    },
    allTime: {
      saved: { type: Number, default: 0 },
      transactions: { type: Number, default: 0 }
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for progress percentage
roundUpSavingSchema.virtual('progressPercentage').get(function() {
  return this.goalAmount > 0 ? (this.totalSaved / this.goalAmount) * 100 : 0;
});

// Virtual for remaining amount
roundUpSavingSchema.virtual('remainingAmount').get(function() {
  return Math.max(0, this.goalAmount - this.totalSaved);
});

// Indexes
roundUpSavingSchema.index({ userId: 1 }, { unique: true });
roundUpSavingSchema.index({ isEnabled: 1, userId: 1 });

// Add round-up transaction
roundUpSavingSchema.methods.addRoundUp = function(transactionData) {
  this.roundUpTransactions.push(transactionData);
  this.totalSaved += transactionData.roundUpAmount;
  this.totalTransactions += 1;
  this.averageRoundUp = this.totalSaved / this.totalTransactions;
  this.lastRoundUpDate = new Date();
  
  // Update monthly and yearly stats
  const now = new Date();
  const currentMonth = now.getMonth();
  const currentYear = now.getFullYear();
  
  this.statistics.thisMonth.saved += transactionData.roundUpAmount;
  this.statistics.thisMonth.transactions += 1;
  this.statistics.thisYear.saved += transactionData.roundUpAmount;
  this.statistics.thisYear.transactions += 1;
  this.statistics.allTime.saved = this.totalSaved;
  this.statistics.allTime.transactions = this.totalTransactions;
  
  return this.save();
};

// Reset monthly stats (called by cron job)
roundUpSavingSchema.statics.resetMonthlyStats = function() {
  return this.updateMany({}, {
    $set: {
      'statistics.thisMonth.saved': 0,
      'statistics.thisMonth.transactions': 0
    }
  });
};

// Reset yearly stats (called by cron job)
roundUpSavingSchema.statics.resetYearlyStats = function() {
  return this.updateMany({}, {
    $set: {
      'statistics.thisYear.saved': 0,
      'statistics.thisYear.transactions': 0
    }
  });
};

module.exports = mongoose.model('RoundUpSaving', roundUpSavingSchema);