import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Users, 
  Calendar, 
  Target, 
  Trophy, 
  Clock, 
  AlertCircle, 
  Check, 
  Star,
  TrendingUp,
  Gift
} from 'lucide-react';
import { format, addDays, isAfter, isBefore } from 'date-fns';

interface GroupMember {
  id: string;
  name: string;
  avatar?: string;
  contributionStatus: 'paid' | 'pending' | 'overdue';
  payoutPosition: number;
  reputationScore: number;
  joinedDate: Date;
  totalContributions: number;
}

interface RotationalGroup {
  id: string;
  name: string;
  description: string;
  contributionAmount: number;
  frequency: 'weekly' | 'monthly';
  totalMembers: number;
  currentPayout: number;
  nextPayoutDate: Date;
  members: GroupMember[];
  currentCycle: number;
  totalCycles: number;
  status: 'active' | 'completed' | 'paused';
}

interface RotationalSavingsProps {
  group: RotationalGroup;
  currentUserId?: string;
  onContribute?: (amount: number) => void;
  onSendReminder?: (memberId: string) => void;
}

export const RotationalSavings: React.FC<RotationalSavingsProps> = ({
  group,
  currentUserId,
  onContribute,
  onSendReminder
}) => {
  const [timeToNextPayout, setTimeToNextPayout] = useState('');

  useEffect(() => {
    const updateCountdown = () => {
      const now = new Date();
      const timeDiff = group.nextPayoutDate.getTime() - now.getTime();
      
      if (timeDiff > 0) {
        const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
        
        if (days > 0) {
          setTimeToNextPayout(`${days}d ${hours}h`);
        } else if (hours > 0) {
          setTimeToNextPayout(`${hours}h ${minutes}m`);
        } else {
          setTimeToNextPayout(`${minutes}m`);
        }
      } else {
        setTimeToNextPayout('Payout due!');
      }
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [group.nextPayoutDate]);

  const getCurrentPayoutMember = () => {
    return group.members.find(member => member.payoutPosition === group.currentPayout);
  };

  const getNextPayoutMember = () => {
    const nextPosition = group.currentPayout + 1;
    return group.members.find(member => member.payoutPosition === nextPosition);
  };

  const getPendingMembers = () => {
    return group.members.filter(member => member.contributionStatus === 'pending');
  };

  const getOverdueMembers = () => {
    return group.members.filter(member => member.contributionStatus === 'overdue');
  };

  const getReputationBadge = (score: number) => {
    if (score >= 90) return { text: 'Platinum', color: 'bg-purple-500', icon: '💎' };
    if (score >= 80) return { text: 'Gold', color: 'bg-yellow-500', icon: '🏆' };
    if (score >= 70) return { text: 'Silver', color: 'bg-gray-400', icon: '🥈' };
    if (score >= 60) return { text: 'Bronze', color: 'bg-orange-500', icon: '🥉' };
    return { text: 'New', color: 'bg-blue-500', icon: '⭐' };
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'text-green-600';
      case 'pending': return 'text-yellow-600';
      case 'overdue': return 'text-red-600';
      default: return 'text-muted-foreground';
    }
  };

  const currentPayoutMember = getCurrentPayoutMember();
  const nextPayoutMember = getNextPayoutMember();
  const pendingMembers = getPendingMembers();
  const overdueMembers = getOverdueMembers();

  const totalExpectedAmount = group.contributionAmount * group.totalMembers;
  const paidMembers = group.members.filter(m => m.contributionStatus === 'paid').length;
  const collectionProgress = (paidMembers / group.totalMembers) * 100;

  return (
    <div className="space-y-6">
      {/* Main Group Info */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Gift className="h-5 w-5 text-primary" />
                {group.name}
              </CardTitle>
              <CardDescription>{group.description}</CardDescription>
            </div>
            <Badge className={`${
              group.status === 'active' ? 'bg-green-500' : 
              group.status === 'completed' ? 'bg-blue-500' : 'bg-yellow-500'
            }`}>
              {group.status.charAt(0).toUpperCase() + group.status.slice(1)}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-muted-foreground" />
              <span>₦{group.contributionAmount.toLocaleString()}</span>
            </div>
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span>{group.totalMembers} members</span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>{group.frequency}</span>
            </div>
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
              <span>Cycle {group.currentCycle}/{group.totalCycles}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Payout Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5 text-yellow-500" />
            Current Payout Round
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {currentPayoutMember && (
            <div className="flex items-center gap-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
              <Avatar className="h-12 w-12">
                <AvatarImage src={currentPayoutMember.avatar} />
                <AvatarFallback>
                  {currentPayoutMember.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <h4 className="font-semibold">{currentPayoutMember.name}</h4>
                  <Badge className="text-xs">
                    {getReputationBadge(currentPayoutMember.reputationScore).icon}
                    {getReputationBadge(currentPayoutMember.reputationScore).text}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  Receiving ₦{totalExpectedAmount.toLocaleString()} this round
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium">Next payout in</p>
                <p className="text-lg font-bold text-yellow-600">{timeToNextPayout}</p>
              </div>
            </div>
          )}

          {/* Collection Progress */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Contributions collected</span>
              <span>{paidMembers}/{group.totalMembers} members</span>
            </div>
            <Progress value={collectionProgress} className="h-3" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>₦{(paidMembers * group.contributionAmount).toLocaleString()}</span>
              <span>₦{totalExpectedAmount.toLocaleString()}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Next Payout Preview */}
      {nextPayoutMember && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-blue-500" />
              Next Payout Round
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <Avatar className="h-10 w-10">
                <AvatarImage src={nextPayoutMember.avatar} />
                <AvatarFallback>
                  {nextPayoutMember.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <h4 className="font-medium">{nextPayoutMember.name}</h4>
                  <Badge variant="outline" className="text-xs">
                    {getReputationBadge(nextPayoutMember.reputationScore).icon}
                    {getReputationBadge(nextPayoutMember.reputationScore).text}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  Next to receive payout
                </p>
              </div>
              <p className="text-sm text-blue-600 font-medium">
                {format(addDays(group.nextPayoutDate, group.frequency === 'weekly' ? 7 : 30), 'MMM dd')}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Members Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Members Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {group.members
              .sort((a, b) => a.payoutPosition - b.payoutPosition)
              .map((member) => {
                const reputation = getReputationBadge(member.reputationScore);
                return (
                  <div
                    key={member.id}
                    className="flex items-center gap-4 p-3 rounded-lg border"
                  >
                    <div className="relative">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={member.avatar} />
                        <AvatarFallback>
                          {member.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      {member.payoutPosition === group.currentPayout && (
                        <div className="absolute -top-1 -right-1 h-4 w-4 bg-yellow-500 rounded-full flex items-center justify-center">
                          <Trophy className="h-2 w-2 text-white" />
                        </div>
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{member.name}</h4>
                        <Badge 
                          className={`text-xs ${reputation.color} text-white`}
                          title={`Reputation Score: ${member.reputationScore}%`}
                        >
                          {reputation.icon} {reputation.text}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>Position: #{member.payoutPosition}</span>
                        <span>Contributions: {member.totalContributions}</span>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className="text-right">
                        <div className={`flex items-center gap-1 text-sm font-medium ${getStatusColor(member.contributionStatus)}`}>
                          {member.contributionStatus === 'paid' && <Check className="h-4 w-4" />}
                          {member.contributionStatus === 'overdue' && <AlertCircle className="h-4 w-4" />}
                          <span className="capitalize">{member.contributionStatus}</span>
                        </div>
                      </div>

                      {member.contributionStatus !== 'paid' && onSendReminder && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onSendReminder(member.id)}
                          className="text-xs"
                        >
                          Remind
                        </Button>
                      )}
                    </div>
                  </div>
                );
              })}
          </div>
        </CardContent>
      </Card>

      {/* Contribution Action */}
      {onContribute && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Your Contribution</h4>
                <p className="text-sm text-muted-foreground">
                  ₦{group.contributionAmount.toLocaleString()} due for this round
                </p>
              </div>
              <Button 
                onClick={() => onContribute(group.contributionAmount)}
                className="bg-primary hover:bg-primary/90"
              >
                Contribute Now
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Alerts for overdue members */}
      {overdueMembers.length > 0 && (
        <Card className="border-red-200 bg-red-50 dark:bg-red-900/20">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-red-800 dark:text-red-200">
                  Overdue Contributions
                </h4>
                <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                  {overdueMembers.length} member(s) have overdue contributions. 
                  Payout may be delayed until all contributions are received.
                </p>
                <div className="mt-2 space-y-1">
                  {overdueMembers.map(member => (
                    <p key={member.id} className="text-xs text-red-600 dark:text-red-400">
                      • {member.name}
                    </p>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};