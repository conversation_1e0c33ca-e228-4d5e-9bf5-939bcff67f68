const mongoose = require('mongoose');

const FixedDepositSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  amount: {
    type: Number,
    required: true,
    min: [10000, 'Minimum fixed deposit amount is ₦10,000']
  },
  duration: {
    type: Number,
    required: true,
    min: [30, 'Minimum duration is 30 days'],
    max: [365, 'Maximum duration is 365 days']
  },
  interestRate: {
    type: Number,
    required: true,
    min: [0, 'Interest rate cannot be negative'],
    max: [50, 'Interest rate cannot exceed 50%']
  },
  startDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  maturityDate: {
    type: Date,
    required: true
  },
  accruedInterest: {
    type: Number,
    default: 0,
    min: [0, 'Accrued interest cannot be negative']
  },
  status: {
    type: String,
    enum: ['active', 'matured', 'broken', 'cancelled'],
    default: 'active',
    index: true
  },
  penaltyRate: {
    type: Number,
    default: 25, // 25% penalty on accrued interest for early termination
    min: [0, 'Penalty rate cannot be negative'],
    max: [100, 'Penalty rate cannot exceed 100%']
  },
  autoRenewal: {
    type: Boolean,
    default: false
  },
  compoundingFrequency: {
    type: String,
    enum: ['daily', 'monthly', 'quarterly', 'annually'],
    default: 'daily'
  },
  breakageDate: {
    type: Date
  },
  breakagePenalty: {
    type: Number,
    default: 0
  },
  finalAmount: {
    type: Number // Amount credited on maturity/breakage
  },
  paymentReference: {
    type: String,
    index: true
  },
  metadata: {
    sourceAccount: String,
    ipAddress: String,
    userAgent: String,
    notes: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for projected maturity amount
FixedDepositSchema.virtual('projectedMaturityAmount').get(function() {
  const principal = this.amount;
  const rate = this.interestRate / 100;
  const days = this.duration;
  
  // Calculate compound interest (daily compounding)
  const dailyRate = rate / 365;
  return principal * Math.pow(1 + dailyRate, days);
});

// Virtual for current value
FixedDepositSchema.virtual('currentValue').get(function() {
  if (this.status === 'matured' || this.status === 'broken') {
    return this.finalAmount || this.amount;
  }
  
  const daysElapsed = Math.floor((Date.now() - this.startDate) / (1000 * 60 * 60 * 24));
  const principal = this.amount;
  const rate = this.interestRate / 100;
  const dailyRate = rate / 365;
  
  return principal * Math.pow(1 + dailyRate, Math.min(daysElapsed, this.duration));
});

// Virtual for days remaining
FixedDepositSchema.virtual('daysRemaining').get(function() {
  if (this.status !== 'active') return 0;
  
  const now = new Date();
  const maturity = new Date(this.maturityDate);
  const diffTime = maturity - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return Math.max(0, diffDays);
});

// Pre-save middleware to calculate maturity date
FixedDepositSchema.pre('save', function(next) {
  if (this.isNew || this.isModified('startDate') || this.isModified('duration')) {
    const maturityDate = new Date(this.startDate);
    maturityDate.setDate(maturityDate.getDate() + this.duration);
    this.maturityDate = maturityDate;
  }
  next();
});

// Index for efficient queries
FixedDepositSchema.index({ userId: 1, status: 1 });
FixedDepositSchema.index({ maturityDate: 1, status: 1 });
FixedDepositSchema.index({ createdAt: -1 });

// Static methods
FixedDepositSchema.statics.getActiveDeposits = function() {
  return this.find({ status: 'active' });
};

FixedDepositSchema.statics.getMaturedDeposits = function() {
  return this.find({ 
    status: 'active',
    maturityDate: { $lte: new Date() }
  });
};

FixedDepositSchema.statics.getUserDeposits = function(userId, status = null) {
  const query = { userId };
  if (status) query.status = status;
  return this.find(query).sort({ createdAt: -1 });
};

// Instance methods
FixedDepositSchema.methods.calculateCurrentInterest = function() {
  const daysElapsed = Math.floor((Date.now() - this.startDate) / (1000 * 60 * 60 * 24));
  const principal = this.amount;
  const rate = this.interestRate / 100;
  const dailyRate = rate / 365;
  
  const currentAmount = principal * Math.pow(1 + dailyRate, Math.min(daysElapsed, this.duration));
  return currentAmount - principal;
};

FixedDepositSchema.methods.breakDeposit = function() {
  if (this.status !== 'active') {
    throw new Error('Only active deposits can be broken');
  }
  
  const currentInterest = this.calculateCurrentInterest();
  const penalty = (currentInterest * this.penaltyRate) / 100;
  const netInterest = Math.max(0, currentInterest - penalty);
  
  this.status = 'broken';
  this.breakageDate = new Date();
  this.breakagePenalty = penalty;
  this.finalAmount = this.amount + netInterest;
  
  return this.save();
};

FixedDepositSchema.methods.mature = function() {
  if (this.status !== 'active') {
    throw new Error('Only active deposits can be matured');
  }
  
  const maturityInterest = this.calculateCurrentInterest();
  
  this.status = 'matured';
  this.accruedInterest = maturityInterest;
  this.finalAmount = this.amount + maturityInterest;
  
  return this.save();
};

module.exports = mongoose.model('FixedDeposit', FixedDepositSchema);
