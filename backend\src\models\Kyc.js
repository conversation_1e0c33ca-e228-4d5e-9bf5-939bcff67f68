const mongoose = require('mongoose');

const kycSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  status: {
    type: String,
    enum: ['pending', 'under_review', 'approved', 'rejected', 'expired'],
    default: 'pending'
  },
  level: {
    type: String,
    enum: ['tier1', 'tier2', 'tier3'],
    default: 'tier1'
  },
  personalInfo: {
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    middleName: String,
    dateOfBirth: { type: Date, required: true },
    gender: { type: String, enum: ['male', 'female', 'other'] },
    nationality: { type: String, default: 'Nigerian' },
    placeOfBirth: String,
    maritalStatus: { type: String, enum: ['single', 'married', 'divorced', 'widowed'] }
  },
  contactInfo: {
    email: { type: String, required: true },
    phone: { type: String, required: true },
    alternatePhone: String,
    address: {
      street: { type: String, required: true },
      city: { type: String, required: true },
      state: { type: String, required: true },
      country: { type: String, default: 'Nigeria' },
      postalCode: String
    }
  },
  identificationDocuments: {
    primary: {
      type: { type: String, enum: ['nin', 'bvn', 'voters_card', 'drivers_license', 'passport'] },
      number: String,
      issueDate: Date,
      expiryDate: Date,
      documentUrl: String,
      verified: { type: Boolean, default: false }
    },
    secondary: {
      type: { type: String, enum: ['utility_bill', 'bank_statement', 'employment_letter'] },
      documentUrl: String,
      verified: { type: Boolean, default: false }
    },
    selfie: {
      documentUrl: String,
      verified: { type: Boolean, default: false }
    }
  },
  employmentInfo: {
    employmentStatus: { 
      type: String, 
      enum: ['employed', 'self_employed', 'unemployed', 'student', 'retired'] 
    },
    employer: String,
    position: String,
    monthlyIncome: Number,
    sourceOfIncome: String,
    workAddress: String
  },
  bankDetails: {
    accountNumber: String,
    bankName: String,
    bankCode: String,
    accountName: String,
    verified: { type: Boolean, default: false }
  },
  nextOfKin: {
    fullName: String,
    relationship: String,
    phone: String,
    email: String,
    address: String
  },
  verification: {
    bvnVerification: {
      verified: { type: Boolean, default: false },
      verifiedAt: Date,
      details: Object
    },
    ninVerification: {
      verified: { type: Boolean, default: false },
      verifiedAt: Date,
      details: Object
    },
    faceMatch: {
      verified: { type: Boolean, default: false },
      verifiedAt: Date,
      confidence: Number
    },
    addressVerification: {
      verified: { type: Boolean, default: false },
      verifiedAt: Date,
      method: String
    }
  },
  riskAssessment: {
    riskLevel: { type: String, enum: ['low', 'medium', 'high'], default: 'low' },
    score: { type: Number, default: 0 },
    factors: [String],
    lastAssessment: Date
  },
  compliance: {
    pep: { type: Boolean, default: false }, // Politically Exposed Person
    sanctions: { type: Boolean, default: false },
    amlCheck: { type: Boolean, default: false },
    lastCheck: Date
  },
  reviewHistory: [{
    reviewedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    previousStatus: String,
    newStatus: String,
    comments: String,
    reviewDate: {
      type: Date,
      default: Date.now
    },
    documents: [String]
  }],
  submittedAt: {
    type: Date,
    default: Date.now
  },
  approvedAt: Date,
  rejectedAt: Date,
  expiryDate: Date,
  rejectionReason: String,
  notes: String
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for completion percentage
kycSchema.virtual('completionPercentage').get(function() {
  let completed = 0;
  let total = 0;
  
  // Personal info (20%)
  total += 20;
  if (this.personalInfo.firstName && this.personalInfo.lastName && this.personalInfo.dateOfBirth) {
    completed += 20;
  }
  
  // Contact info (20%)
  total += 20;
  if (this.contactInfo.email && this.contactInfo.phone && this.contactInfo.address.street) {
    completed += 20;
  }
  
  // Primary ID (30%)
  total += 30;
  if (this.identificationDocuments.primary.type && this.identificationDocuments.primary.number) {
    completed += 30;
  }
  
  // Selfie (15%)
  total += 15;
  if (this.identificationDocuments.selfie.documentUrl) {
    completed += 15;
  }
  
  // Bank details (15%)
  total += 15;
  if (this.bankDetails.accountNumber && this.bankDetails.bankName) {
    completed += 15;
  }
  
  return total > 0 ? (completed / total) * 100 : 0;
});

// Indexes
kycSchema.index({ userId: 1 });
kycSchema.index({ status: 1, level: 1 });
kycSchema.index({ submittedAt: -1 });
kycSchema.index({ 'verification.bvnVerification.verified': 1 });
kycSchema.index({ 'riskAssessment.riskLevel': 1 });

// Add review method
kycSchema.methods.addReview = function(reviewerId, newStatus, comments = '') {
  this.reviewHistory.push({
    reviewedBy: reviewerId,
    previousStatus: this.status,
    newStatus: newStatus,
    comments: comments,
    reviewDate: new Date()
  });
  
  this.status = newStatus;
  
  if (newStatus === 'approved') {
    this.approvedAt = new Date();
    this.expiryDate = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // 1 year
  } else if (newStatus === 'rejected') {
    this.rejectedAt = new Date();
    this.rejectionReason = comments;
  }
  
  return this.save();
};

// Verify document method
kycSchema.methods.verifyDocument = function(documentType, verificationData = {}) {
  switch (documentType) {
    case 'bvn':
      this.verification.bvnVerification.verified = true;
      this.verification.bvnVerification.verifiedAt = new Date();
      this.verification.bvnVerification.details = verificationData;
      break;
    case 'nin':
      this.verification.ninVerification.verified = true;
      this.verification.ninVerification.verifiedAt = new Date();
      this.verification.ninVerification.details = verificationData;
      break;
    case 'selfie':
      this.identificationDocuments.selfie.verified = true;
      this.verification.faceMatch.verified = true;
      this.verification.faceMatch.verifiedAt = new Date();
      this.verification.faceMatch.confidence = verificationData.confidence || 0;
      break;
    case 'address':
      this.verification.addressVerification.verified = true;
      this.verification.addressVerification.verifiedAt = new Date();
      this.verification.addressVerification.method = verificationData.method || 'manual';
      break;
  }
  
  return this.save();
};

// Get KYC records pending review
kycSchema.statics.getPendingReview = function() {
  return this.find({
    status: { $in: ['pending', 'under_review'] }
  }).populate('userId', 'firstName lastName email phone');
};

// Check if KYC is expired
kycSchema.methods.isExpired = function() {
  return this.expiryDate && new Date() > this.expiryDate;
};

module.exports = mongoose.model('Kyc', kycSchema);