const mongoose = require('mongoose');

const investmentSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  productId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'InvestmentProduct',
    required: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  units: {
    type: Number,
    required: true,
    min: 0
  },
  unitPrice: {
    type: Number,
    required: true,
    min: 0
  },
  currentValue: {
    type: Number,
    default: 0
  },
  status: {
    type: String,
    enum: ['active', 'matured', 'liquidated', 'suspended'],
    default: 'active'
  },
  investmentDate: {
    type: Date,
    default: Date.now
  },
  maturityDate: {
    type: Date
  },
  liquidationDate: {
    type: Date
  },
  returns: {
    totalReturn: { type: Number, default: 0 },
    percentageReturn: { type: Number, default: 0 },
    dividends: { type: Number, default: 0 }
  },
  transactions: [{
    type: {
      type: String,
      enum: ['buy', 'sell', 'dividend', 'fee'],
      required: true
    },
    amount: {
      type: Number,
      required: true
    },
    units: {
      type: Number,
      default: 0
    },
    unitPrice: {
      type: Number,
      default: 0
    },
    description: String,
    date: {
      type: Date,
      default: Date.now
    },
    reference: String
  }],
  autoReinvest: {
    type: Boolean,
    default: false
  },
  riskProfile: {
    type: String,
    enum: ['conservative', 'moderate', 'aggressive'],
    default: 'moderate'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for profit/loss
investmentSchema.virtual('profitLoss').get(function() {
  return this.currentValue - this.amount;
});

// Virtual for percentage gain/loss
investmentSchema.virtual('percentageGain').get(function() {
  if (this.amount === 0) return 0;
  return ((this.currentValue - this.amount) / this.amount) * 100;
});

// Indexes
investmentSchema.index({ userId: 1, status: 1 });
investmentSchema.index({ productId: 1, status: 1 });
investmentSchema.index({ investmentDate: -1 });
investmentSchema.index({ maturityDate: 1 });

// Update current value based on latest unit price
investmentSchema.methods.updateCurrentValue = async function(newUnitPrice) {
  this.currentValue = this.units * newUnitPrice;
  this.returns.totalReturn = this.currentValue - this.amount;
  this.returns.percentageReturn = this.amount > 0 ? (this.returns.totalReturn / this.amount) * 100 : 0;
  return this.save();
};

// Add transaction
investmentSchema.methods.addTransaction = function(transactionData) {
  this.transactions.push(transactionData);
  return this.save();
};

module.exports = mongoose.model('Investment', investmentSchema);