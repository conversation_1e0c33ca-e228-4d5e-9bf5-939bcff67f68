import React, { useState, useEffect } from 'react';
import { Calculator, TrendingUp, Calendar, DollarSign, Percent } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface InterestCalculation {
  principal: number;
  rate: number;
  time: number;
  compound: 'monthly' | 'quarterly' | 'annually';
  finalAmount: number;
  totalInterest: number;
  monthlyBreakdown: Array<{
    month: number;
    principal: number;
    interest: number;
    total: number;
  }>;
}

export const InterestCalculator: React.FC = () => {
  const [principal, setPrincipal] = useState<number>(100000);
  const [rate, setRate] = useState<number>(12);
  const [time, setTime] = useState<number>(1);
  const [compound, setCompound] = useState<'monthly' | 'quarterly' | 'annually'>('monthly');
  const [calculation, setCalculation] = useState<InterestCalculation | null>(null);

  const calculateInterest = () => {
    if (!principal || !rate || !time) return;

    const r = rate / 100;
    let n = 1; // Compound frequency per year
    
    switch (compound) {
      case 'monthly':
        n = 12;
        break;
      case 'quarterly':
        n = 4;
        break;
      case 'annually':
        n = 1;
        break;
    }

    // Compound Interest Formula: A = P(1 + r/n)^(nt)
    const finalAmount = principal * Math.pow((1 + r / n), (n * time));
    const totalInterest = finalAmount - principal;

    // Monthly breakdown
    const monthlyBreakdown = [];
    const monthsTotal = time * 12;
    const monthlyRate = r / 12;

    for (let month = 1; month <= Math.min(monthsTotal, 24); month++) {
      const monthlyAmount = principal * Math.pow((1 + monthlyRate), month);
      const monthlyInterest = monthlyAmount - principal;
      
      monthlyBreakdown.push({
        month,
        principal,
        interest: monthlyInterest,
        total: monthlyAmount
      });
    }

    setCalculation({
      principal,
      rate,
      time,
      compound,
      finalAmount,
      totalInterest,
      monthlyBreakdown
    });
  };

  useEffect(() => {
    calculateInterest();
  }, [principal, rate, time, compound]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getROIPercentage = () => {
    if (!calculation) return 0;
    return ((calculation.totalInterest / calculation.principal) * 100);
  };

  return (
    <Card className="bg-gradient-to-br from-background to-muted/20 border-primary/20 shadow-lg">
      <CardHeader className="border-b border-primary/10">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Calculator className="h-5 w-5 text-primary" />
          Interest Calculator
        </CardTitle>
        <CardDescription>
          Calculate your potential returns with our savings plans
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Input Section */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="principal" className="flex items-center gap-1">
                <DollarSign className="h-4 w-4" />
                Initial Amount (₦)
              </Label>
              <Input
                id="principal"
                type="number"
                value={principal || ''}
                onChange={(e) => setPrincipal(Number(e.target.value))}
                min={1000}
                step={1000}
                placeholder="100,000"
                className="text-lg font-medium"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="rate" className="flex items-center gap-1">
                <Percent className="h-4 w-4" />
                Interest Rate (% per year)
              </Label>
              <Select value={rate.toString()} onValueChange={(value) => setRate(Number(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="8">8% - Basic Savings</SelectItem>
                  <SelectItem value="10">10% - Fixed Deposit</SelectItem>
                  <SelectItem value="12">12% - Premium Plan</SelectItem>
                  <SelectItem value="15">15% - VIP Plan</SelectItem>
                  <SelectItem value="18">18% - Group Savings</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="time" className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                Time Period (years)
              </Label>
              <Select value={time.toString()} onValueChange={(value) => setTime(Number(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0.25">3 months</SelectItem>
                  <SelectItem value="0.5">6 months</SelectItem>
                  <SelectItem value="1">1 year</SelectItem>
                  <SelectItem value="2">2 years</SelectItem>
                  <SelectItem value="3">3 years</SelectItem>
                  <SelectItem value="5">5 years</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="compound">Compounding Frequency</Label>
              <Select value={compound} onValueChange={(value) => setCompound(value as 'monthly' | 'quarterly' | 'annually')}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="quarterly">Quarterly</SelectItem>
                  <SelectItem value="annually">Annually</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Results Section */}
          <div className="space-y-4">
            {calculation && (
              <>
                <div className="p-4 rounded-lg bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-muted-foreground">Final Amount</span>
                      <span className="text-xl font-bold text-primary">
                        {formatCurrency(calculation.finalAmount)}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-muted-foreground">Total Interest</span>
                      <span className="text-lg font-semibold text-green-600">
                        {formatCurrency(calculation.totalInterest)}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-muted-foreground">ROI</span>
                      <Badge variant="secondary" className="bg-green-100 text-green-700">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        {getROIPercentage().toFixed(1)}%
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium text-sm">Growth Progress</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Principal</span>
                      <span>Target</span>
                    </div>
                    <Progress 
                      value={(calculation.totalInterest / calculation.finalAmount) * 100} 
                      className="h-3"
                    />
                    <div className="flex justify-between text-xs">
                      <span className="text-muted-foreground">
                        {formatCurrency(calculation.principal)}
                      </span>
                      <span className="text-primary font-medium">
                        {formatCurrency(calculation.finalAmount)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Monthly Breakdown Preview */}
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Monthly Growth (First 6 months)</h4>
                  <div className="space-y-1 max-h-32 overflow-y-auto">
                    {calculation.monthlyBreakdown.slice(0, 6).map((month) => (
                      <div
                        key={month.month}
                        className="flex justify-between items-center text-xs p-2 rounded bg-muted/50"
                      >
                        <span className="text-muted-foreground">Month {month.month}</span>
                        <span className="font-medium">
                          {formatCurrency(month.total)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};