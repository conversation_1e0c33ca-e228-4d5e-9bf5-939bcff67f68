import React from "react";
import { FintechCard } from "@/components/ui/fintech-card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Eye, EyeOff, Plus, Send, ArrowDownLeft } from "lucide-react";

interface WalletOverviewProps {
  balance: number;
  currency?: string;
  showBalance?: boolean;
  onToggleBalance?: () => void;
  onAddFunds?: () => void;
  onSendMoney?: () => void;
  onWithdraw?: () => void;
}

export function WalletOverview({
  balance,
  currency = "₦",
  showBalance = true,
  onToggleBalance,
  onAddFunds,
  onSendMoney,
  onWithdraw,
}: WalletOverviewProps) {
  const formatBalance = (amount: number) => {
    return new Intl.NumberFormat("en-NG", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  return (
    <FintechCard variant="gradient" className="p-4 sm:p-6 text-white bg-gradient-to-br from-primary to-primary/80 dark:from-primary dark:to-primary/80 light:from-green-600 light:to-green-700 rounded-none shadow-[inset_2px_2px_4px_rgba(255,255,255,0.2),inset_-2px_-2px_4px_rgba(0,0,0,0.3)]">
      <div className="flex flex-col sm:flex-row justify-between items-start mb-4 sm:mb-6 gap-4 sm:gap-0">
        <div className="w-full sm:w-auto">
          <p className="text-white/80 text-sm mb-1">Total Balance</p>
          <div className="flex items-center gap-2 sm:gap-3">
            <h2 className="text-2xl sm:text-3xl font-bold break-all">
              {showBalance ? `${currency}${formatBalance(balance)}` : "••••••"}
            </h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={onToggleBalance}
              className="text-white hover:bg-white/20 h-8 w-8 flex-shrink-0 rounded-none shadow-[inset_2px_2px_4px_rgba(255,255,255,0.2),inset_-2px_-2px_4px_rgba(0,0,0,0.3)] hover:shadow-[inset_4px_4px_8px_rgba(255,255,255,0.1),inset_-4px_-4px_8px_rgba(0,0,0,0.4)] active:shadow-[inset_6px_6px_12px_rgba(0,0,0,0.5),inset_-6px_-6px_12px_rgba(255,255,255,0.05)]"
            >
              {showBalance ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>
          </div>
        </div>
        <div className="bg-white/20 rounded-none p-2 shadow-[inset_2px_2px_4px_rgba(255,255,255,0.2),inset_-2px_-2px_4px_rgba(0,0,0,0.3)] self-end sm:self-start">
          <div className="w-8 h-8 bg-white/30 rounded-none flex items-center justify-center shadow-[inset_1px_1px_2px_rgba(255,255,255,0.3),inset_-1px_-1px_2px_rgba(0,0,0,0.2)]">
            <span className="text-xs font-bold">KP</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
        <Button
          onClick={onAddFunds}
          className="bg-white/20 hover:bg-white/30 text-white border-0 h-12 flex-col gap-1 rounded-none shadow-[inset_2px_2px_4px_rgba(255,255,255,0.2),inset_-2px_-2px_4px_rgba(0,0,0,0.3)] hover:shadow-[inset_4px_4px_8px_rgba(255,255,255,0.1),inset_-4px_-4px_8px_rgba(0,0,0,0.4)] active:shadow-[inset_6px_6px_12px_rgba(0,0,0,0.5),inset_-6px_-6px_12px_rgba(255,255,255,0.05)] active:scale-[0.98]"
          variant="outline"
        >
          <Plus className="h-4 w-4" />
          <span className="text-xs">Add Funds</span>
        </Button>
        
        <Button
          onClick={onSendMoney}
          className="bg-white/20 hover:bg-white/30 text-white border-0 h-12 flex-col gap-1 rounded-none shadow-[inset_2px_2px_4px_rgba(255,255,255,0.2),inset_-2px_-2px_4px_rgba(0,0,0,0.3)] hover:shadow-[inset_4px_4px_8px_rgba(255,255,255,0.1),inset_-4px_-4px_8px_rgba(0,0,0,0.4)] active:shadow-[inset_6px_6px_12px_rgba(0,0,0,0.5),inset_-6px_-6px_12px_rgba(255,255,255,0.05)] active:scale-[0.98]"
          variant="outline"
        >
          <Send className="h-4 w-4" />
          <span className="text-xs">Send</span>
        </Button>
        
        <Button
          onClick={onWithdraw}
          className="bg-white/20 hover:bg-white/30 text-white border-0 h-12 flex-col gap-1 rounded-none shadow-[inset_2px_2px_4px_rgba(255,255,255,0.2),inset_-2px_-2px_4px_rgba(0,0,0,0.3)] hover:shadow-[inset_4px_4px_8px_rgba(255,255,255,0.1),inset_-4px_-4px_8px_rgba(0,0,0,0.4)] active:shadow-[inset_6px_6px_12px_rgba(0,0,0,0.5),inset_-6px_-6px_12px_rgba(255,255,255,0.05)] active:scale-[0.98]"
          variant="outline"
        >
          <ArrowDownLeft className="h-4 w-4" />
          <span className="text-xs">Withdraw</span>
        </Button>
      </div>
    </FintechCard>
  );
}