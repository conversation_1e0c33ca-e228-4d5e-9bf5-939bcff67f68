// MongoDB Collections
export const COLLECTIONS = {
  GROUP_SAVINGS: 'group_savings_plans',
  GROUP_MEMBERS: 'group_members',
  GROUP_CONTRIBUTIONS: 'group_contributions',
  GROUP_INVITES: 'group_invites'
};

// Types
export interface GroupSavingsPlan {
  _id?: string;
  name: string;
  description: string;
  category: string;
  targetAmount: number;
  contributionAmount: number;
  startDate: Date;
  endDate: Date;
  status: 'active' | 'pending' | 'completed' | 'cancelled';
  maxMembers: number;
  currentMembers: number;
  createdBy: string; // user ID
  inviteCode: string;
  rules: {
    minContribution: number;
    maxContribution: number;
    frequency: 'daily' | 'weekly' | 'monthly';
    penaltyRate: number;
  };
  metadata: {
    totalCollected: number;
    nextContributionDate: Date;
    completionPercentage: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface GroupMember {
  _id?: string;
  groupId: string;
  userId: string;
  userName: string;
  userEmail: string;
  role: 'admin' | 'member' | 'coordinator';
  contributionAmount: number;
  totalContributed: number;
  joinedAt: Date;
  status: 'active' | 'inactive' | 'suspended';
  lastContribution: Date;
  paymentMethod?: string;
}

export interface GroupContribution {
  _id?: string;
  groupId: string;
  memberId: string;
  amount: number;
  type: 'regular' | 'penalty' | 'bonus' | 'withdrawal';
  status: 'pending' | 'completed' | 'failed';
  paymentMethod: string;
  transactionRef: string;
  createdAt: Date;
  processedAt?: Date;
}

export interface GroupInvite {
  _id?: string;
  groupId: string;
  inviteCode: string;
  invitedBy: string;
  email?: string;
  phone?: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  expiresAt: Date;
  createdAt: Date;
}

// Group Savings Service
export class GroupSavingsService {
  private mongodb: any;
  
  constructor(mongoClient?: any) {
    this.mongodb = mongoClient;
  }

  setMongoClient(mongoClient: any) {
    this.mongodb = mongoClient;
  }

  // Generate unique invite code
  generateInviteCode(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }

  // Create new group savings plan
  async createGroup(planData: Omit<GroupSavingsPlan, '_id' | 'inviteCode' | 'currentMembers' | 'metadata' | 'createdAt' | 'updatedAt'>): Promise<GroupSavingsPlan> {
    const inviteCode = this.generateInviteCode();
    
    const newPlan: Omit<GroupSavingsPlan, '_id'> = {
      ...planData,
      inviteCode,
      currentMembers: 1, // Creator is first member
      metadata: {
        totalCollected: 0,
        nextContributionDate: new Date(planData.startDate),
        completionPercentage: 0
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await this.mongodb.insertOne({
      collection: COLLECTIONS.GROUP_SAVINGS,
      document: newPlan
    });

    if (!result.data) {
      throw new Error('Failed to create group savings plan');
    }

    // Add creator as admin member
    await this.addMember({
      groupId: result.data.insertedId,
      userId: planData.createdBy,
      userName: 'Group Creator',
      userEmail: '',
      role: 'admin',
      contributionAmount: planData.contributionAmount,
      totalContributed: 0,
      joinedAt: new Date(),
      status: 'active',
      lastContribution: new Date()
    });

    return { ...newPlan, _id: result.data.insertedId };
  }

  // Get all groups
  async getGroups(filters?: { 
    status?: string; 
    category?: string; 
    createdBy?: string;
    search?: string;
  }): Promise<GroupSavingsPlan[]> {
    const mongoFilters: any = {};

    if (filters?.status) mongoFilters.status = filters.status;
    if (filters?.category) mongoFilters.category = filters.category;
    if (filters?.createdBy) mongoFilters.createdBy = filters.createdBy;
    if (filters?.search) {
      mongoFilters.$or = [
        { name: { $regex: filters.search, $options: 'i' } },
        { description: { $regex: filters.search, $options: 'i' } }
      ];
    }

    const result = await this.mongodb.find({
      collection: COLLECTIONS.GROUP_SAVINGS,
      filter: mongoFilters,
      sort: { createdAt: -1 }
    });

    return result.data || [];
  }

  // Join group with invite code
  async joinGroupByCode(inviteCode: string, userId: string, userName: string, userEmail: string): Promise<boolean> {
    // Find group by invite code
    const groupResult = await this.mongodb.findOne({
      collection: COLLECTIONS.GROUP_SAVINGS,
      filter: { inviteCode, status: 'active' }
    });

    if (!groupResult.data) {
      throw new Error('Invalid invite code or group not found');
    }

    const group = groupResult.data;

    // Check if group is full
    if (group.currentMembers >= group.maxMembers) {
      throw new Error('Group is full');
    }

    // Check if user is already a member
    const existingMember = await this.mongodb.findOne({
      collection: COLLECTIONS.GROUP_MEMBERS,
      filter: { groupId: group._id, userId }
    });

    if (existingMember.data) {
      throw new Error('User is already a member of this group');
    }

    // Add member
    await this.addMember({
      groupId: group._id!,
      userId,
      userName,
      userEmail,
      role: 'member',
      contributionAmount: group.contributionAmount,
      totalContributed: 0,
      joinedAt: new Date(),
      status: 'active',
      lastContribution: new Date()
    });

    // Update group member count
    await this.mongodb.updateOne({
      collection: COLLECTIONS.GROUP_SAVINGS,
      filter: { _id: group._id },
      update: { 
        $inc: { currentMembers: 1 },
        $set: { updatedAt: new Date() }
      }
    });

    return true;
  }

  // Add member to group
  async addMember(memberData: Omit<GroupMember, '_id'>): Promise<GroupMember> {
    const result = await this.mongodb.insertOne({
      collection: COLLECTIONS.GROUP_MEMBERS,
      document: memberData
    });

    if (!result.data) {
      throw new Error('Failed to add member to group');
    }

    return { ...memberData, _id: result.data.insertedId };
  }

  // Get group members
  async getGroupMembers(groupId: string): Promise<GroupMember[]> {
    const result = await this.mongodb.find({
      collection: COLLECTIONS.GROUP_MEMBERS,
      filter: { groupId },
      sort: { joinedAt: 1 }
    });

    return result.data || [];
  }

  // Record contribution
  async recordContribution(contributionData: Omit<GroupContribution, '_id'>): Promise<GroupContribution> {
    const result = await this.mongodb.insertOne({
      collection: COLLECTIONS.GROUP_CONTRIBUTIONS,
      document: {
        ...contributionData,
        createdAt: new Date()
      }
    });

    if (!result.data) {
      throw new Error('Failed to record contribution');
    }

    // Update member's total contribution
    await this.mongodb.updateOne({
      collection: COLLECTIONS.GROUP_MEMBERS,
      filter: { _id: contributionData.memberId },
      update: {
        $inc: { totalContributed: contributionData.amount },
        $set: { lastContribution: new Date() }
      }
    });

    // Update group's total collected
    await this.updateGroupMetadata(contributionData.groupId);

    return { ...contributionData, _id: result.data.insertedId, createdAt: new Date() };
  }

  // Update group metadata
  async updateGroupMetadata(groupId: string): Promise<void> {
    // Calculate total collected
    const contributionsResult = await this.mongodb.aggregate(
      COLLECTIONS.GROUP_CONTRIBUTIONS,
      [
        { $match: { groupId, status: 'completed' } },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]
    );

    const totalCollected = contributionsResult.data && contributionsResult.data[0] 
      ? contributionsResult.data[0].total 
      : 0;

    // Get group target amount
    const groupResult = await this.mongodb.findOne({
      collection: COLLECTIONS.GROUP_SAVINGS,
      filter: { _id: groupId }
    });

    if (groupResult.data) {
      const completionPercentage = (totalCollected / groupResult.data.targetAmount) * 100;

      await this.mongodb.updateOne({
        collection: COLLECTIONS.GROUP_SAVINGS,
        filter: { _id: groupId },
        update: {
          $set: {
            'metadata.totalCollected': totalCollected,
            'metadata.completionPercentage': Math.min(completionPercentage, 100),
            updatedAt: new Date()
          }
        }
      });
    }
  }

  // Get group contributions
  async getGroupContributions(groupId: string, limit?: number): Promise<GroupContribution[]> {
    const result = await this.mongodb.find({
      collection: COLLECTIONS.GROUP_CONTRIBUTIONS,
      filter: { groupId },
      sort: { createdAt: -1 },
      limit: limit || 50
    });

    return result.data || [];
  }

  // Remove member from group
  async removeMember(groupId: string, memberId: string): Promise<boolean> {
    const result = await this.mongodb.deleteOne({
      collection: COLLECTIONS.GROUP_MEMBERS,
      filter: { _id: memberId, groupId }
    });

    if (result.data) {
      // Update group member count
      await this.mongodb.updateOne({
        collection: COLLECTIONS.GROUP_SAVINGS,
        filter: { _id: groupId },
        update: { 
          $inc: { currentMembers: -1 },
          $set: { updatedAt: new Date() }
        }
      });
    }

    return !!result.data;
  }

  // Update group status
  async updateGroupStatus(groupId: string, status: GroupSavingsPlan['status']): Promise<boolean> {
    const result = await this.mongodb.updateOne({
      collection: COLLECTIONS.GROUP_SAVINGS,
      filter: { _id: groupId },
      update: {
        $set: {
          status,
          updatedAt: new Date()
        }
      }
    });

    return !!result.data;
  }

  // Get user's groups
  async getUserGroups(userId: string): Promise<GroupSavingsPlan[]> {
    // Get user's group memberships
    const membershipsResult = await this.mongodb.find({
      collection: COLLECTIONS.GROUP_MEMBERS,
      filter: { userId, status: 'active' }
    });

    if (!membershipsResult.data || !membershipsResult.data.length) {
      return [];
    }

    const groupIds = membershipsResult.data.map(m => m.groupId);

    // Get group details
    const groupsResult = await this.mongodb.find({
      collection: COLLECTIONS.GROUP_SAVINGS,
      filter: { _id: { $in: groupIds } },
      sort: { createdAt: -1 }
    });

    return groupsResult.data || [];
  }
}

export const groupSavingsService = new GroupSavingsService();