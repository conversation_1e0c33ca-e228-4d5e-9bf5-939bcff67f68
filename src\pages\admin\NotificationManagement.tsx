
import React, { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { NotificationChannel, NotificationType, useNotifications } from "@/hooks/use-notifications";
import { useToast } from "@/hooks/use-toast";
import {
    AlertCircle,
    AlertTriangle,
    Bell,
    CheckCheck,
    CheckCircle,
    Info,
    RefreshCw,
    Send,
    Settings,
    Trash2,
    User,
    Users
} from "lucide-react";

const NotificationManagement = () => {
  const { toast } = useToast();
  const { 
    notifications, 
    addNotification, 
    markAllAsRead, 
    clearNotifications, 
    fetchNotifications 
  } = useNotifications();
  
  const [selectedUser, setSelectedUser] = useState<string>("");
  const [title, setTitle] = useState<string>("Important Notification");
  const [message, setMessage] = useState<string>("This is an important notification message.");
  const [notificationType, setNotificationType] = useState<NotificationType>("info");
  const [channel, setChannel] = useState<NotificationChannel>("in_app");
  const [priority, setPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [activeTab, setActiveTab] = useState("send");
  const [loading, setLoading] = useState(false);
  
  // Fetch users from API
  const [users, setUsers] = useState<any[]>([]);
  const [usersLoading, setUsersLoading] = useState(true);

  useEffect(() => {
    const fetchUsers = async () => {
      setUsersLoading(true);
      try {
        const response = await fetch('/api/v1/admin/users', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to fetch users');
        }

        const data = await response.json();
        setUsers(data.data || []);
      } catch (error) {
        console.error('Error fetching users:', error);
        setUsers([]);
      } finally {
        setUsersLoading(false);
      }
    };

    fetchUsers();
  }, []);

  const handleSendNotification = async () => {
    if (!selectedUser) {
      toast({
        title: "Error",
        description: "Please select a user to send the notification to",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    
    try {
      await addNotification({
        userId: selectedUser,
        title,
        message,
        type: notificationType,
        channel,
        priority,
      });
      
      toast({
        title: "Notification sent",
        description: "The notification has been sent successfully",
      });
      
      // Reset form
      setTitle("Important Notification");
      setMessage("This is an important notification message.");
      setNotificationType("info");
      setChannel("in_app");
      setPriority("medium");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send notification",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSendToAll = async () => {
    setLoading(true);
    let successCount = 0;
    
    try {
      // Send to all users
      for (const user of users) {
        await addNotification({
          userId: user.id,
          title,
          message,
          type: notificationType,
          channel,
          priority,
        });
        successCount++;
      }
      
      toast({
        title: "Bulk notification sent",
        description: `Successfully sent to ${successCount} users`,
      });
      
      // Reset form
      setTitle("Important Notification");
      setMessage("This is an important notification message.");
    } catch (error) {
      toast({
        title: "Error",
        description: `Sent to ${successCount} users, but some failed`,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const refreshNotifications = async () => {
    setLoading(true);
    await fetchNotifications();
    setLoading(false);
    toast({
      title: "Refreshed",
      description: "Notification list has been updated",
    });
  };

  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-amber-500" />;
      case 'info':
      default:
        return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  const getTypeColor = (type: NotificationType) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400';
      case 'error':
        return 'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-400';
      case 'warning':
        return 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400';
      case 'info':
      default:
        return 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Notification Management</h1>
          <p className="text-muted-foreground">
            Send and manage notifications for your users
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={refreshNotifications} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={markAllAsRead} className="text-green-600 hover:text-green-700 hover:bg-green-50 dark:hover:bg-green-900/20">
            <CheckCheck className="h-4 w-4 mr-2" />
            Mark All Read
          </Button>
          <Button variant="outline" onClick={clearNotifications} className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20">
            <Trash2 className="h-4 w-4 mr-2" />
            Clear All
          </Button>
        </div>
      </div>

      <Tabs defaultValue="send" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 w-[400px]">
          <TabsTrigger value="send">
            <Send className="h-4 w-4 mr-2" />
            Send New
          </TabsTrigger>
          <TabsTrigger value="bulk">
            <Users className="h-4 w-4 mr-2" />
            Bulk Send
          </TabsTrigger>
          <TabsTrigger value="manage">
            <Settings className="h-4 w-4 mr-2" />
            Manage
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="send" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Send Notification</CardTitle>
              <CardDescription>
                Send a notification to a specific user
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="user">Select User</Label>
                <Select
                  value={selectedUser}
                  onValueChange={setSelectedUser}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a user" />
                  </SelectTrigger>
                  <SelectContent>
                    {users.map(user => (
                      <SelectItem key={user.id} value={user.id}>
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-2" />
                          {user.name} ({user.email})
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="title">Notification Title</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter notification title"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="message">Notification Message</Label>
                <Textarea
                  id="message"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Enter notification message"
                  className="min-h-[100px]"
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="type">Notification Type</Label>
                  <Select
                    value={notificationType}
                    onValueChange={(value) => setNotificationType(value as NotificationType)}
                  >
                    <SelectTrigger id="type">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="info">Information</SelectItem>
                      <SelectItem value="success">Success</SelectItem>
                      <SelectItem value="warning">Warning</SelectItem>
                      <SelectItem value="error">Error</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="channel">Delivery Channel</Label>
                  <Select
                    value={channel}
                    onValueChange={(value) => setChannel(value as NotificationChannel)}
                  >
                    <SelectTrigger id="channel">
                      <SelectValue placeholder="Select channel" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="in_app">In-App Only</SelectItem>
                      <SelectItem value="email">Email Only</SelectItem>
                      <SelectItem value="sms">SMS Only</SelectItem>
                      <SelectItem value="all">All Channels</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Priority</Label>
                  <RadioGroup 
                    value={priority}
                    onValueChange={(value) => setPriority(value as 'low' | 'medium' | 'high')}
                    className="flex space-x-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="low" id="low" />
                      <Label htmlFor="low" className="text-green-600">Low</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="medium" id="medium" />
                      <Label htmlFor="medium" className="text-amber-600">Medium</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="high" id="high" />
                      <Label htmlFor="high" className="text-red-600">High</Label>
                    </div>
                  </RadioGroup>
                </div>
              </div>
              
              <Button 
                className="w-full"
                onClick={handleSendNotification}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Send Notification
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="bulk" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bulk Send Notifications</CardTitle>
              <CardDescription>
                Send a notification to all users
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="bulk-title">Notification Title</Label>
                <Input
                  id="bulk-title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter notification title"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="bulk-message">Notification Message</Label>
                <Textarea
                  id="bulk-message"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Enter notification message"
                  className="min-h-[100px]"
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="bulk-type">Notification Type</Label>
                  <Select
                    value={notificationType}
                    onValueChange={(value) => setNotificationType(value as NotificationType)}
                  >
                    <SelectTrigger id="bulk-type">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="info">Information</SelectItem>
                      <SelectItem value="success">Success</SelectItem>
                      <SelectItem value="warning">Warning</SelectItem>
                      <SelectItem value="error">Error</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="bulk-channel">Delivery Channel</Label>
                  <Select
                    value={channel}
                    onValueChange={(value) => setChannel(value as NotificationChannel)}
                  >
                    <SelectTrigger id="bulk-channel">
                      <SelectValue placeholder="Select channel" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="in_app">In-App Only</SelectItem>
                      <SelectItem value="email">Email Only</SelectItem>
                      <SelectItem value="sms">SMS Only</SelectItem>
                      <SelectItem value="all">All Channels</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Priority</Label>
                  <RadioGroup 
                    value={priority}
                    onValueChange={(value) => setPriority(value as 'low' | 'medium' | 'high')}
                    className="flex space-x-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="low" id="bulk-low" />
                      <Label htmlFor="bulk-low" className="text-green-600">Low</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="medium" id="bulk-medium" />
                      <Label htmlFor="bulk-medium" className="text-amber-600">Medium</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="high" id="bulk-high" />
                      <Label htmlFor="bulk-high" className="text-red-600">High</Label>
                    </div>
                  </RadioGroup>
                </div>
              </div>
              
              <div className="bg-amber-50 p-4 rounded-lg border border-amber-200 text-amber-700 dark:bg-amber-900/20 dark:border-amber-800 dark:text-amber-400">
                <div className="flex items-start">
                  <AlertTriangle className="h-5 w-5 mr-2 mt-0.5" />
                  <div>
                    <h4 className="font-medium">Warning: Bulk Operation</h4>
                    <p className="text-sm">
                      This will send notifications to all {users.length} users in the system. Please use this feature carefully.
                    </p>
                  </div>
                </div>
              </div>
              
              <Button 
                variant="default"
                className="w-full bg-amber-600 hover:bg-amber-700 text-white"
                onClick={handleSendToAll}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Sending to all users...
                  </>
                ) : (
                  <>
                    <Users className="h-4 w-4 mr-2" />
                    Send to All Users
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="manage">
          <Card>
            <CardHeader>
              <CardTitle>Manage Notifications</CardTitle>
              <CardDescription>
                View and manage all notifications in the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[500px] w-full rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]">Type</TableHead>
                      <TableHead className="w-[200px]">Title</TableHead>
                      <TableHead>Message</TableHead>
                      <TableHead className="w-[100px]">Channel</TableHead>
                      <TableHead className="w-[100px]">Priority</TableHead>
                      <TableHead className="w-[150px]">Timestamp</TableHead>
                      <TableHead className="w-[100px]">Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {notifications.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-12">
                          <Bell className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
                          <p className="text-muted-foreground">No notifications found</p>
                        </TableCell>
                      </TableRow>
                    ) : (
                      notifications.map((notification) => (
                        <TableRow key={notification.id}>
                          <TableCell>
                            {getNotificationIcon(notification.type)}
                          </TableCell>
                          <TableCell className="font-medium">{notification.title}</TableCell>
                          <TableCell className="text-sm max-w-[300px] truncate">
                            {notification.message}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {notification.channel}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className={
                              notification.priority === 'high' ? 'text-red-600 border-red-200' :
                              notification.priority === 'medium' ? 'text-amber-600 border-amber-200' :
                              'text-green-600 border-green-200'
                            }>
                              {notification.priority}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-xs">
                            {notification.createdAt.toLocaleString()}
                          </TableCell>
                          <TableCell>
                            <Badge variant={notification.read ? "outline" : "default"} className={
                              notification.read ? 
                                "bg-gray-100 text-gray-600 border-gray-200 dark:bg-gray-800 dark:text-gray-300" : 
                                `${getTypeColor(notification.type)}`
                            }>
                              {notification.read ? "Read" : "Unread"}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default NotificationManagement;
