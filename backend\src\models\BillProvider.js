const mongoose = require('mongoose');

const billProviderSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  category: {
    type: String,
    required: true,
    enum: ['airtime', 'data', 'electricity', 'cable_tv', 'internet', 'flight', 'betting', 'education']
  },
  provider: {
    type: String,
    required: true // e.g., 'paystack', 'flutterwave', 'vtpass', 'interswitch', 'remita'
  },
  apiKey: {
    type: String,
    required: true
  },
  secretKey: {
    type: String,
    required: true
  },
  baseUrl: {
    type: String,
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  fee: {
    type: Number,
    default: 0
  },
  feeType: {
    type: String,
    enum: ['fixed', 'percentage'],
    default: 'fixed'
  },
  minAmount: {
    type: Number,
    default: 0
  },
  maxAmount: {
    type: Number,
    default: 1000000
  },
  configurations: {
    type: Map,
    of: String,
    default: {}
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('BillProvider', billProviderSchema);