import React, { useState, useEffect } from 'react';
import { He<PERSON><PERSON> } from 'react-helmet-async';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, Search, HelpCircle, MessageCircle, Phone, Mail, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { useNavigate } from 'react-router-dom';
import Header from '@/components/landing/Header';

const FAQ = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [openItems, setOpenItems] = useState<number[]>([]);
  const [filteredFAQs, setFilteredFAQs] = useState<any[]>([]);
  const navigate = useNavigate();

  const faqs = [
    {
      category: 'Getting Started',
      questions: [
        {
          question: 'How do I create an account with Better Interest?',
          answer: 'Creating an account is simple! Click "Get Started" on our homepage, provide your email, phone number, and create a secure password. You\'ll need to verify your email and phone number, then complete a quick KYC process by uploading a valid ID.'
        },
        {
          question: 'What documents do I need for KYC verification?',
          answer: 'You need a valid government-issued ID (National ID, Driver\'s License, or International Passport), a recent utility bill or bank statement for address verification, and a clear selfie for identity confirmation.'
        },
        {
          question: 'Is there a minimum amount to start saving?',
          answer: 'You can start saving with as little as ₦1,000! There\'s no maximum limit, and you can add money to your savings anytime.'
        }
      ]
    },
    {
      category: 'Savings & Investments',
      questions: [
        {
          question: 'How much interest can I earn on my savings?',
          answer: 'Interest rates vary by product: Target Savings (5-8% annually), Fixed Deposits (12-20% annually depending on duration), and Investment products (varies by market performance). All rates are competitive and transparent.'
        },
        {
          question: 'What is a Fixed Deposit and how does it work?',
          answer: 'A Fixed Deposit is a savings product where you lock your money for a specific period (30-365 days) and earn guaranteed returns. The longer the duration, the higher the interest rate. You cannot withdraw before maturity without penalties.'
        },
        {
          question: 'Can I withdraw my money anytime?',
          answer: 'For regular savings plans, yes! You can withdraw anytime. For Fixed Deposits, early withdrawal incurs a 25% penalty on accrued interest. Investment products have varying liquidity terms.'
        },
        {
          question: 'How is interest calculated and when is it paid?',
          answer: 'Interest is calculated daily and compounded. For savings plans, interest is added monthly. For Fixed Deposits, interest accrues daily and is paid at maturity. You can track your earnings in real-time on your dashboard.'
        }
      ]
    },
    {
      category: 'Security & Safety',
      questions: [
        {
          question: 'Is my money safe with Better Interest?',
          answer: 'Absolutely! Your funds are protected by bank-level security, 256-bit SSL encryption, and NDIC insurance up to ₦500,000 per account. We\'re also licensed and regulated by the CBN.'
        },
        {
          question: 'How do you protect my personal information?',
          answer: 'We use industry-standard encryption, secure servers, and strict data protection policies. Your information is never shared with third parties without your consent, and we comply with all Nigerian data protection laws.'
        },
        {
          question: 'What happens if Better Interest goes out of business?',
          answer: 'Your funds are held in trust accounts with partner banks and are protected by NDIC insurance. In the unlikely event of business closure, you would have full access to your funds through the insurance scheme.'
        }
      ]
    },
    {
      category: 'Payments & Transactions',
      questions: [
        {
          question: 'How can I fund my account?',
          answer: 'You can fund your account via bank transfer, debit card, USSD, or mobile banking. All funding methods are instant and secure. We support all major Nigerian banks.'
        },
        {
          question: 'Are there any fees for transactions?',
          answer: 'Account opening and maintenance are free. We charge small fees for some services: ₦10 for transfers below ₦5,000, ₦25 for bill payments, and ₦50 for card transactions. All fees are clearly displayed before confirmation.'
        },
        {
          question: 'How long do withdrawals take?',
          answer: 'Withdrawals to your bank account are processed instantly during banking hours (9 AM - 4 PM, Monday-Friday). Weekend and holiday withdrawals are processed the next business day.'
        }
      ]
    },
    {
      category: 'Group Savings',
      questions: [
        {
          question: 'How do Group Savings work?',
          answer: 'Group Savings allow you to save with friends, family, or colleagues towards a common goal. Create a group, invite members, set contribution amounts and frequency. Everyone can track progress and earn interest together.'
        },
        {
          question: 'Can I leave a group savings plan?',
          answer: 'Yes, you can leave a group at any time. Your contributed amount plus earned interest will be transferred to your individual account. Group admins can also remove members if needed.'
        }
      ]
    },
    {
      category: 'Technical Support',
      questions: [
        {
          question: 'I forgot my password. How do I reset it?',
          answer: 'Click "Forgot Password" on the login page, enter your email address, and we\'ll send you a secure reset link. Follow the instructions in the email to create a new password.'
        },
        {
          question: 'The app is not working properly. What should I do?',
          answer: 'First, try closing and reopening the app, or refreshing your browser. Check your internet connection. If problems persist, contact our support team via live chat, email, or phone.'
        },
        {
          question: 'How do I update my account information?',
          answer: 'Log into your account, go to Settings > Profile, and update your information. Some changes (like phone number or bank details) may require verification for security.'
        }
      ]
    }
  ];

  useEffect(() => {
    if (searchTerm) {
      const filtered = faqs.map(category => ({
        ...category,
        questions: category.questions.filter(
          q => 
            q.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
            q.answer.toLowerCase().includes(searchTerm.toLowerCase())
        )
      })).filter(category => category.questions.length > 0);
      setFilteredFAQs(filtered);
    } else {
      setFilteredFAQs(faqs);
    }
  }, [searchTerm]);

  const toggleItem = (categoryIndex: number, questionIndex: number) => {
    const itemId = categoryIndex * 1000 + questionIndex;
    setOpenItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const isOpen = (categoryIndex: number, questionIndex: number) => {
    const itemId = categoryIndex * 1000 + questionIndex;
    return openItems.includes(itemId);
  };

  return (
    <>
      <Helmet>
        <title>FAQ - Frequently Asked Questions | Better Interest</title>
        <meta name="description" content="Find answers to common questions about Better Interest savings platform. Learn about our services, security, fees, and how to get started." />
        <meta name="keywords" content="FAQ, help, support, Better Interest, savings, questions, answers" />
        <link rel="canonical" href="https://betterinterest.com/faq" />
      </Helmet>

      <div className="min-h-screen bg-background">
        <Header />
        
        {/* Notification Banner */}
        <div className="bg-primary text-primary-foreground py-3">
          <div className="container mx-auto px-4 text-center">
            <p className="text-sm">
              🎉 New Feature: AutoSave is now available! Save spare change automatically. 
              <a href="/features" className="underline ml-2">Learn more</a>
            </p>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-12"
          >
            <Button
              variant="ghost"
              onClick={() => navigate(-1)}
              className="mb-6"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Frequently Asked
              <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                {" "}Questions
              </span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Find answers to common questions about Better Interest. Can't find what you're looking for? 
              Contact our support team.
            </p>
          </motion.div>

          {/* Search */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="max-w-2xl mx-auto mb-12"
          >
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              <Input
                placeholder="Search for answers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 py-6 text-lg rounded-xl"
              />
            </div>
          </motion.div>

          {/* FAQ Content */}
          <div className="max-w-4xl mx-auto">
            {filteredFAQs.map((category, categoryIndex) => (
              <motion.div
                key={categoryIndex}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: categoryIndex * 0.1 }}
                className="mb-8"
              >
                <h2 className="text-2xl font-bold mb-6 text-primary">
                  {category.category}
                </h2>
                
                <div className="space-y-4">
                  {category.questions.map((faq, questionIndex) => (
                    <Card key={questionIndex} className="overflow-hidden">
                      <motion.button
                        onClick={() => toggleItem(categoryIndex, questionIndex)}
                        className="w-full p-6 text-left hover:bg-accent/50 transition-colors"
                        whileHover={{ scale: 1.01 }}
                        whileTap={{ scale: 0.99 }}
                      >
                        <div className="flex items-center justify-between">
                          <h3 className="text-lg font-semibold pr-4">
                            {faq.question}
                          </h3>
                          <motion.div
                            animate={{ rotate: isOpen(categoryIndex, questionIndex) ? 180 : 0 }}
                            transition={{ duration: 0.2 }}
                          >
                            <ChevronDown className="h-5 w-5 text-muted-foreground" />
                          </motion.div>
                        </div>
                      </motion.button>
                      
                      <AnimatePresence>
                        {isOpen(categoryIndex, questionIndex) && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: 'auto', opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3 }}
                          >
                            <CardContent className="pt-0 pb-6">
                              <p className="text-muted-foreground leading-relaxed">
                                {faq.answer}
                              </p>
                            </CardContent>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </Card>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>

          {/* Contact Support */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="max-w-4xl mx-auto mt-16"
          >
            <Card className="p-8 text-center bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10">
              <CardContent className="space-y-6">
                <HelpCircle className="h-12 w-12 text-primary mx-auto" />
                <h3 className="text-2xl font-bold">Still need help?</h3>
                <p className="text-muted-foreground">
                  Our support team is here to help you 24/7. Choose your preferred way to get in touch.
                </p>
                
                <div className="grid md:grid-cols-3 gap-4">
                  <Button variant="outline" className="p-6 h-auto flex-col space-y-2">
                    <MessageCircle className="h-6 w-6" />
                    <span>Live Chat</span>
                    <span className="text-xs text-muted-foreground">Usually responds in minutes</span>
                  </Button>
                  
                  <Button variant="outline" className="p-6 h-auto flex-col space-y-2">
                    <Mail className="h-6 w-6" />
                    <span>Email Support</span>
                    <span className="text-xs text-muted-foreground"><EMAIL></span>
                  </Button>
                  
                  <Button variant="outline" className="p-6 h-auto flex-col space-y-2">
                    <Phone className="h-6 w-6" />
                    <span>Phone Support</span>
                    <span className="text-xs text-muted-foreground">+234 800 BETTER (238837)</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </>
  );
};

export default FAQ;
