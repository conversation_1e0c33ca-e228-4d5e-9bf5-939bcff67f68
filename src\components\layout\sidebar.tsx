
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { MainNav } from "../main-nav";

interface SidebarProps {
  isAdmin?: boolean;
  className?: string;
  collapsed?: boolean;
}

export function Sidebar({ isAdmin = false, className, collapsed: externalCollapsed }: SidebarProps) {
  const [internalCollapsed, setInternalCollapsed] = useState(false);
  const isMobile = useIsMobile();
  
  const collapsed = externalCollapsed !== undefined ? externalCollapsed : internalCollapsed;
  
  useEffect(() => {
    if (externalCollapsed === undefined) {
      setInternalCollapsed(isMobile);
    }
  }, [isMobile, externalCollapsed]);

  return (
    <div
      className={cn(
        "flex flex-col bg-brand-blue border-r border-brand-yellow/30 h-screen sticky top-0 transition-all duration-300 shadow-lg",
        collapsed ? "w-16" : "w-64",
        className
      )}
    >
      <div className="flex h-14 items-center px-4 border-b border-brand-yellow/20">
        <Link
          to={isAdmin ? "/admin/dashboard" : "/dashboard"}
          className="flex items-center gap-1 font-bebas tracking-wide text-brand-yellow transition-all"
        >
          {!collapsed && (
            <div className="flex items-center gap-3">
              <div className="h-10 w-10">
                <img
                  src="/lovable-uploads/new-logo.svg"
                  alt="Better Interest Logo"
                  className="h-full w-full object-contain"
                />
              </div>
              <div className="flex flex-col">
                <span className="text-sm font-bold text-brand-yellow uppercase tracking-wider">
                  Better Interest
                </span>
                <span className="text-xs text-brand-yellow/80">
                  Financial Growth
                </span>
              </div>
            </div>
          )}
          {collapsed && (
            <div className="h-10 w-10 mx-auto">
              <img
                src="/lovable-uploads/new-logo.svg"
                alt="Better Interest Logo"
                className="h-full w-full object-contain"
              />
            </div>
          )}
        </Link>
      </div>
      <div className="flex-1 overflow-auto py-6 px-3">
        <div className="rounded-none bg-white/10 backdrop-blur-sm p-6 mb-4 border border-brand-yellow/10 shadow-[inset_2px_2px_4px_rgba(255,255,255,0.2),inset_-2px_-2px_4px_rgba(0,0,0,0.3)]">
          <MainNav isAdmin={isAdmin} className="space-y-2 font-bebas tracking-wide" collapsed={collapsed} />
        </div>
      </div>
      <div className="border-t border-brand-yellow/20 p-3">
        {externalCollapsed === undefined && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setInternalCollapsed(!internalCollapsed)}
            className="w-full justify-center bg-white/10 border-brand-yellow/30 text-brand-yellow hover:bg-brand-yellow hover:text-brand-blue transition-all font-bebas tracking-wide rounded-none"
          >
            {collapsed ? (
              <ChevronRight className="transition-colors" />
            ) : (
              <ChevronLeft className="transition-colors" />
            )}
          </Button>
        )}
      </div>
    </div>
  );
}
