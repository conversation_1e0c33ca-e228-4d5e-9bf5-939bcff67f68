const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');
const Card = require('../models/Card');
const User = require('../models/User');
const axios = require('axios');

const PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY;
const PAYSTACK_BASE_URL = 'https://api.paystack.co';

// Add new card
router.post('/add', auth, async (req, res) => {
  try {
    const { email, amount = 100 } = req.body; // ₦1.00 for verification
    const userId = req.user.id;

    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Initialize Paystack transaction for card authorization
    const paystackResponse = await axios.post(
      `${PAYSTACK_BASE_URL}/transaction/initialize`,
      {
        email: user.email,
        amount: amount * 100, // Convert to kobo
        currency: 'NGN',
        callback_url: `${process.env.FRONTEND_URL}/cards/verify`,
        metadata: {
          userId: userId,
          purpose: 'card_verification'
        }
      },
      {
        headers: {
          Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!paystackResponse.data.status) {
      return res.status(400).json({
        success: false,
        message: 'Failed to initialize card verification',
        error: paystackResponse.data.message
      });
    }

    res.json({
      success: true,
      message: 'Card verification initialized',
      data: {
        authorizationUrl: paystackResponse.data.data.authorization_url,
        reference: paystackResponse.data.data.reference,
        accessCode: paystackResponse.data.data.access_code
      }
    });
  } catch (error) {
    console.error('Error adding card:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add card',
      error: error.response?.data?.message || error.message
    });
  }
});

// Verify and save card after Paystack authorization
router.post('/verify', auth, async (req, res) => {
  try {
    const { reference } = req.body;
    const userId = req.user.id;

    // Verify transaction with Paystack
    const verificationResponse = await axios.get(
      `${PAYSTACK_BASE_URL}/transaction/verify/${reference}`,
      {
        headers: {
          Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`
        }
      }
    );

    const transaction = verificationResponse.data.data;

    if (!transaction.status || transaction.status !== 'success') {
      return res.status(400).json({
        success: false,
        message: 'Card verification failed'
      });
    }

    const authorization = transaction.authorization;

    // Check if card already exists
    const existingCard = await Card.findOne({
      authorizationCode: authorization.authorization_code
    });

    if (existingCard) {
      return res.status(400).json({
        success: false,
        message: 'Card already exists'
      });
    }

    // Create new card record
    const card = new Card({
      userId,
      authorizationCode: authorization.authorization_code,
      last4: authorization.last4,
      brand: authorization.card_type.toLowerCase(),
      bank: authorization.bank,
      expiryMonth: authorization.exp_month,
      expiryYear: authorization.exp_year,
      cardType: authorization.account_type || 'debit',
      isVerified: true,
      verificationReference: reference,
      verificationAmount: transaction.amount / 100, // Convert from kobo
      metadata: {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        addedVia: 'web'
      }
    });

    // If this is the user's first card, make it default
    const userCardCount = await Card.countDocuments({ userId, isActive: true });
    if (userCardCount === 0) {
      card.isDefault = true;
    }

    await card.save();

    // Reverse the verification charge
    try {
      await axios.post(
        `${PAYSTACK_BASE_URL}/refund`,
        {
          transaction: reference,
          amount: transaction.amount
        },
        {
          headers: {
            Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
            'Content-Type': 'application/json'
          }
        }
      );
    } catch (refundError) {
      console.error('Failed to reverse verification charge:', refundError);
      // Don't fail the card addition if refund fails
    }

    res.json({
      success: true,
      message: 'Card added and verified successfully',
      data: card
    });
  } catch (error) {
    console.error('Error verifying card:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify card',
      error: error.response?.data?.message || error.message
    });
  }
});

// Get user's cards
router.get('/user', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { includeInactive = false } = req.query;

    const cards = await Card.getUserCards(userId, !includeInactive);

    res.json({
      success: true,
      data: cards
    });
  } catch (error) {
    console.error('Error fetching user cards:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch cards'
    });
  }
});

// Set default card
router.put('/:id/set-default', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const card = await Card.findOne({ _id: id, userId });

    if (!card) {
      return res.status(404).json({
        success: false,
        message: 'Card not found'
      });
    }

    if (!card.isActive || card.security.isBlocked) {
      return res.status(400).json({
        success: false,
        message: 'Cannot set inactive or blocked card as default'
      });
    }

    await card.setAsDefault();

    res.json({
      success: true,
      message: 'Default card updated successfully',
      data: card
    });
  } catch (error) {
    console.error('Error setting default card:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to set default card'
    });
  }
});

// Remove card
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const card = await Card.findOne({ _id: id, userId });

    if (!card) {
      return res.status(404).json({
        success: false,
        message: 'Card not found'
      });
    }

    // Deactivate instead of deleting for audit purposes
    card.isActive = false;
    await card.save();

    // If this was the default card, set another card as default
    if (card.isDefault) {
      const nextCard = await Card.findOne({
        userId,
        isActive: true,
        'security.isBlocked': false,
        _id: { $ne: id }
      });

      if (nextCard) {
        await nextCard.setAsDefault();
      }
    }

    res.json({
      success: true,
      message: 'Card removed successfully'
    });
  } catch (error) {
    console.error('Error removing card:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove card'
    });
  }
});

// Charge card
router.post('/charge', auth, async (req, res) => {
  try {
    const { cardId, amount, email, metadata = {} } = req.body;
    const userId = req.user.id;

    // Get card details
    const card = await Card.findOne({ _id: cardId, userId });

    if (!card) {
      return res.status(404).json({
        success: false,
        message: 'Card not found'
      });
    }

    if (!card.isActive || card.security.isBlocked) {
      return res.status(400).json({
        success: false,
        message: 'Card is not available for transactions'
      });
    }

    // Charge card via Paystack
    const chargeResponse = await axios.post(
      `${PAYSTACK_BASE_URL}/transaction/charge_authorization`,
      {
        authorization_code: card.authorizationCode,
        email,
        amount: amount * 100, // Convert to kobo
        currency: 'NGN',
        metadata: {
          ...metadata,
          userId,
          cardId: card._id
        }
      },
      {
        headers: {
          Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const transaction = chargeResponse.data.data;
    const success = transaction.status === 'success';

    // Record transaction on card
    await card.recordTransaction(amount, success);

    if (!success) {
      return res.status(400).json({
        success: false,
        message: 'Transaction failed',
        data: transaction
      });
    }

    res.json({
      success: true,
      message: 'Transaction successful',
      data: {
        reference: transaction.reference,
        amount: transaction.amount / 100,
        status: transaction.status,
        paidAt: transaction.paid_at
      }
    });
  } catch (error) {
    console.error('Error charging card:', error);
    
    // Record failed transaction
    if (req.body.cardId) {
      try {
        const card = await Card.findById(req.body.cardId);
        if (card) {
          await card.recordTransaction(req.body.amount || 0, false);
        }
      } catch (recordError) {
        console.error('Error recording failed transaction:', recordError);
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to process payment',
      error: error.response?.data?.message || error.message
    });
  }
});

// Get card details
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const card = await Card.findOne({ _id: id, userId });

    if (!card) {
      return res.status(404).json({
        success: false,
        message: 'Card not found'
      });
    }

    res.json({
      success: true,
      data: card
    });
  } catch (error) {
    console.error('Error fetching card details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch card details'
    });
  }
});

module.exports = router;
