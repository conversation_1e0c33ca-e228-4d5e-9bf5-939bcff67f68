import { API_CONFIG } from '@/config/api';

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: string[];
}

class ApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;

  constructor() {
    this.baseURL = API_CONFIG.baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }

  private getAuthHeaders(): Record<string, string> {
    const token = localStorage.getItem('token');
    return token ? { 'Authorization': `Bearer ${token}` } : {};
  }

  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    const contentType = response.headers.get('content-type');
    const isJson = contentType?.includes('application/json');
    
    let data: any;
    try {
      data = isJson ? await response.json() : await response.text();
    } catch (error) {
      throw new Error('Failed to parse response');
    }

    if (!response.ok) {
      throw new Error(data.message || data.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return data;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    const headers = {
      ...this.defaultHeaders,
      ...this.getAuthHeaders(),
      ...options.headers,
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers,
        timeout: API_CONFIG.timeout,
      });

      return await this.handleResponse<T>(response);
    } catch (error) {
      console.error(`API Error [${options.method || 'GET'}] ${endpoint}:`, error);
      throw error;
    }
  }

  // HTTP Methods
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    const url = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint;
    return this.request<T>(url, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // File upload
  async upload<T>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }

    const headers = this.getAuthHeaders();
    // Don't set Content-Type for FormData, let browser set it with boundary

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'POST',
        headers,
        body: formData,
      });

      return await this.handleResponse<T>(response);
    } catch (error) {
      console.error(`Upload Error ${endpoint}:`, error);
      throw error;
    }
  }
}

// Specific API service classes
export class AuthAPI {
  constructor(private client: ApiClient) {}

  async login(credentials: { email: string; password: string }) {
    return this.client.post('/auth/login', credentials);
  }

  async register(userData: any) {
    return this.client.post('/auth/register', userData);
  }

  async logout() {
    return this.client.post('/auth/logout');
  }

  async refreshToken() {
    return this.client.post('/auth/refresh');
  }

  async forgotPassword(email: string) {
    return this.client.post('/auth/forgot-password', { email });
  }

  async resetPassword(token: string, password: string) {
    return this.client.post('/auth/reset-password', { token, password });
  }
}

export class UserAPI {
  constructor(private client: ApiClient) {}

  async getProfile() {
    return this.client.get('/users/profile');
  }

  async updateProfile(data: any) {
    return this.client.put('/users/profile', data);
  }

  async uploadAvatar(file: File) {
    return this.client.upload('/users/avatar', file);
  }
}

export class SavingsAPI {
  constructor(private client: ApiClient) {}

  async getPlans() {
    return this.client.get('/savings/plans');
  }

  async createPlan(data: any) {
    return this.client.post('/savings/plans', data);
  }

  async updatePlan(id: string, data: any) {
    return this.client.put(`/savings/plans/${id}`, data);
  }

  async deletePlan(id: string) {
    return this.client.delete(`/savings/plans/${id}`);
  }

  async contribute(planId: string, amount: number) {
    return this.client.post(`/savings/plans/${planId}/contribute`, { amount });
  }
}

export class FixedDepositAPI {
  constructor(private client: ApiClient) {}

  async getRates() {
    return this.client.get('/fixed-deposits/rates');
  }

  async create(data: { amount: number; duration: number; autoRenewal?: boolean }) {
    return this.client.post('/fixed-deposits', data);
  }

  async getDeposits(status?: string) {
    return this.client.get('/fixed-deposits', status ? { status } : undefined);
  }

  async breakDeposit(id: string) {
    return this.client.post(`/fixed-deposits/${id}/break`);
  }

  async calculateInterest(amount: number, duration: number) {
    return this.client.post('/fixed-deposits/calculate-interest', { amount, duration });
  }
}

export class InvestmentAPI {
  constructor(private client: ApiClient) {}

  async getProducts() {
    return this.client.get('/investments/products');
  }

  async buy(productId: string, amount: number) {
    return this.client.post('/investments/buy', { productId, amount });
  }

  async sell(investmentId: string, amount?: number) {
    return this.client.post('/investments/sell', { investmentId, amount });
  }

  async getPortfolio() {
    return this.client.get('/investments/portfolio');
  }
}

export class BillsAPI {
  constructor(private client: ApiClient) {}

  async getProviders(type?: string) {
    return this.client.get('/bills/providers', type ? { type } : undefined);
  }

  async pay(data: { provider: string; amount: number; beneficiary: string; billType: string }) {
    return this.client.post('/bills/pay', data);
  }

  async getHistory() {
    return this.client.get('/bills/history');
  }
}

export class AnalyticsAPI {
  constructor(private client: ApiClient) {}

  async getUserDashboard(period = '30') {
    return this.client.get('/analytics/user-dashboard', { period });
  }

  async getAdminDashboard(period = '30') {
    return this.client.get('/analytics/admin-dashboard', { period });
  }

  async getFinancialReports(params: any) {
    return this.client.get('/analytics/financial-reports', params);
  }
}

// Create singleton instances
const apiClient = new ApiClient();

export const authAPI = new AuthAPI(apiClient);
export const userAPI = new UserAPI(apiClient);
export const savingsAPI = new SavingsAPI(apiClient);
export const fixedDepositAPI = new FixedDepositAPI(apiClient);
export const investmentAPI = new InvestmentAPI(apiClient);
export const billsAPI = new BillsAPI(apiClient);
export const analyticsAPI = new AnalyticsAPI(apiClient);

export default apiClient;
