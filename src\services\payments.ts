import { apiService } from './api';
import { PAYSTACK_CONFIG } from '@/config/api';

export interface PaymentMethod {
  id: string;
  type: 'card' | 'bank_transfer' | 'ussd';
  last4?: string;
  bank?: string;
  brand?: string;
  isDefault: boolean;
  userId: string;
  createdAt: string;
}

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'success' | 'failed';
  reference: string;
  authorizationUrl?: string;
  accessCode?: string;
  paymentMethod?: string;
  userId: string;
  createdAt: string;
}

export interface WithdrawalRequest {
  id: string;
  amount: number;
  accountNumber: string;
  bankCode: string;
  bankName: string;
  accountName: string;
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  reason?: string;
  userId: string;
  processedBy?: string;
  createdAt: string;
  updatedAt: string;
}

export class PaymentsService {
  // Payment Methods
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    try {
      return await apiService.get<PaymentMethod[]>('/payments/methods');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch payment methods');
    }
  }

  async addPaymentMethod(data: {
    type: 'card' | 'bank_transfer';
    cardNumber?: string;
    expiryMonth?: string;
    expiryYear?: string;
    cvv?: string;
    accountNumber?: string;
    bankCode?: string;
  }): Promise<PaymentMethod> {
    try {
      return await apiService.post<PaymentMethod>('/payments/methods', data);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to add payment method');
    }
  }

  async deletePaymentMethod(id: string): Promise<void> {
    try {
      await apiService.delete(`/payments/methods/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete payment method');
    }
  }

  async setDefaultPaymentMethod(id: string): Promise<void> {
    try {
      await apiService.patch(`/payments/methods/${id}/default`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to set default payment method');
    }
  }

  // Payments
  async initiatePayment(data: {
    amount: number;
    currency?: string;
    paymentMethodId?: string;
    metadata?: Record<string, any>;
  }): Promise<PaymentIntent> {
    try {
      return await apiService.post<PaymentIntent>('/payments/initiate', {
        ...data,
        currency: data.currency || 'NGN',
      });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to initiate payment');
    }
  }

  async verifyPayment(reference: string): Promise<PaymentIntent> {
    try {
      return await apiService.get<PaymentIntent>(`/payments/verify/${reference}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Payment verification failed');
    }
  }

  async getPaymentHistory(limit?: number, offset?: number): Promise<PaymentIntent[]> {
    try {
      const params = new URLSearchParams();
      if (limit) params.append('limit', limit.toString());
      if (offset) params.append('offset', offset.toString());
      
      return await apiService.get<PaymentIntent[]>(`/payments/history?${params}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch payment history');
    }
  }

  // Withdrawals
  async createWithdrawalRequest(data: {
    amount: number;
    accountNumber: string;
    bankCode: string;
    accountName: string;
  }): Promise<WithdrawalRequest> {
    try {
      return await apiService.post<WithdrawalRequest>('/payments/withdrawals', data);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Withdrawal request failed');
    }
  }

  async getWithdrawalRequests(): Promise<WithdrawalRequest[]> {
    try {
      return await apiService.get<WithdrawalRequest[]>('/payments/withdrawals');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch withdrawal requests');
    }
  }

  async cancelWithdrawalRequest(id: string): Promise<void> {
    try {
      await apiService.patch(`/payments/withdrawals/${id}/cancel`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to cancel withdrawal request');
    }
  }

  // Bank utilities
  async getBanks(): Promise<{ code: string; name: string }[]> {
    try {
      return await apiService.get('/payments/banks');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch banks');
    }
  }

  async verifyAccountNumber(accountNumber: string, bankCode: string): Promise<{
    accountNumber: string;
    accountName: string;
    bankCode: string;
  }> {
    try {
      return await apiService.post('/payments/verify-account', {
        accountNumber,
        bankCode,
      });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Account verification failed');
    }
  }

  // Paystack utilities
  getPaystackPublicKey(): string {
    return PAYSTACK_CONFIG.publicKey;
  }

  async initializePaystackPayment(amount: number, email: string, metadata?: Record<string, any>): Promise<{
    authorizationUrl: string;
    accessCode: string;
    reference: string;
  }> {
    try {
      return await apiService.post('/payments/paystack/initialize', {
        amount: amount * 100, // Convert to kobo
        email,
        metadata,
      });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Paystack initialization failed');
    }
  }
}

export const paymentsService = new PaymentsService();