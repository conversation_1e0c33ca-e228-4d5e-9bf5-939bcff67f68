import Header from '@/components/landing/Header';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { Target, ArrowRight, CheckCircle, Calendar, TrendingUp, Award } from 'lucide-react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';

const GoalSetting = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: Calendar,
      title: 'Timeline Planning',
      description: 'Set realistic timelines and track your progress towards each goal'
    },
    {
      icon: TrendingUp,
      title: 'Progress Tracking',
      description: 'Visual progress indicators and milestone celebrations'
    },
    {
      icon: Award,
      title: 'Achievement Rewards',
      description: 'Earn bonus interest and rewards when you reach your goals'
    }
  ];

  return (
    <>
      <Helmet>
        <title>Goal Setting | Better Interest</title>
        <meta name="description" content="Set and achieve your financial goals with Better Interest's intelligent goal-setting tools." />
      </Helmet>

      <div className="min-h-screen landing-bg text-white">
        <Header />
        
        {/* Hero Section */}
        <section className="relative pt-20 pb-16 overflow-hidden">
          <div className="container mx-auto px-4 relative z-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[70vh]">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="space-y-8"
              >
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-12 h-12 bg-[#39E59E] rounded-xl flex items-center justify-center">
                    <Target className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-[#39E59E] font-semibold">Goal Setting</span>
                </div>
                
                <h1 className="text-5xl md:text-6xl font-bold leading-tight">
                  <span className="text-white">Achieve Your</span>
                  <br />
                  <span className="text-[#39E59E]">Financial Dreams</span>
                </h1>
                
                <p className="text-xl text-gray-300 leading-relaxed max-w-lg">
                  Turn your financial aspirations into achievable goals with our intelligent 
                  planning tools and progress tracking system.
                </p>

                <div className="flex flex-col sm:flex-row gap-4">
                  <button 
                    className="btn-neumorphic-inward text-lg px-8 py-6 group bg-[#39E59E] hover:bg-[#2dd489]"
                    onClick={() => navigate('/signup')}
                  >
                    Start Planning
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                  </button>
                  
                  <Button 
                    variant="outline" 
                    size="lg" 
                    className="text-lg px-8 py-6 rounded-xl border-[#39E59E] text-[#39E59E] hover:bg-[#39E59E]/10"
                  >
                    Learn More
                  </Button>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="flex justify-center"
              >
                <img
                  src="/lovable-uploads/371e0f57-a280-42c0-8e1b-50aee7ebddc6.png"
                  alt="Goal Setting"
                  className="w-full max-w-md h-auto object-contain"
                />
              </motion.div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-24 relative">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="w-16 h-16 bg-[#39E59E]/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <feature.icon className="w-8 h-8 text-[#39E59E]" />
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-white">{feature.title}</h3>
                  <p className="text-gray-300 leading-relaxed">{feature.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default GoalSetting;
